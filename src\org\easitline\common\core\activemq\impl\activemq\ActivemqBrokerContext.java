package org.easitline.common.core.activemq.impl.activemq;

import java.io.Serializable;



import javax.jms.Connection;
import javax.jms.DeliveryMode;
import javax.jms.Destination;
import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageConsumer;
import javax.jms.MessageProducer;
import javax.jms.Session;

import org.apache.activemq.ActiveMQConnection;
import org.apache.activemq.ActiveMQConnectionFactory;
import org.easitline.common.core.activemq.Broker;
import org.easitline.common.core.activemq.BrokerContext;
import org.easitline.common.core.activemq.impl.proxy.ConsumerProxy;
import org.easitline.common.core.activemq.impl.proxy.ProducerProxy;
import org.easitline.common.core.activemq.log.ActivieMQLogger;
import org.easitline.common.utils.string.StringUtils;

/**
 * 消息队列上下文信息
 * 
 * <AUTHOR>
 * 
 */
public class ActivemqBrokerContext extends BrokerContext{

	/**
	 * EAP的消息队列工厂，JNDI名称
	 */
	private static String QUEUE_URL = "java:/activemq/QueueConnectionFactory";


	/**
	 * 消息的会话对象
	 */
	private Session session;

	/**
	 * 会话
	 */
	private Destination destination;

	/**
	 * 消息队列链接
	 */
	private Connection connection;


	/**
	 * 连接类型
	 */
	private int connectorType;

	/**
	 * 消息的生产者
	 */
	private ProducerProxy producer;

	/**
	 * 消息的消费者
	 */
	private ConsumerProxy consumer;
	
	
	private ActiveMessageProducer activeMessageProducer;
	
	private ActiveMessageConsumer activeMessageConsumer;


	/**
	 * 通过ConnectorURL方式获取代理上下文
	 * 
	 * @param connectorType
	 * @param connector
	 * @param brokerName
	 * @param username
	 * @param password
	 */
	public ActivemqBrokerContext(Integer connectorType, String connector,String brokerName, String username, String password) {
		super(connectorType,connector,brokerName,username,password);
		this.connectorType = connectorType;
		if(StringUtils.isBlank(this.connector)) this.connector = connector;
		this.brokerName = brokerName;
	}

	/**
	 * 
	 * @throws JMSException
	 */
	public void init()  {
		ActivieMQLogger.getLogger().info("<" + brokerName + "> BrokerContext.init()-->URL:"+ this.getConnectorInfo());
		try {
			initConnectionFactory();  //初始化链接工厂信息成功，则设置初始化为true;
			ActivieMQLogger.getLogger().info("<" + brokerName + "> BrokerContext.init() ok ! ");
		} catch (JMSException ex) {
			ActivieMQLogger.getLogger().error("<" + brokerName+ "> BrokerContext.init() error ,URL["+ getConnectorInfo() + "] cause:"+ ex.getMessage());
		}
	}

	/**
	 * 初始化链接工厂
	 * 
	 * @throws JMSException
	 */
	private void initConnectionFactory() throws JMSException {  
		
			if(session != null) {
				ActivieMQLogger.getLogger().info("<" + brokerName + "> 已经初始化，若需要重新连接，请自行reload的方法!");
				return;
			}
			ActivieMQLogger.getLogger().info("<" + brokerName + ">init Connection Factory use ConnectorURL, user: "+ this.getUsername()+ ",pass: "+ this.getPassword()+ ", connector:" + connector);

			ActiveMQConnectionFactory connectionFactory = new ActiveMQConnectionFactory(this.getUsername(),this.getPassword(),connector);
			connectionFactory.setUseAsyncSend(true);
			connectionFactory.setOptimizeAcknowledge(false);
			connectionFactory.setProducerWindowSize(1024*1024);
			connection = connectionFactory.createConnection();// 创建连接对象
			if(connection == null) throw new JMSException("创建ActiveMQ链接失败");
			((ActiveMQConnection)connection).setDispatchAsync(true);
			
			connection.start();
			session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE);// 获取session对象
			if(this.connectorType == Broker.CONNECTOR_TYPE_PRODUCER  || this.connectorType == Broker.CONNECTOR_TYPE_CONSUMER){
				destination = session.createQueue(brokerName+"?consumer.dispatchAsync=false&consumer.prefetchSize=100");// 通过session创建目标地址
			}else{
				destination = session.createTopic(brokerName);
			}
			if (this.connectorType == Broker.CONNECTOR_TYPE_PRODUCER ){
				activeMessageProducer = new ActiveMessageProducer(this,session.createProducer(destination));// 通过目标地址创建生产者对象
				activeMessageProducer.setTimeToLive(120*60*1000);
				activeMessageProducer.setDeliveryMode(DeliveryMode.PERSISTENT);//    设置发送模式为：可以持久化
		        if(this.producer == null){
		        	this.producer = new ProducerProxy(this,activeMessageProducer);
		        }else{
		        	this.producer.setProducer(activeMessageProducer);
		        }
			}else if( this.connectorType == Broker.CONNECTOR_TYPE_TOPIC_PRODUCER) {
				activeMessageProducer = new ActiveMessageProducer(this,session.createProducer(destination));// 通过目标地址创建生产者对象
				activeMessageProducer.setTimeToLive(600*1000);
				activeMessageProducer.setDeliveryMode(DeliveryMode.PERSISTENT);//    设置发送模式为：可以持久化
		        if(this.producer == null){
		        	this.producer = new ProducerProxy(this,activeMessageProducer);
		        }else{
		        	this.producer.setProducer(activeMessageProducer);
		        }
			} else {
				activeMessageConsumer = new ActiveMessageConsumer(this, session.createConsumer(destination)) ;
		        if(this.consumer == null){
		        	this.consumer = new ConsumerProxy(this,activeMessageConsumer);
		        }else{
		        	this.consumer.setConsumer(activeMessageConsumer);
		        }
			}
	}

	private String getConnectorInfo() {
		if (connector == null)
			return QUEUE_URL;
		else {
			return connector;
		}
	}

	/**
	 * 关闭JMS相关链接
	 * 
	 * @throws JMSException
	 */
	public void close() {
	}

	public int getConnectorType() {
		return this.connectorType;
	}

	/**
	 * 获得session对象
	 * 
	 * @return
	 */
	public MessageProducer getProducer() {
		return producer;
	}

	public MessageConsumer getConsumer() {
		return consumer;
	}

	public Session getSession() {
		return session;
	}

	public String getConnector() {
		return connector;
	}

	public String getBrokerName() {
		return brokerName;
	}

	@Override
	public Message createObjectMessage(Serializable messageObj) throws JMSException {
		return this.getSession().createObjectMessage(messageObj);
	}

	@Override
	public void reload() {
		ActivieMQLogger.getLogger().info("<" + brokerName + "> reload...");
		try {
			if (activeMessageProducer != null)
				activeMessageProducer.close();
		} catch (Exception ex) {
			ActivieMQLogger.getLogger()
					.error("<" + brokerName + "> BrokerContext.close(producer) error , cause:" + ex.getMessage());
		}

		try {
			if (activeMessageConsumer != null) {
				activeMessageConsumer.close();
			}
		} catch (Exception ex) {
			ActivieMQLogger.getLogger()
					.error("<" + brokerName + "> BrokerContext.close(consumer) error , cause:" + ex.getMessage());
		}

		try {
			if (session != null)
				session.close();
		} catch (Exception ex) {
			ActivieMQLogger.getLogger()
					.error("<" + brokerName + "> BrokerContext.close(session) error , cause:" + ex.getMessage());
		}

		try {
			if (connection != null) {
				connection.close();
			}
		} catch (Exception ex) {
			ActivieMQLogger.getLogger()
					.error("<" + brokerName + "> BrokerContext.close(connection) error , cause:" + ex.getMessage());
		}
		this.activeMessageProducer =  null;
		this.activeMessageConsumer =  null;
		this.session = null;
		this.connection = null;
		this.init();
	}
}
