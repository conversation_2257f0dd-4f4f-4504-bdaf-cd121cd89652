package org.easitline.common.core.context;

import java.sql.Connection;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import org.easitline.common.core.context.impl.TomcatAppContext;
import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.druid.pool.DruidDataSource;


public abstract class AppContext {
  
  private static Map<String,AppContext> appContexts = new ConcurrentHashMap<String,AppContext>(30);

  /**
   * 构造函数
   * @param appName String 应用程序名称
   */
  public AppContext(){
   
  }
  
  /**
   * 获得应用的上下文信息
   * @param appName 应用名称
   * @return
   */
  public static AppContext getContext(String appId){
	  return getContext(appId,false);
  }
  public static AppContext getContext(String appId,boolean reload){
	  appId = StringUtils.trimToEmpty(appId);
	  if(StringUtils.isBlank(appId)){
		  CoreLogger.getPlatform().error("AppContext.getContext()->AppId can't null!");
		  return null;
	  }
	  AppContext appContext = appContexts.get(appId);
	  
	  if(appContext == null){
		  appContext = new TomcatAppContext(appId);
		  appContexts.put(appId, appContext);
	  }else if(reload==true){
		  appContext.reload(appId); 
	  }
	  return appContext;
  }
  
  public static String[] getAppIds(){
	  return appContexts.keySet().toArray(new String[appContexts.size()]);
  }
  
  public static Map<String,String> getAppNames(){
	  Map<String,String> apps=new HashMap<String, String>();
	  Set<String> set=appContexts.keySet();
	  for(String appId:set){
		TomcatAppContext context=(TomcatAppContext)appContexts.get(appId);
		apps.put(appId,context.getAppName());
	  }
	  return apps;
  }
  
  
  public abstract void reload(String appId);
  
  /**
   * 获得应用的配置属性
   * @param key
   * @param defaultValue 如果key返回的值为空时，返回该缺省值。
   * @return
   */
  public abstract String getProperty(String key,String defaultValue);
  
  public abstract void setProperty(String key,String value);
  
  
  /**
   * 获取应用所有配置参数
   * @return
   */
  public abstract Map<String,String> getPropertys();
  
  /**
   * 获得应用数据源对应系统的数据源
   * @param appDatasourceName
   * @return
   */
  public abstract String getDatasourceName(String appDatasourceName);
  
  /**
        * 获取连接池对象
   * @param appDatasourceName
   * @return
   */
  public abstract DruidDataSource getDruidDataSource(String appDatasourceName);
  
  /**
   * 根据系统数据源，获取系统的链接
   * @param sysDatasourceName
   * @return
   */
  public abstract Connection getConnection(String appDatasourceName);
  

  /**
   * 操作日志
   * @param usercode  用户唯一标识
   * @param resourceId  资源ID
   * @param parameters  操作日志参数
   */
  public abstract void operatorLog(String usercode,String resourceId,Properties parameters);

}

