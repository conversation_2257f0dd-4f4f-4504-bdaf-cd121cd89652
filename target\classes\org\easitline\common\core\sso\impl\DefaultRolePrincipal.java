package org.easitline.common.core.sso.impl;

import org.easitline.common.core.sso.RolePrincipal;

public class DefaultRolePrincipal implements RolePrincipal {

	private String rolename = null;
	
	public DefaultRolePrincipal(String rolename){
		this.rolename = rolename;
	}
	
	@Override
	public String getName() {
		return rolename;
	}
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result
				+ ((rolename == null) ? 0 : rolename.hashCode());
		return result;
	}
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		DefaultRolePrincipal other = (DefaultRolePrincipal) obj;
		if (rolename == null) {
			if (other.rolename != null)
				return false;
		} else if (!rolename.equals(other.rolename))
			return false;
		return true;
	}
}
