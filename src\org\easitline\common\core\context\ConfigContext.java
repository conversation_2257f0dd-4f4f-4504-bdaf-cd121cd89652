package org.easitline.common.core.context;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.common.utils.crypt.DESUtil;
import org.easitline.common.utils.crypt.SM4Util;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;

/**
 * 配置上下文工具类，支持远程/本地加解密、批量加解密、属性获取等功能。
 * 建议通过 getInstance() 获取单例对象，避免静态变量带来的线程安全和可维护性问题。
 * crypto.url 如果存在走远程加密
 * secret.key（优先） 和 secret.key.path 都为空就不进行本地加密返回明文
 * authCode是送过去的token
 * <AUTHOR>
 * @since 2025-05-15
 * @version 2.1
 */
public class ConfigContext {
	// 配置参数
	private String cryptoUrl = null;
	private String authCode = null;
	private String secretKey = null;
	private final CryptoService cryptoService;
	
	private static String localSecretKey = null;
	
	public static String defaultCryptorType = "S01_";

	// 单例实现
	private static final ConfigContext INSTANCE = new ConfigContext();

	// 记录是否需要直接使用默认DES实例
	private static volatile boolean useDefaultDesInstance = false;

	/**
	 * 获取单例实例
	 */
	public static ConfigContext getInstance() {
		return INSTANCE;
	}

	// 静态方法兼容原有接口，内部委托给单例
	public static String getProperties(String key, String defaultValue) {
		return ServerContext.getProperties(key, defaultValue);
	}

	public static String getAppProperties(String appId, String key, String defaultValue) {
		return AppContext.getContext(appId).getProperty(key, defaultValue);
	}

	public static String decryptionStrWithPrefix(String value, String source) {
		return decryptionStrWithPrefix(null,value, source);
	}
	
	public static Map<String, String> decryptStrWithPrefix(Map<String, String> configMap) {
		Map<String, String> textMap = new HashMap<>();
		Map<String, String> reqMap = new HashMap<>();
		Map<String, String> decryptMap = new HashMap<>();
		if (configMap == null || configMap.isEmpty()) {
			return decryptMap;
		}
		for (Map.Entry<String, String> entry : configMap.entrySet()) {
			String itemKey = entry.getKey();
			String itemValue = entry.getValue();
			if (itemValue != null && itemValue.startsWith(ConfigContext.defaultCryptorType)) {
				itemValue = itemValue.replace(ConfigContext.defaultCryptorType, "");
				reqMap.put(itemKey, itemValue);
			} else {
				textMap.put(itemKey, itemValue);
			}
		}
		if (!reqMap.isEmpty()) {
			decryptMap = INSTANCE.cryptoService.batchDecrypt(reqMap);
		}
		decryptMap.putAll(textMap);
		return decryptMap;
	}
	
	public static String decryptionStrWithPrefix(String prefix,String value, String source) {
		if(StringUtils.isBlank(prefix)) {
			prefix = defaultCryptorType;
		}
		if(StringUtils.notBlank(value)) {
			if(value.startsWith(prefix)) {
				value = value.replace(prefix,"");
				return decryptionStr(value);
			}
		}
		return value;
	}
	
	/**
	 * 解密字符串（自动识别 S01_ 前缀）
	 * @param value 密文
	 * @param source 来源
	 * @return 明文
	 *  source AV:应用配置 GV:全局配置 DS:数据库
	 */
	public static String decryptionStr(String value, String source) {
		if(StringUtils.notBlank(value)) {
			return INSTANCE.cryptoService.decrypt(value);
		}else {
			return value;
		}
	}

	public static String decryptionStr(String value) {
		if(StringUtils.notBlank(value)) {
			return decryptionStr(value,null);
		}
		return value;
	}

	public static String encryptionStr(String value) {
		if(StringUtils.notBlank(value)) {
			return INSTANCE.cryptoService.encrypt(value);
		}
		return value;
	}
	

	/**
	 * 批量解密
	 */
	public static Map<String, String> decryptionMapValue(Map<String, String> paramMap) {
		return INSTANCE.cryptoService.batchDecrypt(paramMap);
	}

	/**
	 * 批量加密
	 */
	public static Map<String, String> encryptionMapValue(Map<String, String> paramMap) {
		return INSTANCE.cryptoService.batchEncrypt(paramMap);
	}


	/**
	 * 构造函数，初始化配置参数，缺失时自动切换本地加解密
	 */
	private ConfigContext() {
		cryptoUrl = System.getProperty("crypto.url", "").trim();
		authCode = System.getProperty("authCode", "yq@mars");
		secretKey = this.getSk();
		if (StringUtils.isBlank(cryptoUrl)) {
			CoreLogger.getCryptor().warn("[ConfigContext] cryptoUrl 配置缺失，自动切换为本地加解密,secretKey:"+secretKey);
			this.cryptoService = new LocalCryptoService(secretKey);
		} else {
			CoreLogger.getCryptor().info("[ConfigContext] cryptoUrl 切换到远程加密:"+cryptoUrl+",secretKey:"+secretKey);
			this.cryptoService = new RemoteCryptoService(cryptoUrl, authCode);
		}
	}

	    
    public String getSk() {
    	if(secretKey!=null) {
    		return secretKey;
    	}
    	String sk = System.getProperty("secret.key","");
    	if(StringUtils.notBlank(sk)) {
    		return sk;
    	}
    	String keyPath = System.getProperty("secret.key.path","");
    	if(StringUtils.notBlank(keyPath)) {
    		File file = new File(keyPath);
    		if(file.exists()) {
    			 try {
	                return new String(Files.readAllBytes(Paths.get(keyPath)), StandardCharsets.UTF_8).trim();
	            } catch (Exception e) {
	            	CoreLogger.getCryptor().error("读取秘钥文件失败: " + keyPath, e);
	            }
    		}else {
    			CoreLogger.getCryptor().error(file.getAbsolutePath()+" - 路径不存在");
    		}
    	}
    	return "";
    }


	public interface CryptoService {
		String encrypt(String plaintext);
		String decrypt(String ciphertext);
		Map<String, String> batchEncrypt(Map<String, String> paramMap);
		Map<String, String> batchDecrypt(Map<String, String> paramMap);
	}

	/**
	 * 远程加解密实现
	 */
	public static class RemoteCryptoService implements CryptoService {
		private final String cryptoUrl;
		private final String authCode;
		public RemoteCryptoService(String cryptoUrl, String authCode) {
			this.cryptoUrl = cryptoUrl;
			this.authCode = authCode;
		}
		@Override
		public String encrypt(String plaintext) {
			Map<String, String> map = new HashMap<>();
			String key = RandomKit.uuid();
			map.put(key, plaintext);
			Map<String, String> result = batchEncrypt(map);
			return result.get(key);
		}
		@Override
		public String decrypt(String ciphertext) {
			Map<String, String> map = new HashMap<>();
			String key = RandomKit.uuid();
			map.put(key, ciphertext);
			Map<String, String> result = batchDecrypt(map);
			return result.get(key);
		}
		@Override
		public Map<String, String> batchEncrypt(Map<String, String> paramMap) {
			return remoteCryptoMap(paramMap, "encrypt");
		}
		@Override
		public Map<String, String> batchDecrypt(Map<String, String> paramMap) {
			return remoteCryptoMap(paramMap, "decrypt");
		}
		private Map<String, String> remoteCryptoMap(Map<String, String> configMap, String type) {
			if (configMap == null || configMap.isEmpty()) {
				return new HashMap<>();
			}
			RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(5000).setConnectionRequestTimeout(5000).setSocketTimeout(10000).build();
			try (CloseableHttpClient httpClient = HttpClients.custom().setDefaultRequestConfig(requestConfig).build()) {
				HttpPost postRequest = new HttpPost(cryptoUrl);
				postRequest.setHeader("Content-Type", "application/json");
				postRequest.setHeader("Accept", "application/json");
				postRequest.setHeader("token", authCode);
				JSONObject params = new JSONObject();
				params.put("type", type);
				params.put("data", configMap);
				String jsonBody = params.toJSONString();
				postRequest.setEntity(new StringEntity(jsonBody, "UTF-8"));
				try (CloseableHttpResponse response = httpClient.execute(postRequest)) {
					int statusCode = response.getStatusLine().getStatusCode();
					String responseBody = EntityUtils.toString(response.getEntity(), "UTF-8");
					if (statusCode != 200) {
						CoreLogger.getCryptor().error("[ConfigContext] 远程加解密请求失败，状态码: " + statusCode + ", 响应: " + responseBody);
						return configMap;
					}
					JSONObject result = JSONObject.parseObject(responseBody);
					int code = result.getIntValue("code");
					if (code == 1) {
						JSONObject data = result.getJSONObject("data");
						Map<String, String> resultMap = new HashMap<>();
						for (String key : data.keySet()) {
							resultMap.put(key, data.getString(key));
						}
						return resultMap;
					} else {
						String msg = result.getString("msg");
						CoreLogger.getCryptor().error("[ConfigContext] 远程加解密失败，code: " + code + ", msg: " + msg + ", 响应: " + responseBody);
						return configMap;
					}
				}
			} catch (Exception e) {
				CoreLogger.getCryptor().error("[ConfigContext] 远程加解密异常>"+configMap, e);
				return configMap;
			}
		}
	}

	public static class LocalCryptoService implements CryptoService {
		private final String secretKey;
		public LocalCryptoService(String secretKey) {
			this.secretKey = secretKey;
		}
		@Override
		public String encrypt(String plaintext) {
			if (plaintext == null) return null;
			if(StringUtils.isBlank(secretKey)) {
				return plaintext;
			}
			return SM4Util.encrypt(plaintext, secretKey);
		}
		
		@Override
		public String decrypt(String ciphertext) {
			if (ciphertext == null) return null;
			if(StringUtils.isBlank(secretKey)) {
				return ciphertext;
			}
			try {
				return SM4Util.decrypt(ciphertext, secretKey);
			} catch (Exception e) {
				CoreLogger.getCryptor().error("[ConfigContext] 本地解密异常>"+ciphertext);
				CoreLogger.getCryptor().error(null, e);
				return ciphertext;
			}
		}
		
		@Override
		public Map<String, String> batchEncrypt(Map<String, String> paramMap) {
			Map<String, String> result = new HashMap<>();
			CoreLogger.getCryptor().info("batchEncrypt.map:"+paramMap);
			if(StringUtils.isBlank(secretKey)) {
				return paramMap;
			}
			if (paramMap != null) {
				for (Map.Entry<String, String> entry : paramMap.entrySet()) {
					if(StringUtils.notBlank(entry.getValue())) {
						result.put(entry.getKey(), encrypt(entry.getValue()));
					}else {
						result.put(entry.getKey(), entry.getValue());
					}
				}
			}
			CoreLogger.getCryptor().info("batchEncrypt.map.result:"+result);
			return result;
		}
		
		@Override
		public Map<String, String> batchDecrypt(Map<String, String> paramMap) {
			CoreLogger.getCryptor().info("batchDecrypt.map:"+paramMap);
			if(StringUtils.isBlank(secretKey)) {
				return paramMap;
			}
			Map<String, String> result = new HashMap<>();
			if (paramMap != null) {
				for (Map.Entry<String, String> entry : paramMap.entrySet()) {
					if(StringUtils.notBlank(entry.getValue())) {
						result.put(entry.getKey(), decrypt(entry.getValue()));
					}else {
						result.put(entry.getKey(), entry.getValue());
					}
				}
			}
			CoreLogger.getCryptor().info("batchDecrypt.map.result:"+result);
			return result;
		}
	}

	/**
	 * 远程加解密异常
	 */
	public static class CryptoException extends RuntimeException {
		private static final long serialVersionUID = 1L;
		public CryptoException(String msg) {
			super(msg);
		}
	}
	
	/**
	 * DES解密，支持动态方法检查
	 * 优先使用 getInstanceWithKey 方法，如果不存在则回退到 getInstance 方法
	 * @param encryptedValue 加密值
	 * @return 解密后的值，如果解密失败返回原值
	 */
	public static String desDecryptStr(String encryptedValue) {
		if (encryptedValue != null && encryptedValue.startsWith("3DES_")) {
			encryptedValue = encryptedValue.replace("3DES_", "");
			if (useDefaultDesInstance) {
				// 已经降级，直接用默认方法，异常直接抛出
				return DESUtil.getInstance().decryptStr(encryptedValue);
			}
			try {
				// 优先尝试使用带密钥的实例方法
				return DESUtil.getInstanceWithKey(getLocalSk()).decryptStr(encryptedValue);
			} catch (NoSuchMethodError e) {
				// 方法不存在，永久降级
				useDefaultDesInstance = true;
				CoreLogger.getCryptor().warn("[ConfigContext] getInstanceWithKey方法不存在，永久降级为默认getInstance方法");
				return DESUtil.getInstance().decryptStr(encryptedValue);
			} catch (Exception e) {
				// 其他异常，尝试使用默认方法
				CoreLogger.getCryptor().warn("[ConfigContext] getInstanceWithKey方法调用异常,可能秘钥错误，尝试使用默认方法", e);
				return DESUtil.getInstance().decryptStr(encryptedValue);
			}
		}
		return encryptedValue;
	}
	
	public static String getLocalSk() {
		if(localSecretKey!=null) {
			return localSecretKey;
		}
	    synchronized (ConfigContext.class) {
	        if (localSecretKey == null) {
	            localSecretKey = loadLocalSecretKey();
	        }
	    }
		return localSecretKey;
	}
	
	/**
	 * secrets.inline.key
	 * secrets.key.file 
	 * @return
	 */
    private static String loadLocalSecretKey() {
    	String sk = System.getProperty("secrets.inline.key","");
    	if(StringUtils.notBlank(sk)) {
    		CoreLogger.getCryptor().info("secrets.inline.key>"+sk);
    		return sk;
    	}
    	String keyPath = System.getProperty("secrets.key.file","");
    	if(StringUtils.notBlank(keyPath)) {
    		File file = new File(keyPath);
    		Path path = Paths.get(keyPath);
    		if (Files.exists(path)) {
    			 try {
    				String content = new String(Files.readAllBytes(path), StandardCharsets.UTF_8).replaceAll("\\s+", "").trim();
    				if (content.startsWith("\uFEFF")) {
						content = content.substring(1);
					}
    				CoreLogger.getCryptor().info("secrets.inline.file>"+file.getAbsolutePath()+">"+content);
	                return content;
	            } catch (Exception e) {
	            	CoreLogger.getCryptor().error("读取秘钥文件失败: " + keyPath, e);
	            }
    		}else {
    			CoreLogger.getCryptor().error(file.getAbsolutePath()+" - 路径不存在");
    		}
    	}
    	return "";
    }

}
