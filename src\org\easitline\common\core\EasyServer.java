package org.easitline.common.core;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.utils.calendar.EasyDate;

/**
 * 服务器运行时信息
 */
public class EasyServer {

	/**
	 * 系统启动时间
	 */
	private static  String boottime = EasyDate.getCurrentDateString();
	
	/**
	 * 系统启动的当前毫秒
	 */
	private static  long initTimeMillis = System.currentTimeMillis();
	
	/**
	 * MARS版本信息
	 * @return
	 */
	public static String getVersion(){
		return "3.5";
	}
	
	/**
	 * 版本日期
	 * @return
	 */
	public static String getVersionDate(){
		return "20250812";
	}

	/**
	 * 容器类型
	 * tomcat apusic tongweb
	 * @return
	 */
	public static String getContainerType() {
		return ServerContext.getContainerType();
	}

	
	/**
	 * 获得系统运行时间，返回运行了多少天、多少小时，多少分钟，多少秒。
	 * 
	 * @return String
	 */
	public static  String getRunime() {
		long diff = System.currentTimeMillis() - initTimeMillis;
		long day = diff / (24 * 60 * 60 * 1000l);
		long hour = (diff % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000l);
		long minute = (diff % (60 * 60 * 1000)) / (60 * 1000l);
		long second = (diff % (60 * 1000)) / 1000l;
		String str = "";
		if (day > 0)
			str += day + "天";
		if (hour > 0)
			str += hour + "小时";
		if (minute > 0)
			str += minute + "分钟";
		if (second > 0)
			str += second + "秒";
		return str;
	}
	
	
	public static String getBootTime(){
		return boottime;
	}
	
	

}
