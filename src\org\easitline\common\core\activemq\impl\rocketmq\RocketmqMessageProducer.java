package org.easitline.common.core.activemq.impl.rocketmq;

import javax.jms.Destination;


import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageProducer;
import javax.jms.ObjectMessage;

import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.easitline.common.core.activemq.BrokerContext;
import org.easitline.common.core.activemq.log.ActivieMQLogger;

public class RocketmqMessageProducer implements MessageProducer {
	
	private long timer = System.currentTimeMillis();
	
	private DefaultMQProducer producer;
	
	private BrokerContext context;
	
	public RocketmqMessageProducer(BrokerContext context,DefaultMQProducer producer){
		this.context = context;
		this.producer = producer;
	}

	@Override
	public void close() throws JMSException {
	}

	@Override
	public int getDeliveryMode() throws JMSException {
		return 0;
	}

	@Override
	public Destination getDestination() throws JMSEx<PERSON> {
		return null;
	}

	@Override
	public boolean getDisableMessageID() throws JMSException {
		return false;
	}

	@Override
	public boolean getDisableMessageTimestamp() throws JMSException {
		return false;
	}

	@Override
	public int getPriority() throws JMSException {
		return 0;
	}

	@Override
	public long getTimeToLive() throws JMSException {
		return 0;
	}

	@Override
	public void send(Message message) throws JMSException {
		ObjectMessage objectMessage =  (ObjectMessage)message;
		String messageText = objectMessage.getObject().toString();
		
		ActivieMQLogger.getLogger().info("rocketmq.send("+context.getBrokerName()+") >> "+messageText);
		org.apache.rocketmq.common.message.Message msg = new org.apache.rocketmq.common.message.Message(
				context.getBrokerName(), // 消息主题名称
				"*", // 消息标签
				messageText.getBytes()// 消息内容
		);
		try {
			SendResult sendResult = producer.send(msg);
			ActivieMQLogger.getLogger().info("RocketmqMessageProducer.send(broker:"+context.getBrokerName()+")->sendResult:"+sendResult);
		} catch (Exception ex) {
			ActivieMQLogger.getLogger().info("RocketmqMessageProducer.send(broker:"+context.getBrokerName()+") msg["+messageText+"] error,cause:"+ex.getMessage(),ex);
			throw new JMSException(ex.getMessage());
		}
		// 发送消息并返回结果
	}

	@Override
	public void send(Destination arg0, Message arg1) throws JMSException {
		this.send(arg1);

	}

	@Override
	public void send(Message arg0, int arg1, int arg2, long arg3) throws JMSException {
		this.send(arg0);

	}

	@Override
	public void send(Destination arg0, Message arg1, int arg2, int arg3, long arg4) throws JMSException {
		this.send(arg1);
	}

	@Override
	public void setDeliveryMode(int arg0) throws JMSException {
		// TODO Auto-generated method stub

	}

	@Override
	public void setDisableMessageID(boolean arg0) throws JMSException {
		// TODO Auto-generated method stub

	}

	@Override
	public void setDisableMessageTimestamp(boolean arg0) throws JMSException {
		// TODO Auto-generated method stub

	}

	@Override
	public void setPriority(int arg0) throws JMSException {
		// TODO Auto-generated method stub

	}

	@Override
	public void setTimeToLive(long arg0) throws JMSException {
		// TODO Auto-generated method stub

	}
	
	
	@Override
	public String toString() {
		return "RocketmqMessageProducer("+context.getBrokerName()+","+context.getConnector()+"),Producer实例名："+this.producer.getInstanceName()+",Producer对象名："+this.producer;
	}

}
