# 缓存操作详解

## 概述

EasitLine Core 提供了统一的缓存管理接口，支持多种缓存实现（Redis、Memcache等）。本文档详细介绍缓存相关类的使用方法和最佳实践。

## 核心类介绍

### CacheManager - 缓存管理器

CacheManager 是缓存操作的入口类，提供统一的缓存访问接口。

#### 基础使用

```java
// 获取默认缓存（根据配置自动选择Redis或Memcache）
EasyCache cache = CacheManager.getCache();

// 明确指定使用Redis
EasyCache redisCache = CacheManager.getRedis();

// 明确指定使用Memcache
EasyCache memcache = CacheManager.getMemcache();

// 获取默认本地缓存
EasyCache defaultCache = CacheManager.getDefaultCache();
```

#### 配置说明

在配置文件中设置缓存类型：

```properties
# 缓存类型配置
cache.type=redis  # 可选值：redis, memcache

# Redis配置
redis.host=127.0.0.1
redis.port=6379
redis.password=
redis.database=0
redis.timeout=2000
redis.maxTotal=100
redis.maxIdle=20
redis.minIdle=5

# Memcache配置
memcache.servers=127.0.0.1:11211
memcache.weights=1
memcache.initConn=10
memcache.minConn=5
memcache.maxConn=50
```

### EasyCache - 缓存操作接口

EasyCache 定义了标准的缓存操作方法。

#### 基础操作

```java
EasyCache cache = CacheManager.getCache();

// 存储数据（默认3小时过期）
cache.put("user:123", userObject);

// 存储数据（指定过期时间，单位：秒）
cache.put("user:123", userObject, CacheTime.HOUR); // 1小时
cache.put("session:abc", sessionData, 1800); // 30分钟

// 获取数据
User user = cache.get("user:123");

// 删除数据
cache.delete("user:123");

// 清空所有缓存（谨慎使用）
cache.flushAll();
```

#### 高级操作示例

```java
// 1. 缓存用户信息
public User getUserById(String userId) {
    String cacheKey = "user:" + userId;
    
    // 先从缓存获取
    User user = cache.get(cacheKey);
    if (user != null) {
        debug("缓存命中: " + cacheKey);
        return user;
    }
    
    // 缓存未命中，从数据库查询
    EasyQuery query = getQuery();
    EasyRow row = query.queryForObject(
        "SELECT * FROM users WHERE user_id = ?", userId);
    
    if (row != null) {
        user = convertToUser(row);
        // 存入缓存，1小时过期
        cache.put(cacheKey, user, CacheTime.HOUR);
        debug("数据已缓存: " + cacheKey);
    }
    
    return user;
}

// 2. 缓存查询结果
public List<User> getUsersByDepartment(String deptId) {
    String cacheKey = "users:dept:" + deptId;
    
    List<User> users = cache.get(cacheKey);
    if (users != null) {
        return users;
    }
    
    // 从数据库查询
    EasyQuery query = getQuery();
    List<EasyRow> rows = query.queryForList(
        "SELECT * FROM users WHERE dept_id = ? AND status = 1", deptId);
    
    users = convertToUserList(rows);
    
    // 缓存30分钟
    cache.put(cacheKey, users, 1800);
    
    return users;
}

// 3. 缓存计算结果
public JSONObject getDashboardStats() {
    String cacheKey = "stats:dashboard:" + EasyDate.getCurrentDateString("yyyyMMdd");
    
    JSONObject stats = cache.get(cacheKey);
    if (stats != null) {
        return stats;
    }
    
    // 计算统计数据
    EasyQuery query = getQuery();
    
    int totalUsers = query.queryForInt("SELECT COUNT(*) FROM users WHERE status = 1");
    int totalOrders = query.queryForInt("SELECT COUNT(*) FROM orders WHERE DATE(create_time) = CURDATE()");
    BigDecimal totalAmount = query.queryForObject(
        "SELECT SUM(amount) FROM orders WHERE DATE(create_time) = CURDATE()", BigDecimal.class);
    
    stats = new JSONObject();
    stats.put("totalUsers", totalUsers);
    stats.put("totalOrders", totalOrders);
    stats.put("totalAmount", totalAmount);
    stats.put("updateTime", new Date());
    
    // 缓存1小时
    cache.put(cacheKey, stats, CacheTime.HOUR);
    
    return stats;
}
```

### CacheTime - 缓存时间常量

```java
public class CacheTime {
    public static final int MINUTE = 60;           // 1分钟
    public static final int HOUR = 3600;           // 1小时
    public static final int DAY = 86400;           // 1天
    public static final int WEEK = 604800;         // 1周
    public static final int MONTH = 2592000;       // 1月（30天）
}

// 使用示例
cache.put("short_term", data, CacheTime.MINUTE * 5);  // 5分钟
cache.put("medium_term", data, CacheTime.HOUR * 2);   // 2小时
cache.put("long_term", data, CacheTime.DAY * 7);      // 7天
```

## Redis 缓存详解

### RedisImpl - Redis缓存实现

RedisImpl 提供了Redis的具体实现，支持单机、集群、哨兵模式。

#### 配置方式

```properties
# Redis单机配置
cache.type=redis
redis.host=127.0.0.1
redis.port=6379
redis.password=yourpassword
redis.database=0
redis.timeout=2000

# Redis连接池配置
redis.maxTotal=100
redis.maxIdle=20
redis.minIdle=5
redis.maxWaitMillis=3000

# Redis集群配置
redis.cluster.nodes=127.0.0.1:7000,127.0.0.1:7001,127.0.0.1:7002
redis.cluster.maxRedirections=3

# Redis哨兵配置
redis.sentinel.master=mymaster
redis.sentinel.nodes=127.0.0.1:26379,127.0.0.1:26380,127.0.0.1:26381
```

#### 高级功能

```java
// 获取Redis实例
RedisImpl redis = (RedisImpl) CacheManager.getRedis();

// 字符串操作
redis.put("key", "value", 3600);
String value = redis.get("key");

// 如果Redis实现支持更多操作，可以扩展使用
// 注意：以下方法需要RedisImpl具体实现

// 哈希操作
redis.hset("user:123", "name", "张三");
redis.hset("user:123", "email", "<EMAIL>");
String name = redis.hget("user:123", "name");
Map<String, String> userInfo = redis.hgetAll("user:123");

// 列表操作
redis.lpush("notifications:user:123", "新消息1");
redis.lpush("notifications:user:123", "新消息2");
List<String> notifications = redis.lrange("notifications:user:123", 0, 9); // 获取最新10条

// 集合操作
redis.sadd("online_users", "user1", "user2", "user3");
Set<String> onlineUsers = redis.smembers("online_users");
boolean isOnline = redis.sismember("online_users", "user1");

// 有序集合操作
redis.zadd("user_scores", 100, "user1");
redis.zadd("user_scores", 200, "user2");
Set<String> topUsers = redis.zrevrange("user_scores", 0, 9); // 获取前10名

// 设置过期时间
redis.expire("temp_data", 300); // 5分钟后过期

// 检查键是否存在
boolean exists = redis.exists("user:123");

// 获取剩余过期时间
long ttl = redis.ttl("user:123");
```

### Redis 使用模式

#### 1. 缓存模式（Cache-Aside）

```java
public class UserService {
    
    private EasyCache cache = CacheManager.getRedis();
    
    // 读取数据
    public User getUser(String userId) {
        String cacheKey = "user:" + userId;
        
        // 1. 先从缓存读取
        User user = cache.get(cacheKey);
        if (user != null) {
            return user;
        }
        
        // 2. 缓存未命中，从数据库读取
        EasyQuery query = getQuery();
        EasyRow row = query.queryForObject(
            "SELECT * FROM users WHERE user_id = ?", userId);
        
        if (row != null) {
            user = convertToUser(row);
            // 3. 写入缓存
            cache.put(cacheKey, user, CacheTime.HOUR);
        }
        
        return user;
    }
    
    // 更新数据
    public void updateUser(User user) {
        String cacheKey = "user:" + user.getUserId();
        
        // 1. 先更新数据库
        EasyQuery query = getQuery();
        EasyRecord record = convertToRecord(user);
        query.update(record);
        
        // 2. 删除缓存（下次读取时重新加载）
        cache.delete(cacheKey);
        
        // 或者直接更新缓存
        // cache.put(cacheKey, user, CacheTime.HOUR);
    }
}
```

#### 2. 分布式锁模式

```java
public class DistributedLockService {
    
    private EasyCache cache = CacheManager.getRedis();
    
    // 获取分布式锁
    public boolean acquireLock(String lockKey, String lockValue, int expireSeconds) {
        try {
            // 使用SET命令的NX和EX选项实现分布式锁
            // 这里假设Redis实现支持这种操作
            String result = cache.setIfNotExists(lockKey, lockValue, expireSeconds);
            return "OK".equals(result);
        } catch (Exception e) {
            error("获取分布式锁失败", e);
            return false;
        }
    }
    
    // 释放分布式锁
    public boolean releaseLock(String lockKey, String lockValue) {
        try {
            // 使用Lua脚本确保原子性
            String luaScript = 
                "if redis.call('get', KEYS[1]) == ARGV[1] then " +
                "    return redis.call('del', KEYS[1]) " +
                "else " +
                "    return 0 " +
                "end";
            
            // 这里假设Redis实现支持Lua脚本执行
            Object result = cache.eval(luaScript, Arrays.asList(lockKey), Arrays.asList(lockValue));
            return "1".equals(result.toString());
        } catch (Exception e) {
            error("释放分布式锁失败", e);
            return false;
        }
    }
    
    // 使用分布式锁执行操作
    public void executeWithLock(String lockKey, Runnable task) {
        String lockValue = RandomKit.uuid();
        boolean acquired = false;
        
        try {
            // 尝试获取锁，最多等待5秒
            for (int i = 0; i < 50; i++) {
                acquired = acquireLock(lockKey, lockValue, 30); // 锁30秒过期
                if (acquired) {
                    break;
                }
                Thread.sleep(100); // 等待100ms后重试
            }
            
            if (acquired) {
                // 执行业务逻辑
                task.run();
            } else {
                throw new RuntimeException("无法获取分布式锁: " + lockKey);
            }
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("获取锁被中断", e);
        } finally {
            if (acquired) {
                releaseLock(lockKey, lockValue);
            }
        }
    }
}

// 使用示例
public void processOrder(String orderId) {
    String lockKey = "order:lock:" + orderId;
    
    distributedLockService.executeWithLock(lockKey, () -> {
        // 处理订单的业务逻辑
        // 这里的代码在分布式环境下只会被一个实例执行
        EasyQuery query = getQuery();
        EasyRow order = query.queryForObject(
            "SELECT * FROM orders WHERE order_id = ?", orderId);
        
        if (order != null && "pending".equals(order.getColumnValue("status"))) {
            // 更新订单状态
            query.update("UPDATE orders SET status = 'processing' WHERE order_id = ?", orderId);
            
            // 其他业务逻辑...
        }
    });
}
```

## Memcache 缓存详解

### MemcacheImpl - Memcache缓存实现

MemcacheImpl 提供了Memcache的具体实现。

#### 配置方式

```properties
# Memcache配置
cache.type=memcache
memcache.servers=127.0.0.1:11211,127.0.0.1:11212
memcache.weights=1,1
memcache.initConn=10
memcache.minConn=5
memcache.maxConn=50
memcache.maxIdle=1000*60*5
memcache.maintSleep=30
memcache.nagle=false
memcache.socketTO=3000
memcache.socketConnectTO=0
```

#### 使用示例

```java
// 获取Memcache实例
EasyCache memcache = CacheManager.getMemcache();

// 基础操作
memcache.put("user:123", userObject, CacheTime.HOUR);
User user = memcache.get("user:123");
memcache.delete("user:123");

// 批量操作
Map<String, Object> multiData = new HashMap<>();
multiData.put("user:123", user1);
multiData.put("user:456", user2);

// 如果Memcache实现支持批量操作
for (Map.Entry<String, Object> entry : multiData.entrySet()) {
    memcache.put(entry.getKey(), entry.getValue(), CacheTime.HOUR);
}
```

## 缓存策略和模式

### 1. 缓存更新策略

#### Cache-Aside（旁路缓存）
```java
// 读操作
public User getUser(String userId) {
    User user = cache.get("user:" + userId);
    if (user == null) {
        user = loadUserFromDB(userId);
        if (user != null) {
            cache.put("user:" + userId, user, CacheTime.HOUR);
        }
    }
    return user;
}

// 写操作
public void updateUser(User user) {
    updateUserInDB(user);
    cache.delete("user:" + user.getId()); // 删除缓存，让下次读取时重新加载
}
```

#### Write-Through（写穿透）
```java
public void updateUser(User user) {
    // 同时更新数据库和缓存
    updateUserInDB(user);
    cache.put("user:" + user.getId(), user, CacheTime.HOUR);
}
```

#### Write-Behind（写回）
```java
public void updateUser(User user) {
    // 先更新缓存
    cache.put("user:" + user.getId(), user, CacheTime.HOUR);
    
    // 异步更新数据库
    asyncUpdateUserInDB(user);
}
```

### 2. 缓存穿透防护

```java
// 使用空对象模式防止缓存穿透
private static final User NULL_USER = new User(); // 空对象标识

public User getUser(String userId) {
    String cacheKey = "user:" + userId;
    User user = cache.get(cacheKey);
    
    if (user == NULL_USER) {
        return null; // 返回null表示用户不存在
    }
    
    if (user == null) {
        user = loadUserFromDB(userId);
        if (user != null) {
            cache.put(cacheKey, user, CacheTime.HOUR);
        } else {
            // 缓存空结果，但时间较短
            cache.put(cacheKey, NULL_USER, 60);
        }
    }
    
    return user;
}

// 使用布隆过滤器防止缓存穿透（需要额外实现）
public User getUserWithBloomFilter(String userId) {
    // 先检查布隆过滤器
    if (!bloomFilter.mightContain(userId)) {
        return null; // 肯定不存在
    }
    
    // 可能存在，继续正常的缓存逻辑
    return getUser(userId);
}
```

### 3. 缓存雪崩防护

```java
// 使用随机过期时间防止缓存雪崩
public void cacheUserWithRandomExpire(String userId, User user) {
    int baseExpire = CacheTime.HOUR;
    int randomExpire = RandomKit.randomInt(0, 300); // 0-5分钟随机
    
    cache.put("user:" + userId, user, baseExpire + randomExpire);
}

// 使用互斥锁防止缓存击穿
private final Map<String, Object> lockMap = new ConcurrentHashMap<>();

public User getUserWithMutex(String userId) {
    String cacheKey = "user:" + userId;
    User user = cache.get(cacheKey);
    
    if (user == null) {
        String lockKey = "lock:" + cacheKey;
        Object lock = lockMap.computeIfAbsent(lockKey, k -> new Object());
        
        synchronized (lock) {
            // 双重检查
            user = cache.get(cacheKey);
            if (user == null) {
                user = loadUserFromDB(userId);
                if (user != null) {
                    cache.put(cacheKey, user, CacheTime.HOUR);
                }
            }
        }
        
        lockMap.remove(lockKey); // 清理锁对象
    }
    
    return user;
}
```

### 4. 多级缓存

```java
public class MultiLevelCacheService {
    
    private EasyCache localCache = CacheManager.getDefaultCache();  // 本地缓存
    private EasyCache redisCache = CacheManager.getRedis();         // Redis缓存
    
    public User getUser(String userId) {
        String cacheKey = "user:" + userId;
        
        // 1. 先查本地缓存
        User user = localCache.get(cacheKey);
        if (user != null) {
            debug("本地缓存命中: " + cacheKey);
            return user;
        }
        
        // 2. 查Redis缓存
        user = redisCache.get(cacheKey);
        if (user != null) {
            debug("Redis缓存命中: " + cacheKey);
            // 写入本地缓存，过期时间较短
            localCache.put(cacheKey, user, 300); // 5分钟
            return user;
        }
        
        // 3. 查数据库
        user = loadUserFromDB(userId);
        if (user != null) {
            // 写入Redis缓存
            redisCache.put(cacheKey, user, CacheTime.HOUR);
            // 写入本地缓存
            localCache.put(cacheKey, user, 300);
        }
        
        return user;
    }
    
    public void updateUser(User user) {
        String cacheKey = "user:" + user.getId();
        
        // 更新数据库
        updateUserInDB(user);
        
        // 删除所有级别的缓存
        localCache.delete(cacheKey);
        redisCache.delete(cacheKey);
    }
}
```

## 性能监控和调优

### 1. 缓存命中率监控

```java
public class CacheMonitor {
    
    private AtomicLong hitCount = new AtomicLong(0);
    private AtomicLong missCount = new AtomicLong(0);
    
    public <T> T getWithMonitor(String key) {
        T value = cache.get(key);
        
        if (value != null) {
            hitCount.incrementAndGet();
        } else {
            missCount.incrementAndGet();
        }
        
        return value;
    }
    
    public double getHitRate() {
        long total = hitCount.get() + missCount.get();
        return total == 0 ? 0.0 : (double) hitCount.get() / total;
    }
    
    public void logStats() {
        long hits = hitCount.get();
        long misses = missCount.get();
        double hitRate = getHitRate();
        
        info(String.format("缓存统计 - 命中: %d, 未命中: %d, 命中率: %.2f%%", 
            hits, misses, hitRate * 100));
    }
    
    // 定期输出统计信息
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void scheduledLogStats() {
        logStats();
    }
}
```

### 2. 缓存性能优化

```java
// 批量操作优化
public Map<String, User> batchGetUsers(List<String> userIds) {
    Map<String, User> result = new HashMap<>();
    List<String> missedIds = new ArrayList<>();
    
    // 1. 批量从缓存获取
    for (String userId : userIds) {
        String cacheKey = "user:" + userId;
        User user = cache.get(cacheKey);
        if (user != null) {
            result.put(userId, user);
        } else {
            missedIds.add(userId);
        }
    }
    
    // 2. 批量从数据库查询未命中的数据
    if (!missedIds.isEmpty()) {
        List<User> dbUsers = batchLoadUsersFromDB(missedIds);
        
        // 3. 批量写入缓存
        for (User user : dbUsers) {
            String cacheKey = "user:" + user.getId();
            cache.put(cacheKey, user, CacheTime.HOUR);
            result.put(user.getId(), user);
        }
    }
    
    return result;
}

// 预热缓存
public void warmUpCache() {
    info("开始预热缓存...");
    
    // 预热热点数据
    List<String> hotUserIds = getHotUserIds();
    for (String userId : hotUserIds) {
        try {
            getUser(userId); // 触发缓存加载
        } catch (Exception e) {
            error("预热用户缓存失败: " + userId, e);
        }
    }
    
    // 预热配置数据
    loadSystemConfigs();
    
    info("缓存预热完成");
}
```

## 常见问题和解决方案

### 1. 缓存连接问题

```java
// 健康检查
public boolean checkCacheHealth() {
    try {
        String testKey = "health_check:" + System.currentTimeMillis();
        String testValue = "OK";
        
        // 测试写入
        cache.put(testKey, testValue, 60);
        
        // 测试读取
        String result = cache.get(testKey);
        
        // 清理测试数据
        cache.delete(testKey);
        
        return testValue.equals(result);
        
    } catch (Exception e) {
        error("缓存健康检查失败", e);
        return false;
    }
}

// 缓存降级处理
public User getUserWithFallback(String userId) {
    try {
        return getUser(userId); // 正常缓存逻辑
    } catch (Exception e) {
        warn("缓存异常，降级到数据库查询", e);
        return loadUserFromDB(userId); // 降级到数据库
    }
}
```

### 2. 内存泄漏问题

```java
// 定期清理过期缓存
@Scheduled(fixedRate = 3600000) // 每小时执行一次
public void cleanupExpiredCache() {
    try {
        // 如果使用本地缓存，需要手动清理过期数据
        if (cache instanceof DefaultEasyCacheImpl) {
            ((DefaultEasyCacheImpl) cache).cleanup();
        }
        
        info("缓存清理完成");
    } catch (Exception e) {
        error("缓存清理失败", e);
    }
}

// 监控缓存大小
public void monitorCacheSize() {
    try {
        // 获取缓存统计信息（需要具体实现支持）
        long cacheSize = getCacheSize();
        long maxSize = getMaxCacheSize();
        
        if (cacheSize > maxSize * 0.8) {
            warn("缓存使用率过高: " + (cacheSize * 100 / maxSize) + "%");
        }
        
    } catch (Exception e) {
        error("缓存监控失败", e);
    }
}
```

### 3. 序列化问题

```java
// 确保缓存对象可序列化
public class CacheableUser implements Serializable {
    private static final long serialVersionUID = 1L;
    
    private String userId;
    private String userName;
    private Date createTime;
    
    // 构造函数、getter、setter...
    
    // 避免缓存不可序列化的对象
    private transient Connection connection; // 不会被序列化
}

// 处理序列化异常
public void safeCachePut(String key, Object value, int expireSeconds) {
    try {
        cache.put(key, value, expireSeconds);
    } catch (Exception e) {
        if (e.getCause() instanceof NotSerializableException) {
            warn("对象不可序列化，跳过缓存: " + key);
        } else {
            error("缓存存储失败: " + key, e);
        }
    }
}
```
