package org.easitline.common.core.resource;

import java.util.HashMap;
import java.util.Map;

/**
 * 服务描述信息
 * <AUTHOR>
 */
public class ServiceResource implements java.io.Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 *服务ID，全局唯一,如：SYS_SMS_SERVICE
	 */
	public String serviceId;

	/**
	 * 服务名称,如：短信服务
	 */
	public String serviceName;
	
	/**
	 * 消息处理的类名，全路径,例如:com.easitline.service.SmsService
	 */
	public String className;
	
	/**
	 * 服务描述
	 */
	public String description;
	
	public String appName;
	
	/**
	 * 是否远程服务 比如Dubbo
	 */
	public boolean remoteService = false;
	
	public Map<String,ApiResource> apiResLst = new HashMap<String, ApiResource>();
	
	/**
	 * 保存service调用的Classloader路径，很重要！
	 */
	private ClassLoader classloader = Thread.currentThread().getContextClassLoader();
	
	/**
	 * 创建实例
	 * @return
	 * @throws Exception
	 */
	public Object newInstance() throws Exception {
		return this.classloader.loadClass(this.className).newInstance();
	}
	
	public String toString(){
		StringBuffer buf = new StringBuffer();
		buf.append("服务ID："+this.serviceId+"\r\n");
		buf.append("服务名称："+this.serviceName+"\r\n");
		buf.append("服务归属应用："+this.appName+"\r\n");
		buf.append("服务实现类："+this.className+"\r\n");
		buf.append("服务描述："+this.description+"\r\n");
		return buf.toString();
	}
}
