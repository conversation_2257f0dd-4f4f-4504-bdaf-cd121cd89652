package org.easitline.common.core.activemq.impl.kafka;

import javax.jms.Destination;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageProducer;
import javax.jms.ObjectMessage;

import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.easitline.common.core.activemq.BrokerContext;
import org.easitline.common.core.activemq.log.ActivieMQLogger;

public class KafkaMessageProducer implements MessageProducer{
	
	
	private KafkaProducer<String, String> producer;
	
	private BrokerContext context;
	
	public KafkaMessageProducer(BrokerContext context,KafkaProducer<String, String> producer){
		this.context = context;
		this.producer = producer;
	}

	@Override
	public void close() throws JMSException {
		this.producer.close();
	}

	@Override
	public int getDeliveryMode() throws JMSException {
		return 0;
	}

	@Override
	public Destination getDestination() throws JMSException {
		return null;
	}

	@Override
	public boolean getDisableMessageID() throws JMSException {
		return false;
	}

	@Override
	public boolean getDisableMessageTimestamp() throws JMSException {
		return false;
	}

	@Override
	public int getPriority() throws JMSException {
		return 0;
	}

	@Override
	public long getTimeToLive() throws JMSException {
		return 0;
	}

	@Override
	public void send(Message message) throws JMSException {
		ObjectMessage objectMessage =  (ObjectMessage)message;
		String messageText = objectMessage.getObject().toString();
		ActivieMQLogger.getLogger().info("kafka.send("+context.getBrokerName()+") >> "+messageText);
		ProducerRecord<String, String> record = new ProducerRecord<String, String>(this.context.getBrokerName(), messageText);
		try {
			producer.send(record);
		} catch (Exception ex) {
			throw new JMSException(ex.getMessage());
		}
		// 发送消息并返回结果
	}

	@Override
	public void send(Destination arg0, Message arg1) throws JMSException {
		// TODO Auto-generated method stub
	}

	@Override
	public void send(Message arg0, int arg1, int arg2, long arg3) throws JMSException {
		// TODO Auto-generated method stub

	}

	@Override
	public void send(Destination arg0, Message arg1, int arg2, int arg3, long arg4) throws JMSException {
		// TODO Auto-generated method stub

	}

	@Override
	public void setDeliveryMode(int arg0) throws JMSException {
		// TODO Auto-generated method stub

	}

	@Override
	public void setDisableMessageID(boolean arg0) throws JMSException {
		// TODO Auto-generated method stub

	}

	@Override
	public void setDisableMessageTimestamp(boolean arg0) throws JMSException {
		// TODO Auto-generated method stub

	}

	@Override
	public void setPriority(int arg0) throws JMSException {
		// TODO Auto-generated method stub

	}

	@Override
	public void setTimeToLive(long arg0) throws JMSException {
		// TODO Auto-generated method stub

	}


}
