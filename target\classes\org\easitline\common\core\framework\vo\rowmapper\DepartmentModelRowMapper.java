package org.easitline.common.core.framework.vo.rowmapper;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.easitline.common.core.framework.vo.Department;
import org.easitline.common.core.framework.vo.model.DepartmentModel;
import org.easitline.common.db.EasyRowMapper;


/**

create table EASI_DEPT
(
   DEPT_ID              varchar(32) not null comment '部门ID，唯一',
   DEPT_CODE            varchar(30) comment '部门代码，格式：001002003....',
   DEPT_NAME            varchar(100) comment '部门名称',
   P_DEPT_CODE          varchar(12) comment '父部门代码',
   DEPT_TYPE            varchar(30) comment '部门类型，参考：EASI_DEPT_TYPE',
   LINK_MAN				varchar(30)	comment '联系人',
   LINK_MOBILE			varchar(50)	comment '联系电话',
   RESERVE1             varchar(100) comment '保留字段1',
   RESERVE2             varchar(100) comment '保留字段2',
   RESERVE3             varchar(100) comment '保留字段3',
   RESERVE4             varchar(100) comment '保留字段4',
   RESERVE5             varchar(100) comment '保留字段5',
   RESERVE6             varchar(100) comment '保留字段6',
   RESERVE7             varchar(100) comment '保留字段7',
   RESERVE8             varchar(100) comment '保留字段8',
   RESERVE9             varchar(100) comment '保留字段9',
   RESERVE10            varchar(100) comment '保留字段10',
   primary key (DEPT_ID)
);
 * <AUTHOR>
 *
 */
public class DepartmentModelRowMapper implements EasyRowMapper<DepartmentModel> {

	@Override
	public DepartmentModel mapRow(ResultSet rs, int rowNum) {
		// TODO Auto-generated method stub
		DepartmentModel vo = new DepartmentModel();
		try {
			vo.setDeptType(rs.getString("DEPT_TYPE"));
			vo.setDeptCode(rs.getString("DEPT_CODE"));
			vo.setDeptId(rs.getString("DEPT_ID"));
			vo.setPdeptCode(rs.getString("P_DEPT_CODE"));
			vo.setDeptName(rs.getString("DEPT_NAME"));
			vo.setLinkMan(rs.getString("LINK_MAN"));
			vo.setLinkMobile(rs.getString("LINK_MOBILE"));
			vo.setReserve1(rs.getString("RESERVE1"));
			vo.setReserve2(rs.getString("RESERVE2"));
			vo.setReserve3(rs.getString("RESERVE3"));
			vo.setReserve4(rs.getString("RESERVE4"));
			vo.setReserve5(rs.getString("RESERVE5"));
			vo.setReserve6(rs.getString("RESERVE6"));
			vo.setReserve7(rs.getString("RESERVE7"));
			vo.setReserve8(rs.getString("RESERVE8"));
			vo.setReserve9(rs.getString("RESERVE9"));
			vo.setReserve10(rs.getString("RESERVE10"));
			vo.setNodeType(rs.getInt("NODE_TYPE"));
			
		} catch (SQLException ex) {
			ex.printStackTrace();
		}
		return vo;
		
	}

}
