# 日志格式修复总结

## 问题描述
用户反映日志输出缺少类名和行号信息，例如：
```
2025-08-21 06:08:30.024 [DefaultQuartzScheduler_Worker-8] INFO  yc-yjmanage - 任务数量为空，无需统计
```

期望的输出格式应该包含类名和行号：
```
2025-08-21 06:08:30.037 [DefaultQuartzScheduler_Worker-8] INFO  easitline-jdbc - [3ms] select /*+ MAX_EXECUTION_TIME(10000) */ distinct TASK_ID from ycbusi_gdzh.CC_CALL_RECORD   where 1=1   and CALL_TIMESTAMP > ?   and TASK_ID is not null,params[{1755727680}],source[yc-yjmanage.yc-read-ds] - com.yunqu.yc.yimanage.service.RobotCallTaskStatService.getNeedDealTask(RobotCallTaskStatService.java:120)
```

## 根本原因分析
1. **系统使用Log4j2Impl**：通过ServerContext.isLog4j2()返回true确认
2. **异步日志位置信息问题**：Log4j2的异步日志默认不包含位置信息以提高性能
3. **配置不完整**：缺少强制启用位置信息的配置

## 修复方案

### 1. 修改Log4j2Impl.java
**文件路径**: `src/org/easitline/common/core/log/impl/Log4j2Impl.java`

**修改1**: 静态初始化块添加位置信息配置
```java
static {
    System.setProperty("Log4jContextSelector","org.apache.logging.log4j.core.async.AsyncLoggerContextSelector");
    System.setProperty("log4j2.enableThreadLocals","true");
    // 启用位置信息，虽然会影响性能但能显示类名和行号
    System.setProperty("log4j2.includeLocation","true");
    // 对于异步日志，强制包含位置信息
    System.setProperty("AsyncLogger.includeLocation","true");
}
```

**修改2**: 改进PatternLayout格式
```java
// 设置Layout - 使用更兼容的格式确保类名和行号显示
PatternLayout layout = new PatternLayout();
// 修改格式：%c{1}显示类名，%M显示方法名，%F:%L显示文件名和行号
layout.setConversionPattern("%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5p %c{1}.%M(%F:%L) - %m%n");
fileAppender.setLayout(layout);
```

### 2. 更新log4j2.component.properties
**文件路径**: `src/resource/log4j2.component.properties`

```properties
log4j2.includeLocation=true
Log4jContextSelector=org.apache.logging.log4j.core.async.AsyncLoggerContextSelector
# 强制异步日志包含位置信息
AsyncLogger.includeLocation=true
# 启用线程本地变量以提高性能
log4j2.enableThreadLocals=true
```

### 3. 修改log4j.properties配置文件
**文件路径**: `src/resource/log4j.properties` 和 `resource/log4j.properties`

```properties
log4j.rootLogger=DEBUG,console

log4j.appender.console=org.apache.log4j.ConsoleAppender
log4j.appender.console.layout=org.apache.log4j.PatternLayout
# 修改控制台输出格式，包含类名和行号
log4j.appender.console.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5p %c{1} (%F:%L) - %m%n

log4j.appender.file=org.apache.log4j.RollingFileAppender
log4j.appender.file.File=logs/${project.name}.log
log4j.appender.file.layout=org.apache.log4j.PatternLayout
# 修改文件输出格式，包含类名和行号
log4j.appender.file.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5p %c{1} (%F:%L) - %m%n
```

### 4. 新增log4j2.xml配置文件
**文件路径**: `src/resource/log4j2.xml`

创建了标准的Log4j2 XML配置文件，强制启用位置信息：
```xml
<Configuration status="WARN">
    <Properties>
        <Property name="LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} [%t] %-5level %logger{36} (%F:%L) - %msg%n</Property>
    </Properties>
    <!-- ... 其他配置 ... -->
    <Root level="INFO" includeLocation="true">
        <AppenderRef ref="Console"/>
        <AppenderRef ref="RollingFile"/>
    </Root>
</Configuration>
```

### 5. 修复Maven资源目录结构
- 创建了正确的`src/resource`目录
- 将配置文件从`resource`移动到`src/resource`
- 确保Maven能正确打包资源文件

## 修改后的日志格式
新的日志输出格式将包含：
- 时间戳（精确到毫秒）
- 线程名
- 日志级别
- 类名.方法名(文件名:行号)
- 日志消息

示例输出：
```
2025-08-21 17:18:30.123 [main] INFO  LogFormatTest.testLogFormat(LogFormatTest.java:25) - This is INFO level log - should show class name and line number
```

## 性能影响说明
启用位置信息会对性能产生一定影响，因为需要获取调用栈信息。但这对于调试和问题排查非常重要。如果在生产环境中性能要求极高，可以考虑：
1. 只在DEBUG/INFO级别启用位置信息
2. 使用条件配置根据环境动态启用/禁用

## 验证方法
1. 重新编译项目：`mvn clean compile`
2. 打包项目：`mvn package -DskipTests`
3. 在应用中使用LogEngine创建日志实例并输出日志
4. 检查日志文件是否包含类名和行号信息

## 文件清单
修改的文件：
- `src/org/easitline/common/core/log/impl/Log4j2Impl.java`
- `src/resource/log4j2.component.properties`
- `src/resource/log4j.properties`
- `resource/log4j.properties`

新增的文件：
- `src/resource/` 目录结构
- `src/resource/log4j2.xml`
- `LOG_FORMAT_FIX_SUMMARY.md` (本文档)

## 多层次修复策略
为了确保日志格式修复生效，我们采用了多层次的修复策略：

1. **LogEngine层面**：修改Log4j2Impl和Log4jImpl的格式配置
2. **配置文件层面**：修改log4j.properties和log4j2.component.properties
3. **XML配置层面**：新增log4j2.xml文件作为最高优先级配置
4. **系统属性层面**：在代码中设置系统属性强制启用位置信息

这样可以确保无论应用使用哪种日志配置方式，都能显示类名和行号。

## 注意事项
1. 此修改只影响通过LogEngine创建的日志实例
2. 系统级别的日志（如catalina.out）可能需要额外配置
3. 建议在测试环境验证后再部署到生产环境
