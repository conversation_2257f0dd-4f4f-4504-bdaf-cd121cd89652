package org.easitline.common.api;

import java.util.HashMap;
import java.util.Map;

import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;

import com.alibaba.fastjson.JSONObject;

public abstract class ApiIService extends IService {
	
	protected Map<String, String> methodMap = new HashMap<>();
	
	@Override
	public JSONObject invoke(JSONObject jsonParam) throws ServiceException {
		
		initMethodMap();
		
		JSONObject  jsonObject = new JSONObject();
		
		ClassLoader cl = this.getClass().getClassLoader();
		String method = jsonParam.getString("method");
		String methodClass = methodMap.get(method);
		try {
			if(methodClass!=null){
				ApiServiceMethod apiMethod = (ApiServiceMethod)cl.loadClass(methodClass).getConstructor(JSONObject.class).newInstance(jsonParam);
				return apiMethod.invoke();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		return jsonObject;
	}

	public abstract void initMethodMap();
}
