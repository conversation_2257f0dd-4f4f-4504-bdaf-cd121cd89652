package org.easitline.common.core.activemq;

import java.io.Serializable;

import javax.jms.JMSException;

/**
 * 
 * MQ消息代理
 * 
 * 主要实现消息的发送和监听,消息队列通过采用ActiveMQ来实现<br>
 * 消息队列的配置文件为：broker-config.xml<br>
 *创建Broker通过BrokcerFactory工厂类进行创建,具体参考：<br>
 *<br>
 *创建本地容器内的Broker：<br>
 *Broker broker = BrokcerFactory.getQueueBroker(brokerName);<br>
 * 
 *创建远程的Broker：<br>
 *Broker broker =
 * BrokcerFactory.getQueueBroker("tcp://172.16.54.188:61616",brokerName,账号,密码);<br>
 * 
 **创建 带Failover功能的远程Broker：<br>
 *--指定多个队列 <br>
 *String[] connector = new
 * String[]{"tcp://172.16.54.188:61616","tcp://172.16.54.188:61617"};<br>
 *Broker broker = BrokcerFactory.getQueueBroker(connector,brokerName,账号,密码);<br>
 * <br>
 * 
 * 
 * 
 * @see BrokerFactory
 * 
 * <AUTHOR>
 * 
 */
public interface Broker {
	
	
	public static final int CONNECTOR_TYPE_PRODUCER = 1;
	
	public static final int CONNECTOR_TYPE_CONSUMER = 2;
	
	public static final int CONNECTOR_TYPE_TOPIC_PRODUCER = 3;
	
	public static final int CONNECTOR_TYPE_TOPIC_CONSUMER = 4;

	/**
	 * 发送消息
	 * 
	 * @param messageObj
	 *            需要实现 java.io.Serializable接口
	 * 
	 * @throws JMSException
	 *             发送失败
	 */
	public void sendMessage(Serializable messageObj) throws JMSException;

	/**
	 * 注册消息监听器，当队列中有消息是，MQ将主动回调BrokerMessageListener的实现类来进行消息处理。
	 * 注：可以根据业务的性能的需要，可注册多个消息监听器。
	 * 
	 * @param messageListener
	 *            继承com.suntek.app.vms.notifyserver.activemq.BrokerMessageListener
	 * @throws JMSException
	 *             注册失败，则抛出JMSException。
	 */
	public void registMessageListener(BrokerMessageListener messageListener)
			throws JMSException;

	/**
	 * 获得上下文
	 * 
	 * @return
	 */
	public BrokerContext getContext();
	
	
	public boolean setComsumerMode();

	/**
	 * 关闭JMS的相关链接
	 */
	public void close();

}
