package org.easitline.common.api;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.framework.vo.Department;
import org.easitline.common.core.framework.vo.rowmapper.DepartmentRowMapper;
import org.easitline.common.db.EasyQuery;

import com.alibaba.fastjson.JSONObject;

public abstract class BaseApiServiceMethod implements ApiServiceMethod {
	protected JSONObject paramsJson;
	protected JSONObject result;
	protected JSONObject data;
	protected ApiHeader apiHeader;
	
	protected int pageNo;
	protected int pageSize;
	
	protected EasyQuery getQuery() {
		return ServerContext.getAdminQuery();
	}
	
	protected BaseApiServiceMethod(JSONObject paramsJson){
		this.paramsJson = paramsJson; 
		pageNo = paramsJson.getIntValue("pageNo");
		pageSize = paramsJson.getIntValue("pageSize");
		
		result = new JSONObject();
		data = new JSONObject();
		
		apiHeader = ApiHeader.prase(paramsJson);
	}
	
	/**
	 * 获取当前用户的所属部门
	 * @return
	 */
	protected Department getDepartment(){
		String sql = "SELECT D.* FROM EASI_DEPT D "
				+" INNER JOIN EASI_DEPT_USER DU ON D.DEPT_ID = DU.DEPT_ID "
				+" INNER JOIN EASI_USER U ON DU.USER_ID = U.USER_ID "
				+" WHERE U.USER_ID = ?";
		try {
			Department dept = getQuery().queryForRow(sql, new Object[]{apiHeader.curUserId},new DepartmentRowMapper());
			return dept;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
}
