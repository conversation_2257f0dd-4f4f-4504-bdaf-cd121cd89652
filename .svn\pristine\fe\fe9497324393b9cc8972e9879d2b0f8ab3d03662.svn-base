package org.easitline.common.core.activemq.impl.ctgmq;

import java.io.Serializable;


import java.util.Properties;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageConsumer;
import javax.jms.MessageProducer;

import org.easitline.common.core.activemq.Broker;
import org.easitline.common.core.activemq.BrokerContext;
import org.easitline.common.core.activemq.impl.ObjectMessageImpl;
import org.easitline.common.core.activemq.impl.proxy.ConsumerProxy;
import org.easitline.common.core.activemq.impl.proxy.ProducerProxy;
import org.easitline.common.core.activemq.log.ActivieMQLogger;
import org.easitline.common.core.context.ServerContext;

import com.ctg.mq.api.CTGMQFactory;
import com.ctg.mq.api.IMQProducer;
import com.ctg.mq.api.IMQPullConsumer;
import com.ctg.mq.api.IMQPushConsumer;
import com.ctg.mq.api.PropertyKeyConst;
import com.ctg.mq.api.enums.MQConsumeFromWhere;

/**
 * 消息队列上下文信息
 * 
 * 
 类型：ctgmq
服务地址:*************:9001;*************:9001  --这里是命名服务的地址，不是MQ本身的地址
账号：ivr
密码：r%GNq*Q8@kXkkO7v
集群名：paas-ctgmq-001  
租户ID：4120975

参考一下MQ配置参数：

brokerName=paas-ctgmq-001_broker_2
tenantID=4120975   --租户ID
bdbEnable=false
deleteWhen=4
storeNodeName=broker_20_storeName
brokerId=0
maxMessageSize=5242880
autoCreateTopicEnable=false   --是否自动创建队列
autoCreateSubscriptionGroup=false  --是否自动创建主题
replicaSyncPolicy=WRITE_NO_SYNC
fileReservedTime=168
replicaAckPolicy=SIMPLE_MAJORITY
serverSeq=20
localSyncPolicy=WRITE_NO_SYNC
flushDiskType=ASYNC_FLUSH
msgTraceLogEnable=false
storePathCommitLog=/app/ctgmq//data/broker/paas-ctgmq-001/20/store/commitlog
storePathRootDir=/app/ctgmq//data/broker/paas-ctgmq-001/20/store
brokerRole=SYNC_MASTER
useReentrantLockWhenPutMessage=true
brokerIP2=*************
brokerIP1=*************
pathPerfix=/app/ctgmq/
listenPort=9201
sendMessageThreadPoolNums=32
waitTimeMillsInSendQueue=1000
namesrvAddr=*************\:9001;*************\:9001
serverWorkerThreads=8
brokerClusterName=paas-ctgmq-001   --集群名称
prodInstId=870047497144239104

 * 
 * <AUTHOR>
 * 
 */
public class CtgmqBrokerContext extends BrokerContext{
	
	

	/**
	 * 消息的生产者
	 */
	private ProducerProxy producer;

	/**
	 * 消息的消费者
	 */
	private ConsumerProxy consumer;
	
	
	/**
	 * 消息的生产者
	 */
	private IMQProducer ctgProducer;

	/**
	 * 消息的消费者
	 */
	private IMQPullConsumer ctgConsumer;
	

	public CtgmqBrokerContext(Integer connectorType, String connector, String brokerName, String username,String password) {
		super(connectorType, connector, brokerName, username, password);
		
		// TODO Auto-generated constructor stub
	}

	@Override
	public void init() {
		ActivieMQLogger.getLogger().info("CtgmqBrokerContext.init(), connectorType -> "+ this.getConectorTypeName());
		try {
			if(this.getConnectorType() == Broker.CONNECTOR_TYPE_CONSUMER) {
				if(this.ctgConsumer == null) 	this.initQueueConsumer();
			}
			
			if(this.getConnectorType() == Broker.CONNECTOR_TYPE_PRODUCER) {
				if(this.ctgProducer == null) 	this.initProducer();
			}
			
			if(this.getConnectorType() == Broker.CONNECTOR_TYPE_TOPIC_CONSUMER) {
				if(this.ctgConsumer == null) 	this.initTopicConsumer();
			}
			
			if(this.getConnectorType() == Broker.CONNECTOR_TYPE_TOPIC_PRODUCER) {
				if(this.ctgProducer == null)   this.initProducer();
			}
		
		} catch (Exception ex) {
			ActivieMQLogger.getLogger().error(ex,ex);
		}
	}
	
	
	/**
	 * CTGMQ在创建队列的时候不区分队列类型是队列还是主题，根据消息拉取来区分。
	 * @throws Exception
	 */
	private void initQueueConsumer() throws Exception{
		
		Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.ConsumerGroupName,  this.getConsumerGroup());
        properties.setProperty(PropertyKeyConst.NamesrvAddr, this.getConnector());
        properties.setProperty(PropertyKeyConst.NamesrvAuthID, this.getUsername());
        properties.setProperty(PropertyKeyConst.NamesrvAuthPwd, this.getPassword()); 
        properties.setProperty(PropertyKeyConst.ClusterName, this.getClusterName());
        properties.setProperty(PropertyKeyConst.TenantID, this.getTenantId());
        properties.setProperty(PropertyKeyConst.ClusterConsume, "CLUSTERING");
        //properties.setProperty(PropertyKeyConst.ConsumeTimeoutInSec, "3");
        properties.setProperty(PropertyKeyConst.ConsumeFromWhere,MQConsumeFromWhere.CONSUME_FROM_FIRST_OFFSET.name());
        
        ActivieMQLogger.getLogger().info("initQueueConsumer["+this.getBrokerName()+"] properties -> "+properties);
        ctgConsumer = CTGMQFactory.createPullConsumer(properties);
        ActivieMQLogger.getLogger().info("initQueueConsumer["+this.getBrokerName()+"] ctgConsumer -> "+ctgConsumer);
        int connectResult = ctgConsumer.connect();
        ActivieMQLogger.getLogger().info("initQueueConsumer["+this.getBrokerName()+"] connectResult -> "+connectResult);
        if (connectResult != 0) {
            return;
        }
        
        CtgmqMessageConsumer _consumer = new CtgmqMessageConsumer(this,ctgConsumer,CtgmqMessageConsumer.CONSUMER_TYPE_QUEUE);
        if(this.consumer == null){
        	this.consumer = new ConsumerProxy(this,_consumer);
        }else{
        	((ConsumerProxy)this.consumer).setConsumer(_consumer);
        }

	}
	
	
	/**
	 * CTGMQ在创建队列的时候不区分队列类型是队列还是主题，根据消息拉取来区分。
	 * @throws Exception
	 */
	private void initTopicConsumer() throws Exception{
		
		Properties properties = new Properties();
		//订阅的时候，消费组需要设置成一样的组。
        properties.setProperty(PropertyKeyConst.ConsumerGroupName, this.getConsumerGroup());
        //链接地址，填写命名服务的地址。
        properties.setProperty(PropertyKeyConst.NamesrvAddr, this.getConnector());
        //账号
        properties.setProperty(PropertyKeyConst.NamesrvAuthID, this.getUsername());
        //密码
        properties.setProperty(PropertyKeyConst.NamesrvAuthPwd, this.getPassword()); 
        //集群名称
        properties.setProperty(PropertyKeyConst.ClusterName, this.getClusterName());
        //租户
        properties.setProperty(PropertyKeyConst.TenantID, this.getTenantId());
        //properties.setProperty(PropertyKeyConst.ConsumeTimeoutInSec, "3");
        properties.setProperty(PropertyKeyConst.ConsumeFromWhere,MQConsumeFromWhere.CONSUME_FROM_FIRST_OFFSET.name());
        
        properties.setProperty(PropertyKeyConst.ClusterConsume, "CLUSTERING");
        
        ActivieMQLogger.getLogger().info("initTopicConsumer["+this.getBrokerName()+"] properties -> "+properties);
		
        ctgConsumer = CTGMQFactory.createPullConsumer(properties);
        ActivieMQLogger.getLogger().info("initTopicConsumer["+this.getBrokerName()+"] ctgConsumer -> "+ctgConsumer);
        int connectResult = ctgConsumer.connect();
        ActivieMQLogger.getLogger().info("initTopicConsumer["+this.getBrokerName()+"] connectResult -> "+connectResult);
        if (connectResult != 0) {
        	ActivieMQLogger.getLogger().error("初始化["+this.getBrokerName()+"]IMQPushConsumer 失败，执行结果："+connectResult);
            return;
        }else{
        	ActivieMQLogger.getLogger().error("初始化["+this.getBrokerName()+"]IMQPushConsumer 成功，执行结果："+connectResult);
        }
        
        CtgmqMessageConsumer _consumer = new CtgmqMessageConsumer(this,ctgConsumer,CtgmqMessageConsumer.CONSUMER_TYPE_QUEUE);
        if(this.consumer == null){
        	this.consumer = new ConsumerProxy(this,_consumer);
        }else{
        	((ConsumerProxy)this.consumer).setConsumer(_consumer);
        }
        try {
			Thread.sleep(3000);
		} catch (Exception ex) {
			// TODO: handle exception
		}
        
	}
	
	
	
	private void initProducer()throws Exception{
		
		Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.ProducerGroupName,getProducerInstanceName());
        properties.setProperty(PropertyKeyConst.NamesrvAddr, this.getConnector());
        properties.setProperty(PropertyKeyConst.NamesrvAuthID, this.getUsername());
        properties.setProperty(PropertyKeyConst.NamesrvAuthPwd, this.getPassword()); 
        properties.setProperty(PropertyKeyConst.ClusterName, this.getClusterName());
        properties.setProperty(PropertyKeyConst.TenantID, this.getTenantId());

		
        ctgProducer =  CTGMQFactory.createProducer(properties);
        ActivieMQLogger.getLogger().info("initProducer["+this.getBrokerName()+"] -> "+ctgProducer);
        int connectResult = ctgProducer.connect();
        ActivieMQLogger.getLogger().info("impProducer["+this.getBrokerName()+"].connect() -> "+connectResult);
        if(connectResult != 0){
            return;
        }
        CtgmqMessageProducer _producer = new CtgmqMessageProducer(this,ctgProducer);
        
        if(this.producer == null){
        	this.producer = new ProducerProxy(this,_producer);
        }else{
        	((ProducerProxy)this.producer).setProducer(_producer);
        }
	}

	@Override
	public void close() {

	}

	@Override
	public MessageProducer getProducer() {
		ActivieMQLogger.getLogger().info("getProducer("+this.getBrokerName()+") -> "+this.producer);
		return this.producer;
	}

	@Override
	public MessageConsumer getConsumer() {
		ActivieMQLogger.getLogger().info("getConsumer("+this.getBrokerName()+") -> "+this.consumer);
		return this.consumer;
	}


	@Override
	public Message createObjectMessage(Serializable messageObj) throws JMSException {
		return new ObjectMessageImpl(messageObj);
	}

	@Override
	public String getConnector() {
		return  this.connector;
	}

	@Override
	public String getBrokerName() {
		return this.brokerName;
	}

	@Override
	public void reload() {
		ActivieMQLogger.getLogger().info("重新加载消息队列["+this.brokerName+"]..");
		try {
			if (this.ctgConsumer != null)
				this.ctgConsumer.close();
		} catch (Exception ex) {
			ActivieMQLogger.getLogger().error(ex, ex);
		}
		try {
			if (this.ctgProducer != null)
				this.ctgProducer.close();
		} catch (Exception ex) {
			ActivieMQLogger.getLogger().error(ex, ex);
		}
		this.ctgConsumer = null;
		this.ctgProducer = null;
		this.init();
	}
	
}
