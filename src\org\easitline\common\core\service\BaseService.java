package org.easitline.common.core.service;

import org.easitline.common.db.EasyQuery;

public abstract class BaseService {

	/**
	 * 获得根据当前的应用名和数据源名获得查询对象
	 * @return
	 */
	protected  EasyQuery getQuery() {
		
		if("default-ds".equals(getAppDatasourceName())){
			return EasyQuery.getQuery(this.getAppDatasourceName());
		}else{
			return EasyQuery.getQuery(this.getAppName(),this.getAppDatasourceName());
		}
	}
	
	/**
	 * 获得当前用用ID
	 * @return
	 */
	protected abstract String getAppName();
	
	/**
	 * 获得当前用户的日志名
	 * @return
	 */
	protected abstract String getLoggerName();
	
	/**
	 * 获得当前应用数据源名称
	 * @return
	 */
	protected abstract String getAppDatasourceName();
	
}
