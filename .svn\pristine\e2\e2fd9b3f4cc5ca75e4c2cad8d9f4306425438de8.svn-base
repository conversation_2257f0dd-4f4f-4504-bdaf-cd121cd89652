package org.easitline.common.core.cache;
import org.easitline.common.core.cache.impl.DefaultEasyCacheImpl;
import org.easitline.common.core.cache.impl.MemcacheImpl;
import org.easitline.common.core.cache.impl.RedisImpl;
import org.easitline.common.core.context.ServerContext;

/**
 * Cache管理
 *
 */
public class CacheManager {
	
	/**
	 * 根据配置自动选择缓存对象(redis&memcache)
	 * @return
	 */
	public static  EasyCache  getCache(){
		return getMemcache();
	}
	
	public static  EasyCache  getMemcache(){
		if("redis".equals(ServerContext.getCacheType())){
			return RedisImpl.getInstance();
		}
		return MemcacheImpl.getInstance();
	}
	
	public static  EasyCache  getRedis(){
		return RedisImpl.getInstance();
	}
	public static EasyCache getDefaultCache(){
		return DefaultEasyCacheImpl.getInstance();
	}
	
	
}
