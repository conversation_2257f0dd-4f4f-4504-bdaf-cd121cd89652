package org.easitline.common.core.activemq.impl.activemq;

import javax.jms.Destination;



import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageProducer;
import javax.jms.ObjectMessage;


import org.easitline.common.core.activemq.BrokerContext;
import org.easitline.common.core.activemq.log.ActivieMQLogger;

public class ActiveMessageProducer implements MessageProducer  {
	
	
	private MessageProducer producer;
	
	private BrokerContext context;
	
	public ActiveMessageProducer(BrokerContext context,MessageProducer producer){
		this.context = context;
		this.producer = producer;
	}

	@Override
	public void close() throws JMSException {
	
	}

	@Override
	public int getDeliveryMode() throws JMSException {
		return 0;
	}

	@Override
	public Destination getDestination() throws JMSException {
		return null;
	}

	@Override
	public boolean getDisableMessageID() throws JMSException {
		return false;
	}

	@Override
	public boolean getDisableMessageTimestamp() throws JMSException {
		return false;
	}

	@Override
	public int getPriority() throws JMSException {
		return 0;
	}

	@Override
	public long getTimeToLive() throws JMSException {
		return 0;
	}

	@Override
	public void send(Message message) throws JMSException {
		
		try {
			ObjectMessage objectMessage =  (ObjectMessage)message;
			String messageText = objectMessage.getObject().toString();
			ActivieMQLogger.getLogger().info("ActiveMessageProducer.send("+context.getBrokerName()+") >> "+messageText);
			producer.send(message);
		} catch (JMSException ex) {
			ActivieMQLogger.getLogger().error(ex,ex);
			try {
				Thread.sleep(3000);
			} catch (Exception e) {
				// TODO: handle exception
			}
		}
		
	}

	@Override
	public void send(Destination arg0, Message arg1) throws JMSException {
		// TODO Auto-generated method stub

	}

	@Override
	public void send(Message arg0, int arg1, int arg2, long arg3) throws JMSException {
		// TODO Auto-generated method stub

	}

	@Override
	public void send(Destination arg0, Message arg1, int arg2, int arg3, long arg4) throws JMSException {
		// TODO Auto-generated method stub
	}

	@Override
	public void setDeliveryMode(int arg0) throws JMSException {
		this.producer.setDeliveryMode(arg0);

	}

	@Override
	public void setDisableMessageID(boolean arg0) throws JMSException {
		// TODO Auto-generated method stub

	}

	@Override
	public void setDisableMessageTimestamp(boolean arg0) throws JMSException {
		// TODO Auto-generated method stub

	}

	@Override
	public void setPriority(int arg0) throws JMSException {
		// TODO Auto-generated method stub

	}

	@Override
	public void setTimeToLive(long arg0) throws JMSException {
		this.producer.setTimeToLive(arg0);
	}
	
	@Override
	public String toString() {
		return "ActiveMessageProducer("+context.getBrokerName()+","+context.getConnector()+"),Producer对象名："+this.producer;
	}
	
}
