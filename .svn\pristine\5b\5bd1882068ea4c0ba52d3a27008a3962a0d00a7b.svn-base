package org.easitline.common.core.sso;

import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;

public class LoginSecurityImpl {
	
	private static String logName = "mars-sso";
	
	/**
	 * 是否开启登录验证
	 */
	private static  boolean verifyLogin=Boolean.valueOf(ServerContext.getProperties("verifyLogin","false"));
	/**
	 * 最大操作错误次数
	 */
	private static  int maxErrorLoginCount=Integer.valueOf(ServerContext.getProperties("maxErrorLoginCount","6"));
	/**
	 * 间隔多少分钟解锁
	 */
	private  static int unLockTime= Integer.valueOf(ServerContext.getProperties("unLockTime","10"));
	
	/**
	 * 间隔多少分钟内操作判断为连续操作
	 */
	private static int loginTimeLimit= Integer.valueOf(ServerContext.getProperties("loginTimeLimit","10"));
	
	/**
	 * 间隔多久修改一次密码
	 */
	private static int updatePwdLimit= Integer.valueOf(ServerContext.getProperties("updatePwdLimit","0"));
	
	/**
	 *  保存历史密码次数
	 */
	private static int hisPwdCount = Integer.valueOf(ServerContext.getProperties("hisPwdCount","4"));
	
	/**
	 * 开启首次登录修改密码验证
	 */
	private static  boolean firstLoginUpdatePwd= Boolean.valueOf(ServerContext.getProperties("firstLoginUpdatePwd","false"));
	
	/**
	 * 修改密码不一致次数
	 */
	private static  int pwdDif= Integer.valueOf(ServerContext.getProperties("pwdDif","0"));
	
	
	
	public static boolean failedLogin(String msg){
		LogEngine.getLogger(logName).error(msg);
		return false;
	}
	public  boolean failedLogin(String userAcct,String msg){
		EasyQuery query = ServerContext.getAdminQuery();
		EasyRecord record=new EasyRecord("EASI_USER_LOGIN_SECURITY","USER_ACCT");
		record.set("UPDATE_TIME",EasyDate.getCurrentDateString());
		record.set("LOGIN_MSG", msg);
		if(verifyLogin){
			try {
				String sql="SELECT * FROM EASI_USER_LOGIN_SECURITY WHERE USER_ACCT = ? ";
				JSONObject data=query.queryForRow(sql, new Object[]{userAcct},new JSONMapperImpl());
				SimpleDateFormat dfs = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				Date end = dfs.parse(EasyDate.getCurrentDateString());
				if(data==null||data.size()==0){
					record.setPrimaryValues(userAcct);
					record.set("LOGIN_FAILED_TIME",EasyDate.getCurrentDateString());
					record.set("LAST_UPDATE_PWD_TIME",EasyDate.getCurrentDateString());
					record.set("LOGIN_FAILED_COUNT",1);
					query.save(record);
				}else{
					record.setPrimaryValues(userAcct);
					record.set("LOGIN_FAILED_TIME",EasyDate.getCurrentDateString());
					String updateTime=data.getString("LOGIN_FAILED_TIME");
					if(StringUtils.isNotBlank(updateTime)){
						record.set("LOGIN_FAILED_TIME",EasyDate.getCurrentDateString());
						Date begin=dfs.parse(updateTime);
						long between=end.getTime()-begin.getTime();//除以1000是为了转换成秒
						if(between<loginTimeLimit*60*1000){//十分钟内
							int count=data.getIntValue("LOGIN_FAILED_COUNT")+1;
							record.set("LOGIN_FAILED_COUNT",count);
							if(count>1){
								record.set("LOGIN_MSG", "连续操作错误 "+count+"次,超过"+maxErrorLoginCount+"次账号将会被锁!");
							}
						}
					}else{
						record.set("LOGIN_FAILED_COUNT",1);
					}
					if(record.getIntValue("LOGIN_FAILED_COUNT")==maxErrorLoginCount){
						record.set("LOGIN_LOCK",1 );
						record.set("LOCK_TIME",EasyDate.getCurrentDateString());
						record.set("LOGIN_MSG", "账号被锁(连续操作错误"+maxErrorLoginCount+"次)"+unLockTime+"分钟后解锁!");
						query.update(record);
						return false;
					}
					if(record.getIntValue("LOGIN_FAILED_COUNT") > maxErrorLoginCount){
						Date lockTime=dfs.parse(data.getString("LOCK_TIME"));
						long lockBetween=end.getTime()-lockTime.getTime();
						long minute=unLockTime-lockBetween/60000;
						if(minute<0)minute=unLockTime;
						record.set("LOGIN_LOCK",1);
						record.set("LOGIN_MSG", "账号已被锁,"+minute+"分钟后解锁!");
						query.update(record);
						return false;
					}
					query.update(record);
				}
				
			} catch (Exception e) {
				LogEngine.getLogger(logName).error(e.getMessage(),e);
				return false;
			}
		}
		LogEngine.getLogger(logName).info("login_msg:"+JSONObject.toJSONString(record));
		return false;
	}
	
	public boolean unLock(String userAcct){
			if(verifyLogin==false)return true;
			EasyQuery query = ServerContext.getAdminQuery();
			try {
				String sql="select * from easi_user_login_security where USER_ACCT = ? ";
				JSONObject data=query.queryForRow(sql, new Object[]{userAcct},new JSONMapperImpl());
				EasyRecord record=new EasyRecord("easi_user_login_security","USER_ACCT");
				if(data==null||data.size()<=0){
					record.setPrimaryValues(userAcct);
					record.set("UPDATE_TIME",EasyDate.getCurrentDateString());
					record.set("LAST_UPDATE_PWD_TIME",EasyDate.getCurrentDateString());
					record.set("LOGIN_TIMES",1);
					query.save(record);
					return true;
				}
				if(data.getIntValue("LOGIN_LOCK")==1){
					SimpleDateFormat dfs = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
					Date lockTime=dfs.parse(data.getString("LOCK_TIME"));
					Date end = dfs.parse(EasyDate.getCurrentDateString());
					long lockBetween=end.getTime()-lockTime.getTime();
					if(lockBetween>1000*60*unLockTime){//三十分钟
						record.setPrimaryValues(userAcct);
						record.set("LOGIN_LOCK",0);
						record.set("LOGIN_FAILED_COUNT",0);
						record.set("LOGIN_TIMES",data.getIntValue("LOGIN_TIMES")+1);
						query.update(record);
						return true;
					}else{
						//还不能解锁
						long minute=((1000*60*unLockTime)-lockBetween)/60000;
						record.set("LOGIN_MSG", "账号已被锁,"+minute+"分钟后解锁!");
						query.update(record);
						return false;
					}
				}else{
					record.setPrimaryValues(userAcct);
					record.set("UPDATE_TIME",EasyDate.getCurrentDateString());
					record.set("LOGIN_TIMES",data.getIntValue("LOGIN_TIMES")+1);
					query.update(record);
				}
				LogEngine.getLogger(logName).info("unLock:"+JSONObject.toJSONString(record));
			} catch (Exception e) {
				LogEngine.getLogger(logName).error(e.getMessage(),e);
				return false;
			}
			return true;
	}
	/**
	 * 校验修改密码
	 * 到期密码提醒修改 第一次登录密码修改
	 * @param userAcct
	 * @return
	 */
	public static EasyResult verifyAccount(String userAcct){
		EasyResult result=new EasyResult();
		try {
			if(updatePwdLimit==0&&!firstLoginUpdatePwd){
				return result;
			}
			EasyQuery query = ServerContext.getAdminQuery();
			String sql="SELECT * FROM EASI_USER_LOGIN_SECURITY WHERE USER_ACCT = ? ";
			JSONObject data=query.queryForRow(sql, new Object[]{userAcct},new JSONMapperImpl());
			if(data!=null){
				SimpleDateFormat dfs = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				String lastUpdatePwdTime=data.getString("LAST_UPDATE_PWD_TIME");
				if(StringUtils.isNotBlank(lastUpdatePwdTime)){
					long nd = 1000 * 24 * 60 * 60;
					String currentTime=EasyDate.getCurrentDateString();
					Date lastUpdatePwdTimeDate=dfs.parse(lastUpdatePwdTime);
					Date currentTimeDate=dfs.parse(currentTime);
					long diff=currentTimeDate.getTime()-lastUpdatePwdTimeDate.getTime();
					long day = diff / nd;
					if(updatePwdLimit>0&&day>0&&day>=updatePwdLimit){
						result.setData(2);
						result.setErrorCode(302);
						result.addFail("离上次修改密码已超过["+day+"]天,请立即修改密码.");
						return result;
					}
				}
				int loginTimes=data.getIntValue("LOGIN_TIMES");
				if(firstLoginUpdatePwd&&loginTimes==1){
					result.setErrorCode(301);
					result.setData(1);
					result.addFail("第一次登录需要修改密码！");
					return result;
				}
			}
		} catch (Exception e) {
			LogEngine.getLogger(logName).error(e.getMessage(),e);
		}
		return result;
		
		
	}
	public static  EasyResult pwdLog(String userAcct,String newPwd){
		EasyResult result = new EasyResult();
		try {
			EasyQuery query = ServerContext.getAdminQuery();
			String sql="select * from easi_user_login_security where USER_ACCT = ? ";
			JSONObject data=query.queryForRow(sql, new Object[]{userAcct},new JSONMapperImpl());
			EasyRecord record=new EasyRecord("EASI_USER_LOGIN_SECURITY","USER_ACCT");
			record.set("HISTORY_PWDS",newPwd);  
			record.setPrimaryValues(userAcct);
			record.set("UPDATE_TIME",EasyDate.getCurrentDateString());
			record.set("LAST_UPDATE_PWD_TIME",EasyDate.getCurrentDateString());
			if(data!=null&&data.size()>0){
				String historyPwds = data.getString("HISTORY_PWDS");
				result.setData(historyPwds);
				if(StringUtils.notBlank(historyPwds)) {
					if(commaCount(historyPwds)>=hisPwdCount) {
						if(historyPwds.lastIndexOf(",")>-1) {
							historyPwds = historyPwds.substring(0,historyPwds.lastIndexOf(","));
						}
					}
					historyPwds = historyPwds+","+newPwd;
					record.set("HISTORY_PWDS",historyPwds);  
				}
				query.update(record);
			}else{
				query.save(record);
			}
		} catch (SQLException e) {
			LogEngine.getLogger(logName).error(e.getMessage(),e);
			result.addFail(e.getMessage());
		}
		return result;
	}
	
	private static int commaCount(String str) {
		int len1= str.length();
		int len2 = str.replaceAll(str, ",").length();
		return len1-len2;
	}
	
}
