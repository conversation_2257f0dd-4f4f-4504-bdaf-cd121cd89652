package org.easitline.common.core.framework.vo.rowmapper;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.easitline.common.core.framework.vo.User;
import org.easitline.common.db.EasyRowMapper;


/**

 * <AUTHOR>
 *
 */
public class UserRowMapper implements EasyRowMapper<User> {

	@SuppressWarnings("unchecked")
	@Override
	public User mapRow(ResultSet rs, int rowNum) {
		User vo = new User();
		try {
			vo.setUserId(rs.getString("USER_ID"));
			vo.setUsername(rs.getString("USERNAME"));
			vo.setNikeName(rs.getString("NIKE_NAME"));
			vo.setDepts(rs.getString("DEPTS"));
			vo.setMobile(rs.getString("MOBILE"));
			vo.setRoles(rs.getString("ROLES"));
			vo.setState(rs.getInt("STATE"));
			vo.setEmail(rs.getString("EMAIL"));
			vo.setPlaform(rs.getString("PLAFORM"));
			vo.setIdcard(rs.getString("IDCARD"));
			vo.setIpRange(rs.getString("IP_RANGE"));
			vo.setSex(rs.getString("SEX"));
			vo.setPicUrl(rs.getString("PIC_URL"));
			vo.setDescription(rs.getString("DESCRIPTION"));
			try {
				vo.setUserAcct(rs.getString("USER_ACCT"));
			} catch (Exception ex) {
			}
			
			vo.setImei(rs.getString("IMEI"));
			vo.setData1(rs.getString("DATA1"));
			vo.setData2(rs.getString("DATA2"));
			vo.setData3(rs.getString("DATA3"));
			vo.setData4(rs.getString("DATA4"));
			vo.setData5(rs.getString("DATA5"));
			vo.setData6(rs.getString("DATA6"));
			vo.setData7(rs.getString("DATA7"));
			vo.setData8(rs.getString("DATA8"));
			vo.setData9(rs.getString("DATA9"));
			vo.setData10(rs.getString("DATA10"));

		} catch (SQLException ex) {
			ex.printStackTrace();
		}
		return vo;
		
	}

}
