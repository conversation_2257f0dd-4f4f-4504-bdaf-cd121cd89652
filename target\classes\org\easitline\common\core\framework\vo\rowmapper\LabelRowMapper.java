package org.easitline.common.core.framework.vo.rowmapper;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.easitline.common.core.framework.vo.Label;
import org.easitline.common.db.EasyRowMapper;

public class LabelRowMapper implements  EasyRowMapper<Label>{

	@Override
	public Label mapRow(ResultSet rs, int rowNum) {
		// TODO Auto-generated method stub
		Label vo = new Label();
		try {
			vo.setLabelId(rs.getString("LABEL_ID"));
			vo.setLabelName(rs.getString("LABEL_NAME"));
		} catch (SQLException ex) {
			ex.printStackTrace();
		}
		return vo;
		
	}

}
