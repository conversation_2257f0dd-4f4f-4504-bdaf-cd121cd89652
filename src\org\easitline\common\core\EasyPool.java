package org.easitline.common.core;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

import javax.sql.DataSource;

import org.easitline.common.core.context.ConfigContext;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.common.core.resource.DSResource;
import org.easitline.common.db.EasyRow;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.druid.filter.Filter;
import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.pool.DruidDataSourceFactory;
import com.alibaba.druid.wall.WallConfig;
import com.alibaba.druid.wall.WallFilter;
import com.alibaba.fastjson.JSONObject;

/**
 * Easitline 数据库连接池
 */
public class EasyPool {
	
	private  Map<String, DruidDataSource> datasources = new ConcurrentHashMap<String, DruidDataSource>();
	
	private  Map<String, String> dbTypes = new ConcurrentHashMap<String, String>();
	
	public static List<String> datasourceNames = new ArrayList<String>();

	// 单例模式
	private  static class Holder{
		private static EasyPool uniqueInstance = new EasyPool();
		
	}
	private void initPools(String datasourceName) throws SQLException {

		// 1. 读取所有的系统数据源
		String sql = "select * from EASI_DS_INFO ";
		
		if(datasourceName!=null){
			 sql = "select * from EASI_DS_INFO WHERE SYS_DS_NAME = '"+datasourceName+"'";
		}
		
		List<EasyRow> dsList = null;
		try {
			dsList = ServerContext.getSqliteQuery().queryForList(sql);
		} catch (SQLException ex) {
			CoreLogger.getPlatform().error("EasyEngine->初始化系统数据源失败,原因：+"+ex.getMessage(),ex);
			throw new SQLException("EasyEngine->初始化系统数据源失败,原因：+"+ex.getMessage());
		}

		// 2. 逐个初始化
		for (EasyRow easyRow : dsList) {
			DSResource ds = new DSResource();
			String ip = easyRow.getColumnValue("IP_ADDR");
			ds.setDatasourceName(easyRow.getColumnValue("SYS_DS_NAME"));
			ds.setMaxActive(Integer.parseInt(easyRow.getColumnValue("MAX_CONN")));
			String pwd=easyRow.getColumnValue("PASSWORD");
			if(pwd!=null&&pwd.startsWith("3DES_")){
				ds.setPassword(ConfigContext.desDecryptStr(pwd));
			}else{
				ds.setPassword(ConfigContext.decryptionStrWithPrefix(pwd,"DS"));
			}
			ds.setUsername(easyRow.getColumnValue("USERNAME"));
			ds.setMinIdle(Integer.parseInt(easyRow.getColumnValue("MIN_CONN")));
			ds.setDbMaster(easyRow.getColumnValue("DB_MASTER"));
			this.loadDbCredential(ds);
			String url = "";
			String driverName = "";
			String dsProperties = easyRow.getColumnValue("DS_PROPERTIES");
			String confType = easyRow.getColumnValue("CONF_TYPE");
			String dbType = easyRow.getColumnValue("DB_TYPE");
			String port = easyRow.getColumnValue("IP_PORT");
			String dbName = easyRow.getColumnValue("DB_NAME");
			String urlProperties = easyRow.getColumnValue("URL_PROPERTIES");
			String jdbcUrl=easyRow.getColumnValue("JDBC_URL");
			ds.setProperties(toProperties(dsProperties));
			
			if("advanced".equalsIgnoreCase(confType)) {
				driverName = easyRow.getColumnValue("DRIVER_CLASS");
				url = jdbcUrl;
			}else {
				if(StringUtils.isBlank(ip))continue;
				if (dbType.equals("MySql")) {
					driverName = "com.mysql.jdbc.Driver";
					url = "jdbc:mysql://" + ip + ":" + port + "/" + dbName;
					if(StringUtils.notBlank(urlProperties)){
						url=url+"?"+urlProperties;
					}else{
						url = url + "?characterEncoding=UTF-8&connectionCollation=utf8mb4_general_ci&serverTimezone=Hongkong&useSSL=false";
						//url= url+"?useUnicode=true&remarks=true&characterEncoding=UTF-8&serverTimezone=Hongkong&useSSL=false&allowMultiQueries=true&allowPublicKeyRetrieval=true";
					}
				}if (dbType.equals("MySql8")) {
					driverName = "com.mysql.cj.jdbc.Driver";
					url = "jdbc:mysql://" + ip + ":" + port + "/" + dbName;
					if(StringUtils.notBlank(urlProperties)){
						url=url+"?"+urlProperties;
					}else{
						url = url + "?characterEncoding=UTF-8&connectionCollation=utf8mb4_general_ci&serverTimezone=Hongkong&useSSL=false";
						//url= url+"?useUnicode=true&remarks=true&characterEncoding=UTF-8&serverTimezone=Hongkong&useSSL=false&allowMultiQueries=true&allowPublicKeyRetrieval=true";
					}
				} else if (dbType.equals("Oracle")) {
					driverName = "oracle.jdbc.driver.OracleDriver";
					if(ip.indexOf(".")==-1){
						url = "jdbc:oracle:oci8:@"+ip;
					}else if(ip.indexOf(",")>0){
						String[] ips = ip.split(",");
						url="*************************************************************************="+port+")(host="+ips[0]+"))(address=(protocol=tcp)(port="+port+")(host="+ips[1]+")))(load_balance=no)(connect_data=(FAILOVER_MODE=(TYPE=select)(METHOD=basic)(RETRIES=180)(DELAY=5))(server=dedicated)(service_name="+dbName+")))";
					}else{
						url="jdbc:oracle:thin:@//"+ip+":"+port+"/"+dbName;
					}
				} else if (dbType.equals("SqlServer")) {
					driverName = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
					url = "jdbc:sqlserver://" + ip + ":" + port + ";DatabaseName=" + dbName+";allowMultiQueries=true";
				}else if(dbType.equals("Sybase")){
					driverName = "com.sybase.jdbc4.jdbc.SybDriver";
					url="jdbc:sybase:Tds:"+ip+":"+port+"/"+dbName;
					if(StringUtils.notBlank(urlProperties)){
						url=url+"?"+urlProperties;
					}
				}else if(dbType.equals("Db2")){
					driverName = "com.ibm.db2.jcc.DB2Driver";
					url="jdbc:db2://"+ip+":"+port+"/"+dbName;
					if(StringUtils.notBlank(urlProperties)){
						url=url+"?"+urlProperties;
					}
				}else if(dbType.equals("oscar")){
					driverName = "com.oscar.Driver";
					url="jdbc:oscar://"+ip+":"+port+"/"+dbName;
					if(StringUtils.notBlank(urlProperties)){
						url=url+"?"+urlProperties;
					}
				}else if(dbType.equals("clickhouse")){
					driverName = "ru.yandex.clickhouse.ClickHouseDriver";
					url="jdbc:clickhouse://"+ip+":"+port+"/"+dbName;
					if(StringUtils.notBlank(urlProperties)){
						url=url+"?"+urlProperties;
					}
				}else if (dbType.equals("PostgreSql")){
					driverName = "org.postgresql.Driver";
					String[] ips = ip.split(",");
					String _ips = "";
					for(String str:ips){
						_ips = _ips+str+":"+port+",";
					}
					_ips = _ips.substring(0,_ips.length()-1);
					url="jdbc:postgresql://"+_ips+"/"+dbName+"?targetServerType=any&loadBalanceHosts=false";
				}else if (dbType.equals("OpenGauss")){
					driverName = "org.postgresql.Driver";
					url="jdbc:postgresql://"+ip+":"+port+"/"+dbName;
				}else if (dbType.equals("dameng")){
					driverName = "dm.jdbc.driver.DmDriver";
					if(port.equals("0")){
						url="jdbc:dm://"+ip+"?schema="+dbName;
					}else{
						url="jdbc:dm://"+ip+":"+port+"?schema="+dbName;
					}
				}
				if(ip.startsWith("jdbc")){
					url=ip;
				}
			}
			if(StringUtils.isBlank(url)) {
				throw new NullPointerException("jdbcUrl is blank");
			}
			ds.setDbType(dbType);
			ds.setDriverName(driverName);
			ds.setUrl(url);
			CoreLogger.getPlatform().info("jdbcUrl:"+url+",driverName:"+driverName+",dsProperties:"+dsProperties);
			CoreLogger.getPlatform().info("Username:"+ds.getUsername()+",Password:"+maskPassword(ds.getPassword())+",DbType:"+ds.getDbType());
			this.deploy(ds);
		}

	}
	
	private void loadDbCredential(DSResource ds) {
	    String dsName = ds.getDatasourceName();
	    String userKey = "db." + dsName + ".username";
	    String pwdKey = "db." + dsName + ".password";

	    String username = System.getProperty(userKey);
	    if (StringUtils.isNotBlank(username)) {
	        ds.setUsername(username);
	        CoreLogger.getPlatform().info("已从系统属性加载数据库用户: " + dsName);
	        System.clearProperty(userKey);
	    }

	    String password = System.getProperty(pwdKey);
	    if (StringUtils.isNotBlank(password)) {
	        ds.setPassword(password);
	        CoreLogger.getPlatform().info("已从系统属性加载数据库密码: " + dsName + " > "+maskPassword(password));
	        System.clearProperty(pwdKey);
	    }
	}

	private static String maskPassword(String password) {
	    if (password == null || password.length() <= 3) {
	        return "***";
	    }
	    return password.substring(0, password.length() - 3) + "***";
	}

	
	@SuppressWarnings("unchecked")
	private Map<String,String> toProperties(String jsonStr){
		try {
			if(StringUtils.isNotBlank(jsonStr)){
				return JSONObject.parseObject(jsonStr, Map.class);
			}
		} catch (Exception e) {
			CoreLogger.getPlatform().error(null,e);
		}
		return new HashMap<String,String>();
	}
	/**
	 * 获得当前已经部署的数据源
	 * @return
	 */
	public String[] listDatasources(){
		return this.datasources.keySet().toArray(new String[this.datasources.size()]);
	}
	
	public Map<String, String> dbTypes(){
		return dbTypes;
	}
	
    public String getDbType(String dsName) {
    	return dbTypes().get(dsName);
    }
	
	/**
	 *  获取全局单例对象
	 */
	public static EasyPool getInstance() {
	   if (Holder.uniqueInstance == null) {
		   Holder.uniqueInstance = new EasyPool();
       }
       return Holder.uniqueInstance;
	}
	
	/**
	 * 构造函数，私有，其他地方不能创建对象
	 */
	private EasyPool() {
		try {
			this.initPools(null);
		} catch (Exception ex) {
			CoreLogger.getPlatform().error("初始化数据源失败!",ex);
		}
	}

	/**
	 * 根据数据源名称，获取数据源对应的连接池
	 * 
	 * @param datasourceName -- 数据源名称
	 * @throws Exception
	 */
	public Connection getConnection(String sysDatasourceName) throws SQLException {
		DruidDataSource ds =  datasources.get(sysDatasourceName);
		if(ds == null){
			throw new SQLException("EasyEngine->获取数据库连接失败，原因：未找到系统数据源["+sysDatasourceName+"]配置信息！");
		}
		return ds.getConnection();
	}
	
	/**
	 * 获取mars配置的连接池信息
	 * @param sysDatasourceName
	 * @return
	 * @throws SQLException
	 */
	public DataSource  getDatasource(String sysDatasourceName)  throws SQLException{
		DruidDataSource ds =  datasources.get(sysDatasourceName);
		if(ds == null){
			throw new SQLException("EasyEngine->获取系统数据源失败，原因：未找到系统数据源["+sysDatasourceName+"]配置信息！");
		}
		return ds;
	}
	
	public DruidDataSource  getDruidDatasource(String sysDatasourceName)  throws SQLException{
		DruidDataSource ds =  datasources.get(sysDatasourceName);
		if(ds == null){
			throw new SQLException("EasyEngine->获取系统数据源失败，原因：未找到系统数据源["+sysDatasourceName+"]配置信息！");
		}
		return ds;
	}
	
	public boolean hasDatasource(String sysDatasourceName) {
		return datasources.containsKey(sysDatasourceName);
	}

	private WallFilter wallFilter(){
         WallFilter wallFilter = new WallFilter();
         wallFilter.setConfig(wallConfig());
         return wallFilter;
	}
	  
	 private WallConfig wallConfig() {
        WallConfig config = new WallConfig();
        config.setConditionAndAlwayTrueAllow(true);
        config.setSelectWhereAlwayTrueCheck(false);
        
        config.setMultiStatementAllow(true);//允许一次执行多条语句
        config.setNoneBaseStatementAllow(true);//允许一次执行多条语句
        CoreLogger.getPlatform().info("wallConfig:setConditionAndAlwayTrueAllow:true");
        return config;
    }
	  
	/**
	 * 部署新的数据源
	 * 
	 * @param dsresource
	 * @throws Exception
	 */
	public void deploy(DSResource dsresource) {
		if(dsresource == null) return ;
		//先卸载已经存在的数据源
		this.remove(dsresource.getDatasourceName()); 
		DruidDataSource newds;
		try {
			Class.forName(dsresource.getDriverName());
			Map<String, String> dsProp = dsresource.toMap();
			newds = (DruidDataSource) DruidDataSourceFactory.createDataSource(dsProp);
//			newds.setKeepAlive(true);
//			newds.setKeepAliveBetweenTimeMillis(600*1000);
			
			newds.setTestOnBorrow(true);    //每次获取链接时进行检查
			/**
			 * 如果连接空闲时间大于timeBetweenEvictionRunsMillis指定的毫秒，就会执行参数validationQuery指定的SQL来检测连接是否有效。
			 */
			newds.setTestWhileIdle(true);   
			/**
			 * 当连接池中的连接处于空闲（idle）状态时，Druid 连接池会记录当前时间，并设置一个定时任务。
			 * 这个定时任务会在 setTimeBetweenEvictionRunsMillis 设定的毫秒数后执行。
			 * 当定时任务触发时，Druid 连接池会遍历所有的空闲连接，并对每一个空闲连接执行有效性检查。
			 * 有效性检查通常是通过执行一个 SQL 查询来完成的。例如，常用的有效性检查 SQL 语句是 SELECT 1。
			 * 对于每个空闲连接，如果在规定的超时时间内（默认为 3 秒，可以通过 validationQueryTimeout 参数设置）完成了有效性检查，
			 * 并且检查结果为有效，那么这个连接将继续保持在连接池中。如果检查结果为无效，或者检查过程中发生了异常，Druid 连接池会关闭这个空闲连接。
			 */
			
			
			newds.setValidationQueryTimeout(1);  //检测连接是否有效的超时时间，单位是秒，-1 表示无限制
			/**
			 * 默认值为 30 分钟。当连接池中空闲的连接数大于 minIdle（最小空闲连接数），并且那部分连接的空闲时间大于 minEvictableIdleTimeMillis 时，
			 * 连接池会将那部分连接设置成 Idle 状态并关闭。
			 * 也就是说，如果一条连接 30 分钟都没有使用到，并且这种连接的数量超过了 minIdle，则这些连接就会被关闭了。
			 * 
			 * tzc:这个参数只针对超过空闲链接数的链接生效。
			 */
			newds.setMinEvictableIdleTimeMillis(60*1000);    
			/**
			 * 默认值为 7 小时。如果 minIdle 设置得比较大，连接池中的空闲连接数一直没有超过 minIdle，这时那些空闲连接是不是一直不用关闭？
			 * 当然不是，如果连接太久没用，数据库也会把它关闭。这时如果连接池不把这条连接关闭，系统就会拿到一条已经被数据库关闭的连接。
			 * 为了避免这种情况，Druid 会判断池中的连接如果空闲时间大于 maxEvictableIdleTimeMillis，也会强行把它关闭，
			 * 而不用判断空闲连接数是否小于 minIdle。
			 * tzc：这个是针对所有链接有关系， 的确存在一种可能，由于链接空闲，被数据库或者中间的网络的防火墙被关闭了。
			 */
			newds.setMaxEvictableIdleTimeMillis(120*1000);  
			//每60秒检查一次链接。 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒。默认值为 60000。
			newds.setTimeBetweenEvictionRunsMillis(60*1000); 
			
			newds.setQueryTimeout(180);  //最终会应用到 Statement 对象上，执行时如果超过此时间，则抛出 SQLException
			newds.setTransactionQueryTimeout(60);  //执行一个事务的超时时间，单位是秒 , 一个事务超长一定有问题!!!
			
//			newds.setConnectionErrorRetryAttempts(10);
//			newds.setTimeBetweenConnectErrorMillis(1000);
			
			newds.setLogAbandoned(true);
			newds.setRemoveAbandoned(true);
			newds.setRemoveAbandonedTimeout(300*1000);  //查询最长执行时间为5分钟，超过5分钟，设置 druid 强制回收连接的时限，单位是秒 从连接池获取到 connect 开始算起，超过此值后， Druid 将强制回收该连接
			newds.setName(dsresource.getDatasourceName());
			//newds.setUseUnfairLock(true);
			
			//缺省是-1，阻塞所有线程，这样会导致由于数据源阻塞原因，tomcat的线程被沾满从而系统不可用，fix by tzc ,20221207
			Object mwtcObj = dsProp.get("maxWaitThreadCount");
			if(mwtcObj!=null&&StringUtils.notBlank(mwtcObj.toString())) {
				newds.setMaxWaitThreadCount(Integer.parseInt(mwtcObj.toString()));
			}else {
				if(!dsresource.isMaster()){
					newds.setMaxWaitThreadCount(100);
				}else{
					newds.setMaxWaitThreadCount(1000);
				}
			}
			
			String filters = dsProp.get("filters");
			if(StringUtils.notBlank(filters)&&filters.contains("wall")) {
				if(DSResource.isSuppportWall(dsresource.getDbType())) {
					List<Filter> filterList = new ArrayList<>();
					filterList.add(this.wallFilter());
					newds.setProxyFilters(filterList);
				}
			}
	          
			Properties properties = new Properties();
			properties.setProperty("remarks", "true");
			properties.setProperty("useInformationSchema", "true");
			newds.setConnectProperties(properties);
			
			
			this.deploy(dsresource.getDatasourceName(),dsresource.getDbType(),newds);
			
			CoreLogger.getPlatform().info("EasyEngine->成功加载数据源:"+dsresource.getDatasourceName());
			CoreLogger.getPlatform().info("数据源:"+dsresource.getDatasourceName()+">dsProp>"+dsresource.getFilters());
			CoreLogger.getPlatform().info("数据源:"+dsresource.getDatasourceName()+">Filters>"+newds.getProxyFilters());
//			CoreLogger.getPlatform().info("数据源:"+dsresource.getDatasourceName()+">连接超时connectTimeout>"+newds.getConnectTimeout()+",socketTimeout:"+newds.getSocketTimeout());
			
		} catch (Exception ex) {
			CoreLogger.getPlatform().error("EasyEngine->加载数据源失败:"+dsresource.getDatasourceName()+",原因:"+ex.getMessage(),ex);
		}
	}
	
	public void deploy(String dsName,DruidDataSource ds){
		deploy(dsName,null,ds);
	}
	
	public void deploy(String dsName,String dbType,DruidDataSource ds){
		try {
			this.remove(dsName);
			datasources.put(dsName, ds);
			datasourceNames.add(dsName);
			if(dbType!=null) {
				dbTypes.put(dsName,dbType);
			}
			CoreLogger.getPlatform().info("put:datasourceName:"+dsName+",dbType:"+dbType);
		} catch (Exception e) {
			CoreLogger.getPlatform().error(null,e);
		}
		
	}
	/**
	 * 更新新的数据源
	 * 
	 * @param dsresource
	 * @throws Exception
	 */
	public void update(String datasourceName) throws Exception {
		CoreLogger.getPlatform().info("当前配置数据源列表->"+this.datasources);
		if(datasourceName!=null) this.remove(datasourceName);
		this.initPools(datasourceName);
	}

	/**
	 * 删除数据源
	 * @param dsname
	 */
	public void remove(String datasourceName) {
		CoreLogger.getPlatform().info("删除数据源["+datasourceName+"]...");
		try{
			DruidDataSource ds = datasources.get(datasourceName);
			if(ds!=null&&!ds.isClosed()) {
				ds.close();
			}
			if(null != ds) datasources.remove(datasourceName);
			dbTypes.remove(datasourceName);
			datasourceNames.remove(datasourceName);
		} catch (Exception ex) {
			CoreLogger.getPlatform().error("删除数据源["+datasourceName+"]失败，原因："+ex.getMessage(),ex);
		}
	}
}
