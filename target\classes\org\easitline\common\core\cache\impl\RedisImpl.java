 package org.easitline.common.core.cache.impl;

import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.Set;
import org.easitline.common.core.cache.CacheTime;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.cache.JdkSerializer;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;

import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.JedisSentinelPool;


public class RedisImpl implements EasyCache {
	
	private JdkSerializer serializer = new JdkSerializer();
	
	private JedisPoolConfig jedisPoolConfig=new JedisPoolConfig();
	
	private JedisPool jedisPool = null;
	
	private JedisPool jedisPool1 = null;
	
	private JedisPool useJedisPool = null;
	
	private  JedisSentinelPool jSentinelPool = null;
	
	private JedisCluster cluster =null;
	
	private static String connType = null;
	//当前redis的可用状态
    private boolean useState = true;  // 缺省是正常，通过RedisHealthChecker线程来维护。
    //最后检测时间
    private long lastCheckTime = System.currentTimeMillis();
    
    private int checkErrCount = 0 ;
	
	private static RedisImpl redis = null;
	
	private static Object syncObj = new Object();
	
	private Thread healthThread = null;

	private RedisImpl(){
	}
	
	public static RedisImpl getInstance(){
		if(redis == null){
			synchronized (syncObj) {
				if(redis!=null) return redis;
				RedisImpl _redis = new RedisImpl();
				try {
					_redis.init();
				} catch (Exception ex) {
					CoreLogger.getRedis().error(ex.getMessage(),ex);
				}
				redis = _redis;
			}
		}
		return redis;
	}
	
	/**
	 * 默认三小时
	 */
	@Override
	public void put(String key, Object value) {
		this.put(key, value,CacheTime.HOUR*3);
	}

	@Override
	public void put(String key, Object value, int second) {
		key=getKey(key);
		
		if(cluster!=null){
			cluster.setex(serializer.keyToBytes(key),second, serializer.valueToBytes(value));
			return;
		}
		Jedis jedis=getJedis();
		try {
			if(jedis!=null){
			  jedis.setex(serializer.keyToBytes(key),second, serializer.valueToBytes(value));
			}
		}catch(Exception e){
			CoreLogger.getRedis().error(e.getMessage(),e);
		}finally{
			if(jedis!=null)jedis.close();
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public <T> T get(String key) {
		key=getKey(key);
		
		if(cluster!=null){
			byte[] value = cluster.get(serializer.keyToBytes(key));
			return (T) serializer.valueFromBytes(value);
		}
		Jedis jedis=getJedis();
		try {
			if(jedis!=null){
				byte[] value = jedis.get(serializer.keyToBytes(key));
				return (T) serializer.valueFromBytes(value);
			}else{
				return null;
			}
		}catch(Exception e){
			CoreLogger.getRedis().error(e.getMessage(),e);
		}finally{
			if(jedis!=null)jedis.close();
		}
		return null;
	}

	@Override
	public void flushAll() {
		if(cluster!=null){
			return;
		}
		Jedis jedis = getJedis();
		try {
			jedis.flushDB();
		}catch(Exception e){
			CoreLogger.getRedis().error(e.getMessage(),e);
		}finally {close(jedis);}
	}
	
	@Override
	public void delete(String key) {
		key=getKey(key);
		
		if(cluster!=null){
			cluster.del(serializer.keyToBytes(key));
			return;
		}
		Jedis jedis=getJedis();
		try {
			if(jedis!=null){
				jedis.del(serializer.keyToBytes(key));
			}
		}catch(Exception e){
			CoreLogger.getRedis().error(e.getMessage(),e);
		}finally{
			if(jedis!=null)jedis.close();
		}
	}
	
	public JedisCluster getCluster(){
		//fix by tzc , 在初始化的时候已经指定对象redis的模式，不能为空就重新创建! ,可能存在哨兵或者单节点模式getJedis()为空的时候调用该方法。  fix by tzc ,20240201
//		if(cluster==null){
//			init();
//		}
		return cluster;
	}
	
	public JedisPool  getUseJedisPool(){
		return this.useJedisPool;
	}
	
	/**
	 * 获得当前redis连接池的资源信息
	 * @return
	 */
	private String getPoolResourceInfo(){
		if(this.jSentinelPool!=null){
			int numIdle = jSentinelPool.getNumIdle();
			int numActive = jSentinelPool.getNumActive();
			int numWaiters = jSentinelPool.getNumWaiters();
			return "Master:"+jSentinelPool.getCurrentHostMaster()+",Idle: " + numIdle+",Active: " + numActive +",Waiting:" + numWaiters;
		}
		if(this.getUseJedisPool()!=null){
			int numIdle = this.getUseJedisPool().getNumIdle();
			int numActive = this.getUseJedisPool().getNumActive();
			int numWaiters = this.getUseJedisPool().getNumWaiters();
			return "Idle: " + numIdle+",Active: " + numActive +",Waiting:" + numWaiters;
		}
		return "";
	}
	
	public Jedis getJedis() {
		//如果检查已经30秒没有执行，不做处理。
		if(System.currentTimeMillis() - lastCheckTime < 30*1000){
			if(!this.useState){ //如果状态不可用
				CoreLogger.getRedis().error("健康线程检查redis连接池不可用[异常次数："+checkErrCount+"]，返回NULL，暂停提供方服务！ --"+getStackTrace());
				return null;
			}
		}
		Jedis jedis = null;
		long timer = System.currentTimeMillis();
		jedis = this.getJedisExt();
		timer = System.currentTimeMillis() - timer ;
		if(timer>200){
			CoreLogger.getRedis().warn("获取redis链接耗时"+timer+"ms，请检查redis状态是否正常!");
		}
		return jedis;
	}
	
	/**
	 * 从连接池中获取redis链接
	 * @param isCheckState  是否检查状态，如果是：这如果状态不可用的时候，直接返回空。
	 * @return
	 */
	private Jedis getJedisExt() {
		try {
			JedisPool _jedisPool = this.getUseJedisPool(); // 获得当前使用的redisPool;
			if(_jedisPool!=null){
				try {
					return  _jedisPool.getResource();
				} catch (Exception poolEx) {
					//如果不存在多节点的情况，这直接抛出异常
					if(jedisPool == null || jedisPool1 == null) throw poolEx;
					CoreLogger.getRedis().error("获取redis链接失败，原因："+poolEx.getMessage(),poolEx);
				}
				//如果由于获取redis的链接异常，切换redis节点    fix by tzc ,20230914;
				if(this.useJedisPool == jedisPool)  this.useJedisPool = jedisPool1;
				else  this.useJedisPool = jedisPool;
				
				_jedisPool = this.getUseJedisPool();
				if(_jedisPool!=null) return _jedisPool.getResource();
			}else if(jSentinelPool!=null){
				return jSentinelPool.getResource();
			}
		} catch (Exception ex) {  //redis不可用，代表redis服务器已经连接不上。
			CoreLogger.getRedis().error("获取redis连接失败，原因："+ex.getMessage(),ex);
		}
		return null;
    }
	
	private void  healthChecker(){
		if(healthThread != null) return;
		healthThread = new Thread(new RedisHealthChecker());
		healthThread.start();
	}
	
	@Override
	public void init() {
		CoreLogger.getRedis().info("Redis初始化调用栈：" + getStackTrace());
		stop();
		String addrInfo = ServerContext.getCacheAddr();
		if(StringUtils.isBlank(addrInfo)){
			return;
		}
		String redisConf= ServerContext.getProperties("redisConf","");
		CoreLogger.getRedis().info("init redis["+addrInfo+"]["+redisConf+"]...");
		JSONObject params=new JSONObject();
		if(StringUtils.notBlank(redisConf)){
			params=JSONObject.parseObject(redisConf);
		}else{
			params.put("auth", "");
			params.put("connType", "cluster");
		}
		connType = params.getString("connType");
		 //连接池中的最大空闲连接
        jedisPoolConfig.setMaxIdle(30);
        //连接池中的最大连接数
        jedisPoolConfig.setMaxTotal(300);
        //连接池中的最小空闲连接或初始化连接数
        jedisPoolConfig.setMinIdle(10);
        //获取连接时的最大等待毫秒数,小于零:阻塞不确定的时间,默认-1
        jedisPoolConfig.setMaxWaitMillis(2000);
        //每次释放连接的最大数目
        //jedisPoolConfig.setNumTestsPerEvictionRun(50);
        //释放连接的扫描间隔（毫秒）
       // jedisPoolConfig.setTimeBetweenEvictionRunsMillis(60*1000); 
        // 多长空闲时间之后回收空闲连接（毫秒）
        jedisPoolConfig.setMinEvictableIdleTimeMillis(60*1000); 
        //连接空闲多久后释放, 当空闲时间>该值 且 空闲连接>最大空闲连接数 时直接释放（毫秒）
        //jedisPoolConfig.setSoftMinEvictableIdleTimeMillis(10*1000); 
        //在获取连接的时候检查有效性, 默认false
        jedisPoolConfig.setTestOnBorrow(true);
        //在归还给pool时，是否提前进行validate操作
        jedisPoolConfig.setTestOnReturn(false); 
        //在空闲时检查有效性, 默认false,改为true，防止防火墙把空闲链接被关闭导致链接不可用。
        jedisPoolConfig.setTestWhileIdle(true);
        //每两分钟检查一下空闲链接是否可用。
        jedisPoolConfig.setTimeBetweenEvictionRunsMillis(120*1000);
        //参数是连接池驱逐循环中每次运行时检查的空闲连接的数量,一个常见的起点是设置 numTestsPerEvictionRun 为连接池大小的一个较小比例。例如，如果连接池大小为100，可以尝试设置为 10 或 20。然后，根据应用程序的性能和稳定性进行调整。
        jedisPoolConfig.setNumTestsPerEvictionRun(10);
        //连接耗尽时是否阻塞, false报异常,ture阻塞直到超时, 默认true
        jedisPoolConfig.setBlockWhenExhausted(true); 
        
        this.settingParams(params);
        addrInfo = addrInfo.replaceAll(" ", ";");
        addrInfo = addrInfo.replaceAll(",", ";");
        addrInfo = addrInfo.replaceAll("，", ";");
        addrInfo = addrInfo.replaceAll("；", ";");
		String[] addrArray = addrInfo.split(";");
		
		String user = params.getString("user");
		String auth = params.getString("auth");
		int database = params.getIntValue("database");
		int enableSSL=params.getIntValue("enableSSL");
		
		CoreLogger.getRedis().info("redis connection type["+connType+"]");
		if("cluster".equals(connType)){
			//集群
			Set<HostAndPort> nodes = new LinkedHashSet<HostAndPort>();
			for(String addr:addrArray){
				String host=addr.split(":")[0];
				int port=Integer.valueOf(addr.split(":")[1]);
				nodes.add(new HostAndPort(host,port));
			}
			if(nodes.size()>0){
				//soTimeout:返回值的超时时间,maxAttempts:出现异常最大重试次数
				if(StringUtils.isBlank(auth)){
					cluster = new JedisCluster(nodes,jedisPoolConfig);
				}else{
					if(StringUtils.notBlank(user)) {
						cluster = new JedisCluster(nodes, 5000,3000,10,user,auth,ServerContext.getNodeName(),jedisPoolConfig);
					}else {
						cluster = new JedisCluster(nodes, 5000,3000,10,auth,jedisPoolConfig);
						
					}
				}
			}
		}
		else if("single".equals(connType)){
			//单个
			if(addrArray.length>=1){
				String[] addr=addrArray[0].split(":");
				if(StringUtils.notBlank(auth)) {
					if(StringUtils.notBlank(user)) {
						jedisPool = new JedisPool(jedisPoolConfig, addr[0], Integer.valueOf(addr[1]),5000,user,auth,database,enableSSL==1);
					}else {
						jedisPool = new JedisPool(jedisPoolConfig, addr[0], Integer.valueOf(addr[1]),5000,auth,database,enableSSL==1);
					}
				}else{
					jedisPool = new JedisPool(jedisPoolConfig, addr[0], Integer.valueOf(addr[1]),enableSSL==1);
				}
				this.useJedisPool = jedisPool;
			}
			if(addrArray.length>=2){
				String[] addr=addrArray[1].split(":");
				if(StringUtils.notBlank(auth)) {
					if(StringUtils.notBlank(user)) {
						jedisPool1 = new JedisPool(jedisPoolConfig, addr[0], Integer.valueOf(addr[1]),5000,user,auth,database,enableSSL==1);
					}else {
						jedisPool1 = new JedisPool(jedisPoolConfig, addr[0], Integer.valueOf(addr[1]),5000,auth,database,enableSSL==1);
					}
				}else{
					jedisPool1 = new JedisPool(jedisPoolConfig, addr[0], Integer.valueOf(addr[1]),enableSSL==1);
				}
			}
			return;
		}
		else if("sentinel".equals(connType)){
			//哨兵模式
			String  masterName=params.getString("masterName");
			if(StringUtils.notBlank(masterName)){
				Set<String> sentinels = new HashSet<String>();
				for(String addr:addrArray){
					sentinels.add(addr);
				}
				if(StringUtils.isBlank(auth)){
					jSentinelPool = new JedisSentinelPool(masterName,sentinels,jedisPoolConfig);
				}else{
					if(StringUtils.notBlank(user)) {
						jSentinelPool = new JedisSentinelPool(masterName,sentinels,jedisPoolConfig,5000,user,auth,database);
					}else {
						jSentinelPool = new JedisSentinelPool(masterName,sentinels,jedisPoolConfig,auth);
					}
				}
				return;
			}else{
				CoreLogger.getRedis().error("哨兵模式缺少配置masterName");
			}
		}
		if(params.getIntValue("redisHealthChecker")==0) {
			healthChecker();
		}
	}

	private void settingParams(JSONObject params){
		int maxTotal = params.getIntValue("maxTotal");
		if(maxTotal>0){
			jedisPoolConfig.setMaxTotal(maxTotal);
		}
		int maxIdle = params.getIntValue("maxIdle");
		if(maxIdle>0){
			jedisPoolConfig.setMaxIdle(maxIdle);
		}
		int minIdle = params.getIntValue("minIdle");
		if(minIdle>0){
			jedisPoolConfig.setMinIdle(minIdle);
		}
		int maxWait = params.getIntValue("maxWait");
		if(maxWait>0){
			jedisPoolConfig.setMaxWaitMillis(maxWait);
		}
		long minEvictableIdleTimeMillis = params.getLongValue("minEvictableIdleTimeMillis");
		if(minEvictableIdleTimeMillis>0){
			jedisPoolConfig.setMinEvictableIdleTimeMillis(minEvictableIdleTimeMillis);
		}
		

	}
	
	@Override
	public void stop() {
		CoreLogger.getRedis().info("stop redis...");
		try {
			if(jedisPool!=null){
				jedisPool.close();
				jedisPool.destroy();
			}
		} catch (Exception e) {
			CoreLogger.getRedis().error(e);
		}
		
		try {
			if(jedisPool1!=null){
				jedisPool1.close();
				jedisPool1.destroy();
			}
		} catch (Exception e) {
			CoreLogger.getRedis().error(e);
		}
		
		try {
			if(jSentinelPool!=null){
				jSentinelPool.close();
				jSentinelPool.destroy();
			}
		} catch (Exception e) {
			CoreLogger.getRedis().error(e);
		}
		
		try {
			if(cluster!=null){
				cluster.close();
			}
		} catch (Exception e) {
			CoreLogger.getRedis().error(e);
		}
		jedisPool=null;
		jedisPool1=null;
		jSentinelPool=null;
		cluster=null;
		
	}
	
	public static String getConnType() {
		return connType;
	}
	
	public void reload(){
		 lastCheckTime = System.currentTimeMillis();
		 checkErrCount = 0 ;
  		 init();
	}
	
   public void close(Jedis jedis) {
		if (jedis != null){
			jedis.close();
		}
   }
   
	private  String getStackTrace(){
		StackTraceElement[] stackTrace  = Thread.currentThread().getStackTrace();
		if(stackTrace == null) return "";
		StackTraceElement e = null;
		int length = stackTrace.length;
		boolean impl = false;
		for(int i=0;i<length;i++){
			e = stackTrace[i];
			String className = e.getClassName();
			if(className.startsWith("org.easitline")){
				impl = true;
				continue;
			}
			if(impl){
				return e.toString();
			}
		}
		return e.toString();
	}
   
   private String getKey(String key){
	  // return ServerContext.getNodeName()+key;
	   return key;
   }
   
   public String randomKey() {
		Jedis jedis = getJedis();
		try {
			return jedis.randomKey();
		}catch(Exception e){
			CoreLogger.getRedis().error(e.getMessage(),e);
		}finally {close(jedis);}
		return null;
	}
   
   
	public void setTestWhileIdle(boolean testWhileIdle) {
		jedisPoolConfig.setTestWhileIdle(testWhileIdle);
	}
	
	public void setMinEvictableIdleTimeMillis(int minEvictableIdleTimeMillis) {
		jedisPoolConfig.setMinEvictableIdleTimeMillis(minEvictableIdleTimeMillis);
	}
	
	public void setTimeBetweenEvictionRunsMillis(int timeBetweenEvictionRunsMillis) {
		jedisPoolConfig.setTimeBetweenEvictionRunsMillis(timeBetweenEvictionRunsMillis);
	}
	
	public void setNumTestsPerEvictionRun(int numTestsPerEvictionRun) {
		jedisPoolConfig.setNumTestsPerEvictionRun(numTestsPerEvictionRun);
	}
	
	//执行redis健康检测
	private class RedisHealthChecker  implements Runnable{
		public void run() {
			try {
				Thread.sleep(10*1000);
			} catch (Exception e) {
				// TODO: handle exception
			}
			long checkpoint = 0;
			String firstErrTime = "";
			while(true){
				if(System.currentTimeMillis() - checkpoint > 600*1000){
					String info = getPoolResourceInfo();
					if(StringUtils.isNotBlank(info)){
						CoreLogger.getRedis().info("执行redis健康检查,当前连接池信息："+info);
					}else{
						CoreLogger.getRedis().info("执行redis健康检查...");
					}
					checkpoint = System.currentTimeMillis();
				}
				lastCheckTime = System.currentTimeMillis();
				Jedis jedis=getJedisExt();
				
				if(jedis!=null){
					firstErrTime = "";
					useState = true;
					checkErrCount = 0 ;
				}else{
					if(StringUtils.isBlank(firstErrTime)) firstErrTime = EasyCalendar.newInstance().getDateTime("-");
					checkErrCount++;
					CoreLogger.getRedis().info("执行redis健康检查异常，无法获取到redis的链接，异常[首次出现时间："+firstErrTime+"]次数："+checkErrCount);
				}
				if(checkErrCount>=3) {
					useState = false;
				}
				try {
					if(jedis!=null) jedis.close();
				}catch(Exception e){
				}
				try {
					Thread.sleep(3000);
				} catch (Exception e) {
					// TODO: handle exception
				}
			}
		}
	}

}




