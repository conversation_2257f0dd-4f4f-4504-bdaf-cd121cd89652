package org.easitline.common.core.sso;

import java.security.Principal;
import java.util.Map;
import java.util.Set;

import org.easitline.common.core.framework.vo.Department;
import org.easitline.common.core.framework.vo.User;


public interface UserPrincipal extends Principal {
	
	/**
	 * 获得SSO类型 1：默认sso  2：其它sso
	 * @return
	 */
	public int getSsoType();
	/**
	 * 获得当前登陆ID
	 * @return
	 */
	public String getUserId();

	/**
	 * 获得当前登陆账号
	 * @return
	 */
	public String getLoginAcct();

	/**
	 * 获取当前登陆账号类型
	 * @return
	 */
	public int getLoginType();

	/**
	 * 获取当前登陆用户名
	 * @return
	 */
	public String getUserName();
	
	/**
	 * 获取根ID
	 * @return
	 */
	public String getRootId();

	/**
	 * 获取当前登陆账号的别名
	 * @return
	 */
	public String getNikeName();
	
	/**
	 * 获得当前用户的联系电话
	 * @return
	 */
	public String getMobile();
	
	public String getEmail();
	
	/**
	 * 获得当前用户的登录IP地址
	 * @return
	 */
	public String getIpAddr();
	
	public boolean isSuperUser();
	
	/**
	 * 判断是否拥有该角色
	 * @return
	 */
	public boolean   isRole(String roleId);
	
	/**
	 * 判断是否拥有该资源
	 * @return
	 */
	public boolean   isResource(String resId);
	
	
	/**
	 * 获得用户所属单位,部门类型单位：SYS_DW
	 * @return
	 */
	public Department  getOwnerUnit();
	
	
	public Set<String> getRoles();
	
	public Set<String> getResources();
	
	
	/**
	 * 获得用户信息
	 * @return
	 */
	public User getUserInfo();
	
	/**
	 * 获得用户所在部门
	 * @return
	 */
	public Department getDepartment();
	
	/**
	 * 获得用户的登录时间
	 * @return
	 */
	public String getLoginTime();
	
	/**
	 * 获得在线时长
	 * @return
	 */
	public String getOnlineTime();
	
	public void setRootId(String rootId);
	
	public void setAttribute(String key,Object value);
	
	public  <T> T getAttribute(String key);
	
	public void removeAttribute(String key);
	
	public <T> T getAttribute(String key,Object defaultVal);
	
	public  Map<String, Object> toMap();
	
}
