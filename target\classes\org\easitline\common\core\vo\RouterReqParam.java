package org.easitline.common.core.vo;

import java.io.Serializable;

public class RouterReqParam implements Serializable {

	private static final long serialVersionUID = -673992050499121882L;
	
	private String serviceId;

	private String method;
	
	private String timestamp;
	
	private String format;
	
	private String app_key;
	
	private String version;
	
	private String sign;
	
	private String access_token;

	public RouterReqParam(String serviceId, String method, String timestamp, String format, String app_key, String version, String sign,
			String access_token) {
		super();
		this.serviceId = serviceId;
		this.method = method;
		this.timestamp = timestamp;
		this.format = format;
		this.app_key = app_key;
		this.version = version;
		this.sign = sign;
		this.access_token = access_token;
	}
	
	public String getServiceId() {
		return serviceId;
	}

	public void setServiceId(String serviceId) {
		this.serviceId = serviceId;
	}

	public String getMethod() {
		return method;
	}

	public void setMethod(String method) {
		this.method = method;
	}

	public String getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(String timestamp) {
		this.timestamp = timestamp;
	}

	public String getFormat() {
		return format;
	}

	public void setFormat(String format) {
		this.format = format;
	}

	public String getApp_key() {
		return app_key;
	}

	public void setApp_key(String app_key) {
		this.app_key = app_key;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getSign() {
		return sign;
	}

	public void setSign(String sign) {
		this.sign = sign;
	}

	public String getAccess_token() {
		return access_token;
	}

	public void setAccess_token(String access_token) {
		this.access_token = access_token;
	}	
}
