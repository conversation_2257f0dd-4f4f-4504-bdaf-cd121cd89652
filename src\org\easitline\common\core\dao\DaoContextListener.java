package org.easitline.common.core.dao;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.net.URLClassLoader;
import java.util.Enumeration;

import javax.servlet.ServletContext;
import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;

import org.easitline.common.annotation.engine.Parser;
import org.easitline.common.annotation.engine.TypeEntity;
import org.easitline.common.core.dao.impl.ApusicDaoContext;
import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.common.utils.string.StringUtils;
public  class DaoContextListener  implements ServletContextListener {

	public void contextInitialized(ServletContextEvent event)  { 
		
		
			 CoreLogger.getPlatform().info("deployAppDir->"+System.getProperty("deployAppDir"));
			 CoreLogger.getPlatform().info("deployDir->"+System.getProperty("deployDir"));
			 
			 //这里增加兼容apusic 和 东方通 应用服务器情况。
			 //东方通的环境变量： "tongweb.base": "/home/<USER>/domains/domain1",
			 //金蝶的环境变量："com.apusic.aas.instanceRoot": "/home/<USER>/aas/domains/mydomain",
			 
			 String deployDir = System.getProperty("deployAppDir");
			 
			 if(StringUtils.isBlank(deployDir)){
				 deployDir = System.getProperty("tongweb.base");
				 if(StringUtils.isNotBlank(deployDir)){
					 deployDir = deployDir + "/deployment";
				 }else{
					 deployDir = System.getProperty("com.apusic.aas.instanceRoot");
					 if(StringUtils.isNotBlank(deployDir)){
						 deployDir = deployDir + "/applications";
					 }
				 }
			 }

			if(StringUtils.isNotBlank(deployDir)){
				//String appDeployDir = StringUtils.replace(deployDir, "autodeploy", "applications");
				CoreLogger.getPlatform().info("Load apusic or tongweb["+ event.getServletContext().getContextPath()+","+deployDir+"] @WebObject ..." );
				ApusicDaoContext.loadDao(deployDir, event);
				return;
			}
		
			ServletContext servletContext= event.getServletContext();
	    	String contextPath = servletContext.getContextPath();
	    	String daoPath = servletContext.getInitParameter("daoPath");
	    	String packageName = servletContext.getInitParameter("packageName");
	    	
	    	if(StringUtils.isBlank(packageName)) {
	    		packageName = "/"; //从应用的根目录中获取资源信息
	    	}else {
	    		packageName = packageName.replace('.', '/');
	    	}
	    	
	    	CoreLogger.getPlatform().info("Load ["+contextPath+","+daoPath+"] @WebObject ..." );
	    	Enumeration<URL> dirs;
	    	long begin=System.currentTimeMillis();
			try {
				URLClassLoader uLoader = (URLClassLoader)Thread.currentThread().getContextClassLoader();
				dirs = uLoader.getResources(packageName);
				if(dirs == null){
					CoreLogger.getPlatform().error("Load ["+contextPath+"] @WebObject error,cause: resource[/] is null!" );
					return ;
				}
				while (dirs.hasMoreElements()) {
		    		URL url = dirs.nextElement();
		    		File dir = new File(url.getPath());
		    		if(dir != null) {
		    			if(dir.listFiles() == null) {
		    				CoreLogger.getPlatform().error("Load ["+dir.getPath()+"] @WebObject error,cause: dir.listFiles() is null!" );
		    				continue ;
		    			}
		    		}
		    		loadDirClass(dir,"",contextPath,daoPath);
		    	}
				long end=System.currentTimeMillis();
				CoreLogger.getPlatform().info("Load nend time is "+(end-begin)+"ms!");
			} catch (IOException ex) { 
				CoreLogger.getPlatform().error("Load ["+contextPath+"] @WebObject error,cause: "+ex.getMessage() );
				CoreLogger.getPlatform().error(null,ex);
			}
	    }
	    /**
	     * 从目录中加载类
	     * @param dir
	     * @param pkg
	     */
	   private    void loadDirClass(File dir,String pkg,String contextPath,String daoPath){
	    	if(dir != null){
	    		File[] files = dir.listFiles();
	    		if(files == null) return;
	    	
	    		for(File file:files){
	    			if(file.isDirectory()){
	    				String nextPackage = "";
	    				if(StringUtils.isBlank(pkg)){
	    					nextPackage = file.getName();
	    				}else{
	    					nextPackage = pkg + "." + file.getName();
	    				}
	    				loadDirClass(file,nextPackage,contextPath,daoPath);
	    			}else{
	    				String className = file.getName();
		    			if(className.endsWith(".class")){
		    				className = className.substring(0, className.length()-6);
		    				loadTypeEntity(pkg+"."+className,contextPath,daoPath);
		    			}
	    			}
	    		}
	    	}
	    }
	    
	    private   void loadTypeEntity(String className,String contextPath,String daoPath){
	    	try {
	    		Class<?> clazz = Thread.currentThread().getContextClassLoader().loadClass(className);
	    		if(!DaoContext.class.isAssignableFrom(clazz)) return ;
	    		TypeEntity typeEntity =  Parser.parse(Thread.currentThread().getContextClassLoader().loadClass(className));
	    		if(typeEntity == null) return;
	    		CoreLogger.getPlatform().info("Load "+typeEntity,null);
	    		DaoFactory.putObject(contextPath,daoPath,typeEntity.getName(), typeEntity);
			} catch (Exception ex) {
				CoreLogger.getPlatform().error("Parse  @WebObject ["+contextPath+","+className+"]   error,cause:"+ex.getMessage(),ex);
			} catch(Error er){
				CoreLogger.getPlatform().error("Parse  @WebObject ["+contextPath+","+className+"]   error,cause:"+er.getMessage(),er);
			}
	    }

		@Override
		public void contextDestroyed(ServletContextEvent arg0) {

		}
		

}
