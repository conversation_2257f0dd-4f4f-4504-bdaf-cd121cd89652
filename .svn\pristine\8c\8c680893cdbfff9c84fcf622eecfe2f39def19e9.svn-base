package org.easitline.common.core.sso.impl;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.security.Principal;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.framework.StructureImpl;
import org.easitline.common.core.framework.vo.Department;
import org.easitline.common.core.framework.vo.User;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.sso.UserPrincipal;
import org.easitline.common.db.EasyRow;

public class DefaultUserPrincipal implements UserPrincipal,Serializable {
	private static final long serialVersionUID = 5719844765834912762L;
	private String rootId;
	private String userId;
	private String loginAcct;
	private int loginType;
	private String userName;
	private String nikeName;
	private String mobile;
	private String email;
	private String ipAddr;
	private String loginTime;
	private User user;
	private Department ownerDw = null;
	private Map<String,Object> attribute=new HashMap<String,Object>();
	private Principal principal;

	private Set<String>    roles = new HashSet<String>();
	private Set<String>    resources = new HashSet<String>();
	
	public DefaultUserPrincipal(){
	}
	
	public DefaultUserPrincipal(Principal principal){
		this.principal = principal;
		//this.init();
	}
//	
//	private void init(){
//		
//		String marsNodeType = System.getProperty("MARS_NODE_TYPE", "mgr");
//		if("mgr".equalsIgnoreCase(marsNodeType)){
//			String sql = "SELECT t1.USER_ID,t1.IP_RANGE,t2.TRY_LOGIN_TIMES FROM EASI_USER t1,EASI_USER_LOGIN t2 WHERE t1.USER_ID = t2.USER_ID and t1.STATE = 0 and t2.USER_ACCT=?";
//			try {
//				EasyRow row = ServerContext.getAdminQuery().queryForRow(sql, new Object[]{this.principal.getName()});
//				if(row == null){
//					LogEngine.getLogger("mars-sso").warn("无效登录用户信息->"+this.principal.getName());
//				}
//				this.userId = row.getColumnValue("USER_ID");
//				this.userName = row.getColumnValue("USER_ID");
//				this.loginAcct = principal.getName();
//			} catch (SQLException ex) {
//				LogEngine.getLogger("mars-sso").error(ex,ex);
//			}
//		}else{
//			String sql="SELECT t1.USER_ID,t1.USER_PWD,t1.LOCK_STATE,t1.ACCT_STATE USER_STATE,t2.ENT_STATE,t2.ENT_TYPE,t1.AGENT_ID   FROM CC_EC_USER t1 , CC_ENT t2  WHERE  t1.ENT_ID = t2.ENT_ID and t2.ENT_TYPE <>2 and t1.USER_ACCT=? ";
//			try {
//				EasyRow row = ServerContext.getAdminQuery().queryForRow(sql, new Object[]{this.principal.getName()});
//				if(row == null){
//					LogEngine.getLogger("mars-sso").warn("无效登录用户信息->"+this.principal.getName());
//				}
//				this.userId = row.getColumnValue("USER_ID");
//				this.userName = row.getColumnValue("USER_ID");
//				this.loginAcct = principal.getName();
//			} catch (SQLException ex) {
//				LogEngine.getLogger("mars-sso").error(ex,ex);
//			}
//		}
//		
//	}
	
	@Override
	public String getName() {
		return this.userId;
	}

	public String getUserId() {
		return userId;
	}

	protected void setRoles(Set<String> roles) {
		//if(this.principal != null) this.principal.
		this.roles = roles;
	}



	protected void setResources(Set<String> resources) {
		this.resources = resources;
	}



	public void setUserId(String userId) {
		this.userId = userId;
	}



	public String getLoginAcct() {
		return loginAcct;
	}



	public void setLoginAcct(String loginAcct) {
		this.loginAcct = loginAcct;
	}



	public int getLoginType() {
		return loginType;
	}



	public void setLoginType(int loginType) {
		this.loginType = loginType;
	}



	public String getUserName() {
		return userName;
	}



	public void setUserName(String userName) {
		this.userName = userName;
	}



	public String getNikeName() {
		return nikeName;
	}



	public void setNikeName(String nikeName) {
		this.nikeName = nikeName;
	}



	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result
				+ ((userId == null) ? 0 : userId.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj) 
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		DefaultUserPrincipal other = (DefaultUserPrincipal) obj;
		if (userId == null) {
			if (other.userId != null)
				return false;
		} else if (!userId.equals(other.userId))
			return false;
		return true;
	}


	@Override
	public boolean isRole(String roleId) {
		return this.roles.contains(roleId);
	}

	@Override
	public boolean isResource(String resId) {
		if(this.isRole("SYS_SUPER_USER")) return true;
		boolean bl =  this.resources.contains(resId);
		return bl;
	}


	

	public String getMobile() {
		if(this.mobile == null) return "";
		return mobile;
	}



	public void setMobile(String mobile) {
		this.mobile = mobile;
	}



	public String getIpAddr() {
		if(this.ipAddr == null) return "";
		return ipAddr;
	}



	public void setIpAddr(String ipAddr) {
		this.ipAddr = ipAddr;
	}



	@Override
	public String getLoginTime() {
		return this.loginTime;
	}



	@Override
	public String getOnlineTime() {
		return "";
	}

	@Override
	public User getUserInfo() {
		if(user == null) user = StructureImpl.getUser(this.userId);
		return user;
	}

	@Override
	public Department getDepartment() {
		User user=this.getUserInfo();
		if(user!=null)return user.getDepartment();
		return null;
	}

	@Override
	public Department getOwnerUnit() {
		if(this.ownerDw!=null) return this.ownerDw;
		Department department = this.getDepartment();
		if(department != null){
			if(department.isUnit()){
				this.ownerDw = department;
				return  this.ownerDw;
			}else{
				this.ownerDw = department.getParentUnit();
				return this.ownerDw;
			}
		}
		return null;
	}
	
	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}
	
	public String getRootId() {
		return rootId;
	}

	public void setRootId(String rootId) {
		this.rootId = rootId;
	}

	public void setAttribute(String key,Object value){
		attribute.put(key, value);
	}
	@SuppressWarnings("unchecked")
	public <T> T getAttribute(String key){
		return (T)attribute.get(key);
	}
	public void removeAttribute(String key){
	    attribute.remove(key);
	}
	@SuppressWarnings("unchecked")
	public <T> T getAttribute(String key,Object defaultVal){
		Object obj=attribute.get(key);
		if(obj==null){
			return (T)defaultVal;
		}
		return (T)obj;
	}

	@Override
	public int getSsoType() {
		return 1;
	}
	
	@Override
	public  Map<String, Object> toMap(){
		Map<String, Object> map = new HashMap<String, Object>();    
        Field[] declaredFields = this.getClass().getDeclaredFields();    
        for (Field field : declaredFields) {    
            field.setAccessible(true);  
            try {
				map.put(field.getName(), field.get(this));
			} catch (IllegalArgumentException e) {
				e.printStackTrace();
			} catch (IllegalAccessException e) {
				e.printStackTrace();
			}  
        }    
        return map;  
	}

	public Set<String> getRoles() {
		return roles;
	}

	public Set<String> getResources() {
		return resources;
	}

	@Override
	public boolean isSuperUser() {
		return this.isRole("SYS_SUPER_USER");
	}
}
