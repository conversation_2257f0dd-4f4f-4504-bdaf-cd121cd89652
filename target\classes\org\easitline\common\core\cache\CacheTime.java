package org.easitline.common.core.cache;

/**
 * 缓存时间（秒）
 */
public class CacheTime {
	
	/**
	 * 三分钟
	 */
	public static final Integer THREE_MINUTES=60*3;
	
	/**
	 * 十分钟
	 */
 	public static final Integer TEN_MINUTES=60*10;
	
	/**
	 * 半个小时
	 */
	public static final Integer HALF_HOUR=60*30;
	
	/**
	 * 一个小时
	 */
	public static final Integer HOUR=60*60;
	
	/**
	 * 一天
	 */
	public static final Integer ONE_DAY=60*60*24;
	
	
	public static enum CacheName {
		threeMinutes {
			@Override
			public String get() {
				return this.name();
			}
		},
		session {
			@Override
			public String get() {
				return this.name();
			}
		}, 
		halfHour {
			@Override
			public String get() {
				return this.name();
			}
		},
		hour {
			@Override
			public String get() {
				return this.name();
			}
		},
		oneDay {
			@Override
			public String get() {
				return this.name();
			}
		},
		_default {
			@Override
			public String get() {
				return this.name().substring(1);
			}
		};
		public abstract String get();
	}

}
