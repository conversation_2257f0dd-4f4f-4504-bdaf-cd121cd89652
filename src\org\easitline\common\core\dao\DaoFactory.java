package org.easitline.common.core.dao;

import java.util.HashMap;
import java.util.Map;

import org.easitline.common.annotation.engine.TypeEntity;
import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.common.utils.string.StringUtils;

/**
 * 数据对象工厂
 *
 */
public class DaoFactory {
	
	private static Map<String,HashMap<String,TypeEntity>>  dataObjects = new HashMap<String,HashMap<String,TypeEntity>>();
	
	private static Map<String,String>  daoPaths = new HashMap<String,String>();
	
	
	public synchronized static void putObject(String contextPath,String typeName,TypeEntity typeEntity){
		putObject( contextPath, null,typeName, typeEntity);
	}

	/**
	 * 添加应用的数据对象
	 * @param contextPath
	 * @param objectName
	 * @param dataObject
	 */
	 public synchronized static void putObject(String contextPath,String daoPath,String typeName,TypeEntity typeEntity){
		HashMap<String,TypeEntity> _types = dataObjects.get(contextPath);
		if(_types == null){
			_types =  new   HashMap<String,TypeEntity>();
			dataObjects.put(contextPath, _types);
		}
		if(StringUtils.notBlank(daoPath)){
			daoPaths.put(daoPath, contextPath);
		}
		_types.put(typeName, typeEntity);
	}
	
	
	/**
	 * 获得数据对象
	 * @param contextPath
	 * @param objectName
	 * @return
	 */
	public static  TypeEntity getObject(String contextPath,String typeName){
		HashMap<String,TypeEntity> _types = dataObjects.get(contextPath);
		if(_types == null) return null;
		return _types.get(typeName);
	}
	public static  TypeEntity getObjectByPath(String daoPath,String typeName){
		String contextPath=daoPaths.get(daoPath);
		if(contextPath == null){ 
			CoreLogger.getPlatform().error("daoPath>"+daoPath+" not exist!");
			return null;
		}
		return getObject(contextPath,typeName);
	}
	
	

}
