package org.easitline.common.core.activemq.impl.rocketmq;

import java.util.List;



import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageConsumer;
import javax.jms.MessageListener;
import javax.jms.ObjectMessage;

import org.apache.rocketmq.client.consumer.DefaultLitePullConsumer;
import org.apache.rocketmq.common.message.MessageExt;
import org.easitline.common.core.activemq.BrokerContext;
import org.easitline.common.core.activemq.impl.ObjectMessageImpl;
import org.easitline.common.core.activemq.log.ActivieMQLogger;

public class RocketmqMessageConsumer implements MessageConsumer {
	
	private DefaultLitePullConsumer consumer;
	
	private BrokerContext context;
	
	public RocketmqMessageConsumer(BrokerContext context,DefaultLitePullConsumer consumer) {
		this.context = context;
		this.consumer = consumer;
	}

	@Override
	public void close() throws JMSException {
	}

	@Override
	public MessageListener getMessageListener() throws JMSException {
		return null;
	}

	@Override
	public String getMessageSelector() throws JMSException {
		return null;
	}

	@Override
	public Message receive() throws JMSException {
		return this.receive(5*1000);
	}

	
	@Override
	public Message receive(long timeout) throws JMSException {
		
		if(!this.consumer.isRunning()) {
			ActivieMQLogger.getLogger().error("Receive("+context.getBrokerName()+") error,cause:"+context.getBrokerName()+" isClose!");
			throw new JMSException(context.getBrokerName()+" isClose!");
		}
		
		ObjectMessage objectMessage = null;
		try {
			List<MessageExt> messageExts = consumer.poll(timeout);
			if(messageExts.size() == 0){
				ActivieMQLogger.getLogger().info("Receive("+context.getBrokerName()+") message size: "+messageExts.size());
				return null;
			}
			objectMessage =   this.getObjectMessage(messageExts.get(0));
			ActivieMQLogger.getLogger().info("Receive("+context.getBrokerName()+")[size:"+messageExts.size()+"] << "+objectMessage);
		} catch (Exception ex) {
			ActivieMQLogger.getLogger().error("Receive("+context.getBrokerName()+") error,cause:"+ex.getMessage(),ex);
			throw ex;
		}
		return objectMessage;
	}

	@Override
	public Message receiveNoWait() throws JMSException {
		return  this.receive(5000);
	}

	@Override
	public void setMessageListener(MessageListener arg0) throws JMSException {
	}
	
	private  ObjectMessageImpl  getObjectMessage(MessageExt message){
		String body = new String(message.getBody());
		ObjectMessageImpl objectMessage = new ObjectMessageImpl(body);
		return objectMessage;
	}
	
	public String toString(){
		return "RocketmqMessageConsumer("+context.getBrokerName()+","+context.getConnector()+")"+
					" -> 运行状态："+consumer.isRunning()+",Consumer实例名:"+this.consumer.getInstanceName()+",Consumer对象名:"+this.consumer;
	}
	
}
