package org.easitline.common.core.activemq.impl;

import java.io.Serializable;

import javax.jms.JMSException;
import javax.jms.Message;

import org.easitline.common.core.activemq.Broker;
import org.easitline.common.core.activemq.BrokerContext;
import org.easitline.common.core.activemq.BrokerMessageListener;
import org.easitline.common.core.activemq.impl.proxy.MessageProxy;
import org.easitline.common.core.activemq.log.ActivieMQLogger;


/**
 * 消息代理的实现类
 * 
 * <AUTHOR>
 * 
 */
public class BrokerImpl implements Broker { 
    
 
	boolean  consumerMode = false;
	
	private BrokerContext context;
 
	public BrokerImpl(BrokerContext context) {
		this.context = context;
	}

	public BrokerContext getContext() {
		return this.context;
	}

	/**
	 * 发送消息
	 */
	public void sendMessage(Serializable messageObj) throws JMSException {

		try {
			//Message message = context.createObjectMessage(messageObj);
			context.getProducer().send(new MessageProxy(messageObj));
		} catch (Exception ex) {
			ActivieMQLogger.getLogger().error(" BrokerImpl.sendMessage(<" + context.getBrokerName()	+ ">) send message error , cause:"+ ex.getMessage(), ex);
			throw new JMSException(ex.getMessage()); 
		} 
	}

	public void registMessageListener(BrokerMessageListener messageListener) throws JMSException {
		try {
			context.getConsumer().setMessageListener(new MessageListenerProxy(messageListener, context));
		} catch (JMSException ex) {
			ActivieMQLogger.getLogger().error("<"+ context.getBrokerName()+ "> BrokerImpl.registMessageListener() error , cause:"+ ex.getMessage(), ex);
		}
	}

	public void close() {
	}

	@Override
	public boolean setComsumerMode() {
		// TODO Auto-generated method stub
		return false;
	}
	
	
	public String toString(){
		return this.context.getBrokerName();
	}



}
