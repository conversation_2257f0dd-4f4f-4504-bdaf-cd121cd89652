# EasitLine Core 最佳实践指南

## 概述

本文档提供了使用 EasitLine Core 框架的最佳实践和推荐方法，帮助开发者编写高质量、高性能、易维护的代码。

## 目录

1. [项目结构](#项目结构)
2. [数据库操作](#数据库操作)
3. [缓存使用](#缓存使用)
4. [异常处理](#异常处理)
5. [日志记录](#日志记录)
6. [安全实践](#安全实践)
7. [性能优化](#性能优化)
8. [代码规范](#代码规范)

---

## 项目结构

### 推荐的项目结构

```
src/
  ├── org/example/
  │   ├── controller/     # Servlet控制器
  │   ├── service/        # 业务逻辑层
  │   ├── dao/            # 数据访问层
  │   ├── model/          # 数据模型
  │   ├── util/           # 工具类
  │   └── config/         # 配置类
  └── resources/
      ├── config/         # 配置文件
      └── sql/            # SQL脚本
```

### 分层设计

遵循标准的MVC架构：

1. **控制层（Controller）**：处理HTTP请求，调用服务层，返回结果
2. **服务层（Service）**：实现业务逻辑，调用DAO层
3. **数据访问层（DAO）**：负责数据库操作
4. **模型层（Model）**：数据模型定义

### 命名规范

- **类名**：使用驼峰命名法，如`UserController`、`UserService`
- **方法名**：使用驼峰命名法，如`getUserById`、`createUser`
- **变量名**：使用驼峰命名法，如`userId`、`userName`
- **常量名**：使用大写下划线，如`MAX_RETRY_COUNT`

## 数据库操作

### 使用事务

对于涉及多个操作的业务逻辑，始终使用事务：

```java
EasyQuery query = getQuery();
query.begin();
try {
    // 操作1
    query.save(record1);
    
    // 操作2
    query.update(record2);
    
    // 提交事务
    query.commit();
    
} catch (Exception e) {
    // 回滚事务
    query.rollback();
    error("操作失败", e);
    throw e;
}
```

### 使用参数化查询

始终使用参数化查询防止SQL注入：

```java
// 正确做法
List<EasyRow> users = query.queryForList(
    "SELECT * FROM users WHERE username = ?", username);

// 错误做法 - 容易导致SQL注入
List<EasyRow> users = query.queryForList(
    "SELECT * FROM users WHERE username = '" + username + "'");
```

### 分页查询

对于大数据量查询，使用分页查询：

```java
int pageNo = 1;
int pageSize = 20;
int offset = (pageNo - 1) * pageSize;

List<EasyRow> users = query.queryForList(
    "SELECT * FROM users ORDER BY create_time DESC LIMIT ?, ?", 
    offset, pageSize);

int total = query.queryForInt("SELECT COUNT(*) FROM users");
```

### 批量操作

对于大批量数据操作，使用批处理：

```java
query.begin();
try {
    for (int i = 0; i < records.size(); i++) {
        query.save(records.get(i));
        
        // 每100条提交一次
        if (i > 0 && i % 100 == 0) {
            query.commit();
            query.begin();
        }
    }
    query.commit();
} catch (Exception e) {
    query.rollback();
    throw e;
}
```

## 缓存使用

### 缓存键命名规范

使用有意义的前缀和分隔符：

```java
// 用户信息缓存
String userCacheKey = "user:" + userId;

// 产品信息缓存
String productCacheKey = "product:" + productId;

// 列表缓存
String listCacheKey = "list:users:department:" + deptId;
```

### 设置合理的过期时间

根据数据更新频率设置过期时间：

```java
// 频繁变化的数据，短期缓存
cache.put("stats:daily:" + date, statsData, CacheTime.HOUR);

// 相对稳定的数据，长期缓存
cache.put("config:system", configData, CacheTime.DAY);

// 几乎不变的数据，超长期缓存
cache.put("constants:dict", dictData, CacheTime.WEEK);
```

### 缓存更新策略

1. **被动失效**：设置过期时间，到期自动失效
2. **主动更新**：数据变更时主动更新缓存

```java
// 更新用户信息时同步更新缓存
public void updateUser(EasyRecord user) {
    // 更新数据库
    query.update(user);
    
    // 更新缓存
    String cacheKey = "user:" + user.get("USER_ID");
    cache.put(cacheKey, user, CacheTime.HOUR);
}
```

### 防止缓存雪崩

使用随机过期时间：

```java
// 添加随机因子，避免同时过期
int expireTime = CacheTime.HOUR + RandomKit.randomInt(0, 300);
cache.put(key, value, expireTime);
```

### 缓存穿透防护

对空结果也进行缓存：

```java
User user = cache.get("user:" + userId);
if (user == null) {
    // 从数据库查询
    EasyRow row = query.queryForObject(
        "SELECT * FROM users WHERE user_id = ?", userId);
    
    if (row != null) {
        user = convertToUser(row);
        // 正常缓存
        cache.put("user:" + userId, user, CacheTime.HOUR);
    } else {
        // 缓存空结果，但过期时间较短
        cache.put("user:" + userId, NULL_OBJECT, 60);
    }
}
```

## 异常处理

### 异常分类处理

根据异常类型采取不同处理策略：

```java
try {
    // 业务逻辑
} catch (SQLException e) {
    // 数据库异常处理
    error("数据库操作失败", e);
    return EasyResult.error("系统繁忙，请稍后再试");
} catch (IllegalArgumentException e) {
    // 参数异常处理
    warn("参数错误: " + e.getMessage());
    return EasyResult.error("参数错误: " + e.getMessage());
} catch (Exception e) {
    // 未知异常处理
    error("未知异常", e);
    return EasyResult.error("系统异常，请联系管理员");
}
```

### 不要吞掉异常

避免空catch块或只打印不处理：

```java
// 错误示例
try {
    // 业务逻辑
} catch (Exception e) {
    e.printStackTrace(); // 只打印不处理
}

// 正确示例
try {
    // 业务逻辑
} catch (Exception e) {
    error("操作失败", e);
    throw new ServiceException("操作失败", e);
}
```

### 使用自定义异常

定义业务相关的异常类：

```java
public class ServiceException extends RuntimeException {
    private int code;
    
    public ServiceException(String message) {
        super(message);
        this.code = 500;
    }
    
    public ServiceException(String message, int code) {
        super(message);
        this.code = code;
    }
    
    public int getCode() {
        return code;
    }
}
```

## 日志记录

### 日志级别使用

- **DEBUG**：详细的开发调试信息
- **INFO**：一般操作信息，如用户登录、重要业务流程
- **WARN**：潜在问题警告，如配置错误、性能问题
- **ERROR**：错误信息，如异常、业务处理失败

```java
// 调试信息
debug("处理用户请求: " + userId);

// 一般信息
info("用户登录成功: " + userName);

// 警告信息
warn("配置项缺失，使用默认值: " + configName);

// 错误信息
error("用户创建失败", exception);
```

### 记录关键业务日志

对于重要操作，使用OperatorLog记录：

```java
// 记录用户创建操作
OperatorLog log = new OperatorLog(1, "创建用户")
    .setUserId(getCurrentUserId())
    .setUserAcct(getCurrentUserAcct())
    .setIpAddr(getRequest().getRemoteAddr())
    .setModule("用户管理")
    .setParams(userRecord);
log.save();
```

### 避免敏感信息泄露

不要在日志中记录敏感信息：

```java
// 错误示例
debug("用户登录，密码为: " + password);

// 正确示例
debug("用户登录: " + username);
```

## 安全实践

### 密码存储

使用加密算法存储密码：

```java
// 密码加密
String encryptedPassword = MD5Util.getHexMD5(password);

// 密码验证
boolean isValid = MD5Util.verify(inputPassword, storedPassword);
```

### 敏感数据加密

对敏感数据进行加密：

```java
// 加密敏感信息
String encryptedIdCard = ConfigContext.encryptionStr(idCard, "DS");

// 解密敏感信息
String idCard = ConfigContext.decryptionStr(encryptedIdCard, "DS");
```

### 防止SQL注入

始终使用参数化查询：

```java
// 安全查询
List<EasyRow> users = query.queryForList(
    "SELECT * FROM users WHERE username = ?", username);
```

### 防止XSS攻击

对输入输出进行HTML转义：

```java
// 转义HTML特殊字符
String safeHtml = StringUtils.escapeHtml(input);
```

### 权限控制

使用PreAuthorize注解进行权限控制：

```java
@PreAuthorize(resId = "user.create", checkType = ResIdCheckType.METHOD)
public void createUser() {
    // 创建用户逻辑
}
```

## 性能优化

### 数据库优化

1. **索引优化**：为常用查询字段创建索引
2. **查询优化**：只查询需要的字段，避免SELECT *
3. **连接池管理**：合理配置连接池参数

```java
// 只查询需要的字段
List<EasyRow> users = query.queryForList(
    "SELECT user_id, user_name, email FROM users WHERE dept_id = ?", 
    deptId);
```

### 缓存优化

1. **合理使用缓存**：频繁访问、计算复杂的数据优先缓存
2. **避免缓存穿透**：对空结果也进行缓存
3. **定期清理**：设置合理的过期时间，避免内存泄漏

### 代码优化

1. **避免大对象**：减少内存占用
2. **使用StringBuilder**：字符串拼接优先使用StringBuilder
3. **避免频繁创建对象**：使用对象池或缓存

```java
// 使用StringBuilder拼接字符串
StringBuilder sql = new StringBuilder();
sql.append("SELECT * FROM users ");
sql.append("WHERE status = ? ");
if (deptId != null) {
    sql.append("AND dept_id = ? ");
}
sql.append("ORDER BY create_time DESC");

// 执行查询
List<Object> params = new ArrayList<>();
params.add(status);
if (deptId != null) {
    params.add(deptId);
}
List<EasyRow> users = query.queryForList(sql.toString(), params.toArray());
```

## 代码规范

### 命名规范

1. **类名**：使用驼峰命名法，首字母大写
2. **方法名**：使用驼峰命名法，首字母小写
3. **变量名**：使用驼峰命名法，首字母小写
4. **常量名**：使用大写下划线

### 注释规范

1. **类注释**：说明类的用途、作者、版本等
2. **方法注释**：说明方法的功能、参数、返回值等
3. **关键代码注释**：解释复杂逻辑

```java
/**
 * 用户服务类，提供用户相关的业务逻辑
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class UserService extends BaseService {
    
    /**
     * 创建新用户
     * 
     * @param userInfo 用户信息
     * @return 操作结果
     */
    public EasyResult createUser(JSONObject userInfo) {
        // 方法实现
    }
}
```

### 代码格式

1. **缩进**：使用4个空格
2. **行宽**：不超过120个字符
3. **括号**：左括号不换行，右括号独占一行
4. **空行**：方法之间空一行，逻辑块之间空一行

### 代码复用

1. **提取公共方法**：将重复代码提取为公共方法
2. **使用工具类**：将通用功能放入工具类
3. **继承与组合**：合理使用继承和组合实现代码复用

```java
// 提取公共方法
private EasyRow getUserById(String userId) {
    return query.queryForObject(
        "SELECT * FROM users WHERE user_id = ?", userId);
}

// 在多个地方调用
public void method1() {
    EasyRow user = getUserById("123");
    // 其他逻辑
}

public void method2() {
    EasyRow user = getUserById("456");
    // 其他逻辑
}
```

## 总结

遵循以上最佳实践，可以帮助您开发出高质量、高性能、易维护的应用程序。随着项目的发展，不断总结经验，持续改进开发实践。
