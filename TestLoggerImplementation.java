import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.context.ServerContext;

/**
 * 测试当前使用的日志实现
 */
public class TestLoggerImplementation {
    
    public static void main(String[] args) {
        System.out.println("=== 日志实现测试 ===");
        
        // 1. 检查isLog4j2配置
        boolean isLog4j2 = ServerContext.isLog4j2();
        System.out.println("ServerContext.isLog4j2(): " + isLog4j2);
        
        // 2. 创建logger并检查其类型
        Logger logger = LogEngine.getLogger("test-implementation");
        System.out.println("Logger class: " + logger.getClass().getName());
        System.out.println("Logger toString: " + logger.toString());
        
        // 3. 输出测试日志
        System.out.println("\n=== 测试日志输出 ===");
        logger.info("这是测试日志 - 检查是否显示位置信息");
        logger.warn("这是警告日志 - 检查格式");
        logger.error("这是错误日志 - 检查格式");
        
        // 4. 测试不同的logger名称
        Logger logger2 = LogEngine.getLogger("yc-yjmanage");
        logger2.info("模拟yc-yjmanage的日志输出");
        
        System.out.println("\n=== 测试完成 ===");
        System.out.println("请检查日志文件中的输出格式");
    }
}
