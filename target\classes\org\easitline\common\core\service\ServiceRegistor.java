package org.easitline.common.core.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.utils.string.StringUtils;


/**
 * 服务注册类
 * <AUTHOR>
 *
 */
public class ServiceRegistor{

	private static Map<String, ServiceResource> services = new ConcurrentHashMap<String, ServiceResource>();

	private static class Holder{
		private static ServiceRegistor registor = new ServiceRegistor();
	}
	
	/**
	 * 获得服务注册器
	 * @return
	 */
	public static ServiceRegistor getRegistor() {
		return Holder.registor;
	}
	
	public ServiceResource findServiceByID(String serviceID) {
		return services.get(serviceID);
	}
	
	/**
	 * 根据服务的前缀获得服务列表
	 * @param serviceIdPrefix
	 * @return
	 */
	public List<String> findServiceByPrefix(String serviceIdPrefix) {
		List<String>  list = new ArrayList<String>();
		Set<String> keys = services.keySet();
		for(String key : keys){
			if(key.startsWith(serviceIdPrefix)){
				list.add(key);
			}
		}
		return list;
	}

	/**
	 * 注册新的服务
	 * @param serviceDesc	服务描述对象
	 *            
	 */
	public void register(ServiceResource serviceResource) {
		if(StringUtils.notBlank(serviceResource.serviceId)){
			services.put(serviceResource.serviceId, serviceResource);
		}else{
			throw new RuntimeException("serviceId not allow is null.");
		}
	}

	/**
	 * 销毁服务
	 * @param serviceID	 服务ID
	 */
	public void unregister(ServiceResource serviceResource) {
		services.remove(serviceResource.serviceId);
	}
	
	
	public List<ServiceResource>  listAllServices(){
		return new ArrayList(services.values());
	}
}
