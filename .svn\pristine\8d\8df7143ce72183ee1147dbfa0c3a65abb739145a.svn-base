package org.easitline.common.core.sso;

import javax.security.auth.login.LoginException;

/**
 * 平台认证接口，各个业务系统可以根据需要进行重新实现。
 * <AUTHOR>
 *
 */
public interface LoginFactory {

	public boolean  login(String acct,String password) throws LoginException;
	
	public UserPrincipal  createUserPrincipal(String user) throws LoginException;
	
	public RolePrincipal  createRolePrincipal(String user) throws LoginException;
}
