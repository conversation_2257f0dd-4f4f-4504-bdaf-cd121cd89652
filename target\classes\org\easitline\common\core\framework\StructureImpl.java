package org.easitline.common.core.framework;


import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;


import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.framework.vo.*;
import org.easitline.common.core.framework.vo.model.DepartmentModel;
import org.easitline.common.core.framework.vo.rowmapper.DepartmentModelRowMapper;
import org.easitline.common.core.framework.vo.rowmapper.DepartmentRowMapper;
import org.easitline.common.core.framework.vo.rowmapper.LabelRowMapper;
import org.easitline.common.core.framework.vo.rowmapper.RoleRowMapper;
import org.easitline.common.core.framework.vo.rowmapper.UserRowMapper;
import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.common.utils.lang.TreeUtil;

 /**
  * 组织架构统一操作接口
 *
 */

public class StructureImpl {

    /**
                * 获得标签信息
     * @param labelId 标签ID
     * @return
     */
    public static  Label getLabel(String labelId){
    	String sql = "SELECT * FROM EASI_LABEL WHERE LABEL_ID  = ?";
    	try {
			return ServerContext.getAdminQuery().queryForRow(sql,null,new LabelRowMapper());
		} catch (SQLException ex) {
			CoreLogger.getLogger().error("Core-framework->获取所有标签列表失败，原因："+ex.getMessage(),ex);
		}
    	return null;
    }
    
    
    /**
              * 获得标签信息
     * @param labelId
     * @return
     */
    public static  List<Label> getLabels(){
    	String sql = "SELECT * FROM EASI_LABEL";
    	try {
			return ServerContext.getAdminQuery().queryForList(sql,null,new LabelRowMapper());
		} catch (SQLException ex) {
			CoreLogger.getLogger().error("Core-framework->获取所有标签列表失败，原因："+ex.getMessage(),ex);
		}
    	return null;
    }
    

    
    /**
     * 获得用户信息
     * @param userId  用户ID
     * @return
     */
    public static  User  getUser(String userId){
    	String sql = "SELECT T1.*,T2.USER_ACCT FROM EASI_USER T1 LEFT JOIN EASI_USER_LOGIN T2 ON T1.USER_ID = T2.USER_ID AND T2.LOGIN_TYPE = 1 WHERE T1.STATE <> 9 AND T1.USER_ID = ?";
    	try {
			return ServerContext.getAdminQuery().queryForRow(sql, new String[]{userId},new UserRowMapper());
		} catch (SQLException ex) {
			CoreLogger.getLogger().error("Core-framework->获取用户信息失败，原因："+ex.getMessage(),ex);
			return null;
		}
    }
    
    /**
     * 获得部门
     * @param deptId  部门ID
     * @return
     */
    public static  Department getDepartment(String deptId){
    	String sql = "SELECT t1.*,t2.NODE_TYPE FROM EASI_DEPT t1 left join  EASI_DEPT_TYPE t2  on t1.DEPT_TYPE = t2.DEPT_TYPE  WHERE t1.DEPT_ID = ?";
    	try {
			return ServerContext.getAdminQuery().queryForRow(sql, new String[]{deptId},new DepartmentRowMapper());
		} catch (SQLException ex) {
			CoreLogger.getLogger().error("Core-framework->获取部门信息失败，原因："+ex.getMessage(),ex);
			return null;
		}
    }
    
    
    /**
     * 获得部门
     * @param deptId 部门代码
     * @return
     */
    public static  Department getDepartmentByCode(String deptCode){
    	String sql = "SELECT t1.*,t2.NODE_TYPE FROM EASI_DEPT t1 left join  EASI_DEPT_TYPE t2  on t1.DEPT_TYPE = t2.DEPT_TYPE   WHERE t1.DEPT_CODE = ?";
    	try {
			return ServerContext.getAdminQuery().queryForRow(sql, new String[]{deptCode},new DepartmentRowMapper());
		} catch (SQLException ex) {
			CoreLogger.getLogger().error("Core-framework->获取部门信息失败，原因："+ex.getMessage(),ex);
			return null;
		}
    }
    
    
    /**
     * 获得指定部门类型的部门列表
     * @param deptTypeId 部门类型ID
     * @return
     */
    public static  List<Department> getDepartmentByType(String deptTypeId){
    	String sql = "SELECT t1.*,t2.NODE_TYPE FROM EASI_DEPT t1 left join  EASI_DEPT_TYPE t2  on t1.DEPT_TYPE = t2.DEPT_TYPE   WHERE t1.DEPT_TYPE = ?";
    	try {
			return ServerContext.getAdminQuery().queryForList(sql, new String[]{deptTypeId},new DepartmentRowMapper());
		} catch (SQLException ex) {
			CoreLogger.getLogger().error("Core-framework->获取部门信息失败，原因："+ex.getMessage(),ex);
			return null;
		}
    }
     
    /**
     * 获得所有角色列表
     * @return
     */
    public static  List<Role> getRoles(){
    	String sql = "SELECT * FROM EASI_ROLE  ";
    	try {
			return ServerContext.getAdminQuery().queryForList(sql,null,new RoleRowMapper());
		} catch (SQLException ex) {
			CoreLogger.getLogger().error("Core-framework->获取角色列表失败，原因："+ex.getMessage(),ex);
		}
    	return null;
    }
    

    /** 
     * 获得角色
     * @param roleId  角色ID
     * @return
     */
    public static  Role getRole(String roleId){
    	String sql = "SELECT * FROM EASI_ROLE WHERE ROLE_ID = ?";
    	try {
			return ServerContext.getAdminQuery().queryForRow(sql, new String[]{roleId},new RoleRowMapper());
		} catch (SQLException ex) {
			CoreLogger.getLogger().error("Core-framework->获取角色信息失败，原因："+ex.getMessage(),ex);
		}
    	return null;
    }
    
    
    /**
     * 获得指定节点的组织架构树
     * @deptId  部门ID
     * @param nodeTypes  节点类型，1：节点 2：单位 3：部门 
     * @return
     */
    public static  String getStructTreeByNode(String deptId,int[] nodeTypes){
    	Set<Integer> typeSet = new HashSet<Integer>();
		for(int nodeType :nodeTypes){
			typeSet.add(nodeType);
		}
		
		Department department = getDepartment(deptId);
		String sql = " select t1.*,t2.NODE_TYPE from EASI_DEPT t1 left join EASI_DEPT_TYPE t2 on t1.DEPT_TYPE = t2.DEPT_TYPE   ORDER BY IDX_ORDER , DEPT_CODE  ";
	
		List<DepartmentModel> cnodes = new ArrayList<DepartmentModel>();
		try {
			List<DepartmentModel> list = ServerContext.getAdminQuery().queryForList(sql, null, new DepartmentModelRowMapper());
			//获得当前单位的子部门
			getDWNodes(list, cnodes,"0",typeSet );
			JSONArray  jsonArray = JSONArray.parseArray(JSONObject.toJSONString(cnodes));
			return TreeUtil.getZtreeJSONString(department.getDeptName(),department.getDeptCode(), "deptCode", "deptName", "pdeptCode", 2, null, jsonArray);
		} catch (SQLException ex) {
			CoreLogger.getLogger().error("StructureImpl->创建用户所在单位结构树失败，原因："+ex.getMessage(), ex);
		}
		return "{}";
    }
    public static  List<DepartmentModel> getDepartmentList(int... nodeTypes){
    	Set<Integer> typeSet = new HashSet<Integer>();
		for(int nodeType :nodeTypes){
			typeSet.add(nodeType);
		}
		String sql = " select t1.*,t2.NODE_TYPE from EASI_DEPT t1 left join EASI_DEPT_TYPE t2 on t1.DEPT_TYPE = t2.DEPT_TYPE  ORDER BY  IDX_ORDER , DEPT_CODE  ";
		List<DepartmentModel> cnodes = new ArrayList<DepartmentModel>();
		try {
			List<DepartmentModel> list = ServerContext.getAdminQuery().queryForList(sql, new Object[]{}, new DepartmentModelRowMapper());
			getDWNodes(list, cnodes,"0",typeSet );
			return cnodes;
		} catch (SQLException e){
			CoreLogger.getLogger().error(e);
		}
		return cnodes;
    }
    /**
     * 获得组织架构树
     * @param nodeTypes  节点类型，1：节点 2：单位 3：部门 
     * @return
     */
    public static  String getStructTree(int[] nodeTypes){
    	Set<Integer> typeSet = new HashSet<Integer>();
		for(int nodeType :nodeTypes){
			typeSet.add(nodeType);
		}
		
		String sql = " select t1.*,t2.NODE_TYPE from EASI_DEPT t1 left join EASI_DEPT_TYPE t2 on t1.DEPT_TYPE = t2.DEPT_TYPE   ORDER BY  IDX_ORDER , DEPT_CODE  ";
	
		List<DepartmentModel> cnodes = new ArrayList<DepartmentModel>();
		try {
			List<DepartmentModel> list = ServerContext.getAdminQuery().queryForList(sql, null, new DepartmentModelRowMapper());
			//获得当前单位的子部门
			getDWNodes(list, cnodes,"0",typeSet );
			JSONArray  jsonArray = JSONArray.parseArray(JSONObject.toJSONString(cnodes));
			return TreeUtil.getZtreeJSONString("组织架构", "0", "deptCode", "deptName", "pdeptCode", 2, null, jsonArray);
		} catch (SQLException ex) {
			CoreLogger.getLogger().error("StructureImpl->创建用户所在单位结构树失败，原因："+ex.getMessage(), ex);
		}
		return "{}";
    }
    
    /**
               * 获得用户所在单位的组织架构树
     * @param userId  用户ID
     * @param nodeTypes  节点类型，1：节点 2：单位 3：部门 
     * @return  ztree json对象
     */
	public static  String getStructTreeByUser(String userId,int[] nodeTypes){
		Set<Integer> typeSet = new HashSet<Integer>();
		for(int nodeType :nodeTypes){
			typeSet.add(nodeType);
		}
		String sql = " select t1.*,t2.NODE_TYPE from EASI_DEPT t1 left join EASI_DEPT_TYPE t2 on t1.DEPT_TYPE = t2.DEPT_TYPE   ORDER BY  IDX_ORDER , DEPT_CODE  ";
		User user = getUser(userId);
		if(user == null) return "{}";
		Department department =  user.getDepartment();
		if(!department.isUnit()){  // 如果当前节点不是单位，则往上找到节点为单位的节点
			department  = department.getParentUnit();
		}
		if(department == null)  return "{}";
		if(typeSet.contains(0)){  // 选择上级单位
			department = department.getParentUnit();
			if(department == null){
				return "{}";
			}
			typeSet.clear();
			typeSet.add(3);
		}
		// 保存所有子节点信息
		List<DepartmentModel> cnodes = new ArrayList<DepartmentModel>();
		try {
			List<DepartmentModel> list = ServerContext.getAdminQuery().queryForList(sql, null, new DepartmentModelRowMapper());
			// 获得当前单位的子部门
			getDWNodes(list, cnodes, department.getDeptCode(),typeSet );
			JSONArray  jsonArray = JSONArray.parseArray(JSONObject.toJSONString(cnodes));
			return TreeUtil.getZtreeJSONString(department.getDeptName(), department.getDeptCode(), "deptCode", "deptName", "pdeptCode", 3, null, jsonArray);
		} catch (SQLException ex) {
			CoreLogger.getLogger().error("StructureImpl->创建用户所在单位结构树失败，原因："+ex.getMessage(), ex);
		}
		return "{}";
	}
	
	/**
	 * 获得单位下的部门信息
	 * @param list
	 * @param cnodes
	 * @param dwNodeCode
	 */
	private static  void getDWNodes(List<DepartmentModel> list,List<DepartmentModel> cnodes,String dwNodeCode,Set<Integer> typeSet ){
		for(DepartmentModel dm:list){  // 找到当前用户所在的部门，然后采用递归的的算法把所有子部门都找出来
			if(dwNodeCode.equals(dm.getPdeptCode())){
				if(typeSet.contains(dm.getNodeType())){
					cnodes.add(dm);
					getDWNodes(list, cnodes, dm.getDeptCode(),typeSet);  // 找当前部门的所有子部门
				}
			}
		}
		
	}
 

}
