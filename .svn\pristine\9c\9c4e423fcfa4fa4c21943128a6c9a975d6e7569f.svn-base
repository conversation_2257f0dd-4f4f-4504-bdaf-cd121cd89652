package org.easitline.common.core.activemq;

import java.util.HashSet;

import org.easitline.common.core.activemq.impl.BrokerImpl;
import org.easitline.common.core.activemq.log.ActivieMQLogger;

/**
 * 负责进行Consumer的注册和管理
 * 
 * <AUTHOR>
 * 
 */
public class ConsumerRegister extends Thread {

	/**
	 * 消息队列
	 */
	private Broker broker;

	/**
	 * 消息监听
	 */
	private Class listenerClass;

	/**
	 * 生成consumers个数
	 */
	private int count;

	private static HashSet<BrokerMessageListener> consumers = new HashSet<BrokerMessageListener>();

	/**
	 * 注册Consumre
	 * 
	 * @param broker
	 *            消息队列
	 * @param listenerClass
	 *            消息监听器
	 * @param count
	 *            数量
	 */
	public ConsumerRegister(Broker broker, Class<?> listenerClass, int count) {
		this.broker = broker;
		this.listenerClass = listenerClass;
		this.count = count;
	}

	public void run() {
		try {
			Thread.sleep(5000);
		} catch (InterruptedException e) {
			ActivieMQLogger.getLogger().error("ConsumerRegister.run() error,cause:" + e.getMessage());
		}

		ActivieMQLogger.getLogger().info("<" + ((BrokerImpl) broker).getContext().getBrokerName()+ "> ConsumerRegister.run()  -->register consumer["+ listenerClass.getName() + "] , count=" + this.count);

		for (int i = 1; i <= count; i++) {
			while (true) {
				try {
					BrokerMessageListener brokerMessageListener = (BrokerMessageListener) listenerClass.newInstance();
					// 注册消息监听器
					broker.registMessageListener(brokerMessageListener);
					// consumers.add(broker);
					ActivieMQLogger.getLogger().info("<"+ ((BrokerImpl) broker).getContext().getBrokerName()+ "> ConsumerRegister.run()  -->register consumer["+ listenerClass.getName()+ "] , count=" + this.count);
					break;
				} catch (Exception ex1) {
					ActivieMQLogger.getLogger().error("ConsumerRegister.run()  register consumer error,cause:"+ ex1.getMessage());
					try {
						Thread.sleep(5000);
					} catch (Exception ex2) {
						ActivieMQLogger.getLogger().error("ConsumerRegister.run() error,cause:"+ ex2.getMessage());
					}

				}
			}

		}
	}

	public static HashSet<BrokerMessageListener> getConsumers(Class listenerClass) {
		return consumers;
	}

	public static HashSet<BrokerMessageListener> getAllConsumers() {
		return consumers;
	}

	/**
	 * 关闭
	 * 
	 * @param listenerClass
	 */
	public static void close(Class listenerClass) {

	}

	/**
	 * 关闭所有Consumer
	 */
	public static void closeAll() {

	}
}
