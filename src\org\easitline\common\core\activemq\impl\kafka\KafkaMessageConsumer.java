package org.easitline.common.core.activemq.impl.kafka;

import java.time.Duration;

import java.util.LinkedList;
import java.util.Queue;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageConsumer;
import javax.jms.MessageListener;
import javax.jms.ObjectMessage;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.easitline.common.core.activemq.BrokerContext;
import org.easitline.common.core.activemq.impl.ObjectMessageImpl;
import org.easitline.common.core.activemq.log.ActivieMQLogger;

public class KafkaMessageConsumer implements MessageConsumer {
	
	private Queue<ConsumerRecord<String, String>>  messageQueue =  new LinkedList<ConsumerRecord<String, String>>();
	
	private KafkaConsumer<String, String> consumer;
	
	private BrokerContext context;
	
	public KafkaMessageConsumer(BrokerContext context,KafkaConsumer<String, String> consumer) {
		this.context = context;
		this.consumer = consumer;
	}

	@Override
	public void close() throws JMSException {
		this.consumer.close();
	}

	@Override
	public MessageListener getMessageListener() throws JMSException {
		return null;
	}

	@Override
	public String getMessageSelector() throws JMSException {
		return null;
	}

	@Override
	public Message receive() throws JMSException {
		return this.receive(0);
	}

	@Override
	public Message receive(long timeout) throws JMSException {
		if(messageQueue.size() == 0){
			ConsumerRecords<String, String>  records  = consumer.poll(Duration.ofMillis(timeout));
			 for (ConsumerRecord<String, String> record : records) {
				 this.messageQueue.add(record);
	         }
		}else{
			ActivieMQLogger.getLogger().info("kafka.receive("+context.getBrokerName()+")->timeout:"+timeout+",queueSize:"+this.messageQueue.size());
		}
		ConsumerRecord<String, String>  message   = null;
		if(messageQueue!=null){
			message = messageQueue.poll();
		}
		ObjectMessage objectMessage = null;
		if(message!=null){
			objectMessage =  this.getObjectMessage(message);
			ActivieMQLogger.getLogger().info("kafka.receive("+context.getBrokerName()+") << "+objectMessage);
		}
		return objectMessage;
	}

	@Override
	public Message receiveNoWait() throws JMSException {
		return null;
	}

	@Override
	public void setMessageListener(MessageListener arg0) throws JMSException {
	}
	
	private  ObjectMessageImpl  getObjectMessage(ConsumerRecord<String, String>  record){
		ObjectMessageImpl objectMessage = new ObjectMessageImpl(record.value());
		return objectMessage;
	}

}
