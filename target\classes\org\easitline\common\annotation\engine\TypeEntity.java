package org.easitline.common.annotation.engine;

import java.util.HashMap;
import java.util.Map;

/**
 * 对表的定义
 * <AUTHOR>
 *
 */

public class TypeEntity {
	
	private Map<String,MethodEntity> controls = new  HashMap<String,MethodEntity>();

	private Class<?> clazz;

	private String  name;
	
	public void putMethodEntity(MethodEntity  methodEntity){
		if(methodEntity == null) return ;
		controls.put(methodEntity.getName(), methodEntity);
	}
	
	public MethodEntity getMethodEntity(String controlName){
		return controls.get(controlName);
	}
	
	public Map<String,MethodEntity> getMethodEntity(){
		return controls;
	}

	public Class<?> getClazz() {
		return clazz;
	}

	public void setClazz(Class<?> clazz) {
		this.clazz = clazz;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	public String toString(){
		StringBuffer buf = new StringBuffer();
		buf.append("TypeEntity->name:").append(this.name).append(",class:").append(this.clazz).append(",method:").append(this.controls);
		return buf.toString();
	}

}
