package org.easitline.common.core.cache.impl;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.easitline.common.core.cache.EasyCache;

public class DefaultEasyCacheImpl implements EasyCache {
	
	
	private static class Holder{
		private static DefaultEasyCacheImpl cache = new DefaultEasyCacheImpl();
	}
	
	public DefaultEasyCacheImpl(){
		
	}
	
	public static DefaultEasyCacheImpl getInstance(){
		return Holder.cache;
	}
	
	private static Map<String,Object> keyValues = new ConcurrentHashMap<String,Object>();

	@Override
	public void put(String key, Object value) {
		keyValues.put(key, value);
	}

	@SuppressWarnings("unchecked")
	@Override
	public  <T> T get(String key) {
		return (T)keyValues.get(key);
	}

	@Override
	public void put(String key, Object value, int second) {
		keyValues.put(key, value);
	}

	@Override
	public void flushAll() {
		keyValues.clear();
	}

	@Override
	public void delete(String key) {
		keyValues.remove(key);
	}

	@Override
	public void init() {
		keyValues = new ConcurrentHashMap<String,Object>();
	}

	@Override
	public void stop() {
		keyValues=null;
	}

	@Override
	public void reload() {
		stop();
		init();
	}

	public Map<String, Object> getKeyValues() {
		return keyValues;
	}

	public void setKeyValues(Map<String, Object> keyValues) {
		DefaultEasyCacheImpl.keyValues = keyValues;
	}
	
	
}
