package org.easitline.common.core.web.websecurity;

import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;

import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.common.utils.string.StringUtils;

public class XssHttpServletRequest extends HttpServletRequestWrapper{
		private HttpServletRequest request;
		private boolean filterHtmlTag;
		private String urlRules;

	    public XssHttpServletRequest(HttpServletRequest request,String urlRules,boolean filterHtmlTag) {
	        super(request);
	        this.request = request;
	        this.urlRules = urlRules;
	        this.filterHtmlTag = filterHtmlTag;
	    }
	    
      public static boolean getJSONType(String str) {
        boolean result = false;
        if (StringUtils.isNotBlank(str)) {
            str = str.trim();
            if (str.startsWith("{") && str.endsWith("}")) {
                result = true;
            } else if (str.startsWith("[") && str.endsWith("]")) {
                result = true;
            }
        }
        return result;
     }
	    
	    
	    /**
	     * 重写getParameter方法
	     */
	    @Override
	    public String getParameter(String name) {
	        String value = this.request.getParameter(name);
	        if (value == null) {
	            return null;
	        }
	        boolean isJson = false;
//	        if("data".equals(name)) {
//	        	if(getJSONType(value)) {
//	    	    	value = StringUtils.replaceIgnoreCase(value,"<","&lt;");
//	    	    	value = StringUtils.replaceIgnoreCase(value,">","&gt;");
//	        		Object obj  = JSON.parse(value);
//	        		if(obj instanceof JSONObject) {
//	        			JSONObject valueJson = (JSONObject) obj;
//	        			Set<String>  set  = valueJson.keySet();
//	        			JSONObject jsonObject = new JSONObject();
//	        			for(String s:set) {
//	        				Object valueObj = valueJson.get(s);
//	        				if(valueObj instanceof String) {
//	        					jsonObject.put(s, format(valueObj.toString()));
//	        				}else {
//	        					jsonObject.put(s, valueObj);
//	        				}
//	        			}
//	        			value = jsonObject.toJSONString();
//	        			isJson  =  true;
//	        		}
//	        	}
//	        }
	        if(!isJson) {
	        	value = format(value);
	        }
	        return value;
	    }
	    /**
	     * 重写getParameterMap
	     */
	    @Override
	    public Map<String, String[]> getParameterMap() {
	    	Map<String, String[]> parameterMap =  super.getParameterMap();
	    	Map<String, String[]> newMap = new HashMap<String, String[]>();
	        for (Entry<String, String[]> entry : parameterMap.entrySet()) { 
	        	String [] values = entry.getValue();
	            for (int i = 0; i < values.length; i++) {
	                if(values[i] instanceof String){
	                    values[i] = format(values[i]);
	                }
	            }
	            newMap.put(entry.getKey(), values);
	        }
	        return newMap;
	    }
	    public String filter(String message) {
	        if (message == null)
	            return (null);
	        message = format(message);
	        return message;
	    }
	    /**
	     *  @desc 统一处理特殊字符的方法，这里用的是apache的StringUtils.replaceEach  方法
	     *  @param name 要替换的字符
	     */
	    private  String format(String value) {
	    	String xssList = urlRules;
	    	if(StringUtils.isBlank(urlRules)) {
	    		value = StringUtils.replaceIgnoreCase(value,"<","&lt;");
	    		value = StringUtils.replaceIgnoreCase(value,">","&gt;");
	    		xssList = "<script,script>,or ,',expression\\\\\\\\(,iframe>,<iframe,<img,alert,prompt,<,</,confirm,onerror,onkeyup,onclick,oncompleteonabort,onabort,onblur,onchange,ondblclick,onfocus,onkeydown,onkeypress,onreset,onsubmit,onload,onunload,onmouse,ondrag,oncontextmenu,HYPERLINK";
	    	}
	    	String[] strs = xssList.split(",");
	        // 循环替换直到字符串不再变化
	        String previousValue;
	        String originalValue = value;
	        do {
	            previousValue = value;
	            for(String str:strs) {
	                String newValue = StringUtils.replaceIgnoreCase(value,str,"");
	                if(!newValue.equals(value)) {
	                	CoreLogger.getLogger().warn("检测到XSS攻击参数 - 原始值: [" + originalValue + "], 命中规则: [" + str + "], 过滤后的值: [" + newValue + "]");
	                }
	                value = newValue;
	            }
	        } while (!value.equals(previousValue));
	        
	        if(filterHtmlTag) {
	            value = deleteAllHTMLTag(value);
	        }
	    	return value;
	    }
	    
	    
	    
	    private static String xssEncode(String s) {
	        if (s == null || s.isEmpty()) {
	            return s;
	        }
	        StringBuilder sb = new StringBuilder(s.length() + 16);
	        for (int i = 0; i < s.length(); i++) {
	            char c = s.charAt(i);
	            switch (c) {
	            case '\\':
	            	sb.append('＼');// 全角大于号
	            	break;
	            case '>':
	                sb.append('＞');// 全角大于号
	                break;
	            case '<':
	                sb.append('＜');// 全角小于号
	                break;
//	            case '(':
//	            	sb.append('（');// 全角大于号
//	            	break;
//	            case ')':
//	            	sb.append('）');// 全角小于号
//	            	break;
//	            case '\'':
//	                sb.append('‘');// 全角单引号
//	                break;
//	            case '\"':
//	                sb.append('“');// 全角双引号
//	                break;
//	            case '&':
//	                sb.append('＆');// 全角
//	                break;
//	            case '\\':
//	                sb.append('＼');// 全角斜线
//	                break;
//	            case '#':
//	                sb.append('＃');// 全角井号
//	                break;
	            case '%':    // < 字符的 URL 编码形式表示的 ASCII 字符（十六进制格式） 是: %3c
	                processUrlEncoder(sb, s, i);
	                break;
	            default:
	                sb.append(c);
	                break;
	            }
	        }
	        return sb.toString();
	    }
	    
	    public static void processUrlEncoder(StringBuilder sb, String s, int index){
	        if(s.length() >= index + 2){
	            if(s.charAt(index+1) == '3' && (s.charAt(index+2) == 'c' || s.charAt(index+2) == 'C')){    // %3c, %3C
	                sb.append('＜');
	                return;
	            }
	            if(s.charAt(index+1) == '6' && s.charAt(index+2) == '0'){    // %3c (0x3c=60)
	                sb.append('＜');
	                return;
	            }            
	            if(s.charAt(index+1) == '3' && (s.charAt(index+2) == 'e' || s.charAt(index+2) == 'E')){    // %3e, %3E
	                sb.append('＞');
	                return;
	            }
	            if(s.charAt(index+1) == '6' && s.charAt(index+2) == '2'){    // %3e (0x3e=62)
	                sb.append('＞');
	                return;
	            }
	        }
	        sb.append(s.charAt(index));
	    }
	    
	   private static String deleteAllHTMLTag(String source) {
	     if(source == null) {
	          return "";
	     }
	     String s = source;
	     /** 删除普通标签  */
	     s = s.replaceAll("<(S*?)[^>]*>.*?|<.*? />", "");
	     /** 删除转义字符 */
	     s = s.replaceAll("&.{2,6}?;", "");
	     return s;
	   }

	    
}
