package org.easitline.common.core.sso;
import java.util.Map;

import javax.security.auth.Subject;
import javax.security.auth.callback.Callback;
import javax.security.auth.callback.CallbackHandler;
import javax.security.auth.callback.NameCallback;
import javax.security.auth.callback.PasswordCallback;
import javax.security.auth.login.LoginException;
import javax.security.auth.spi.LoginModule;

import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.sso.impl.DefaultLoginFactory;
import org.easitline.common.core.sso.impl.DefaultUserManager;

/**
 * Mars平台缺省登录模块
 * update by cxf 20181023
 */
public class EasyLoginModule implements LoginModule {

	public static final String WEB_REQUEST_KEY = "javax.servlet.http.HttpServletRequest";
	private static String logName = "mars-sso";
	private Subject subject;
	private CallbackHandler handler;
	private boolean loginSuccess = false;
	private boolean commitSuccess = false;
	private String username = null;
	private String password = null;
	
	private UserPrincipal userPrincipal = null;
	private RolePrincipal rolePrincipal = null;
	private LoginFactory loginFactory = new DefaultLoginFactory();
	
	@Override
	public void initialize(Subject subject, CallbackHandler callbackHandler,Map<String, ?> sharedState, Map<String, ?> options) {
		this.subject = subject;
		this.handler = callbackHandler;
	}
	
	@Override
	public boolean login() throws LoginException {
		loginSuccess = false;
		if(handler == null){
			throw new LoginException("Login Handle is null!");
		}
		Callback[] callbacks = new Callback[2];
		callbacks[0] = new NameCallback("j_username");
		callbacks[1] = new PasswordCallback("j_password",false);
		try {
			handler.handle(callbacks);
			username = ((NameCallback)callbacks[0]).getName();
			password = new String(((PasswordCallback)callbacks[1]).getPassword());
			LogEngine.getLogger(logName).debug("执行登录操作,登录用户账号:"+username+"");
			if(username == null || password == null){
				throw new LoginException("SSO登录失败，原因：用户名或密码为空!");
			}
			loginSuccess = loginFactory.login(username,password);
			LogEngine.getLogger(logName).debug("loginFactory.login() login result is :"+loginSuccess);
		} catch (Exception ex) {
			LogEngine.getLogger(logName).error("SSO登录登录异常，原因"+ex.getMessage(),ex);
		}
		return loginSuccess;
	}

	@Override
	public boolean commit() throws LoginException {
		if(loginSuccess == false){
			commitSuccess = false;
			return false;
		}else{
			userPrincipal = loginFactory.createUserPrincipal(username);
			rolePrincipal = loginFactory.createRolePrincipal(username);
			if(!subject.getPrincipals().contains(userPrincipal)){
				subject.getPrincipals().add(userPrincipal);
			}
			if(!subject.getPrincipals().contains(rolePrincipal)){
				subject.getPrincipals().add(rolePrincipal);
			}
			commitSuccess = true;
			return true;
		}
	}

	@Override
	public boolean abort() throws LoginException {
		if(loginSuccess == false){
			return false;
		}
		else if(loginSuccess == true && commitSuccess ==false ){
			loginSuccess = false;
			username = null;
			password = null;
			if(userPrincipal != null && userPrincipal.getUserId() != null) {
				DefaultUserManager.userOffline(username);
			}
			userPrincipal = null;
			rolePrincipal = null;
		}else{
			logout();
		}
		return true;
	}


	@Override
	public boolean logout() throws LoginException {
		LogEngine.getLogger(logName).debug("Easitline soo logout["+this.username+"]");
		subject.getPrincipals().remove(userPrincipal);
		subject.getPrincipals().remove(rolePrincipal);
		loginSuccess = false;
		loginSuccess = commitSuccess;
		if(userPrincipal != null && userPrincipal.getUserId() != null) {
			DefaultUserManager.userOffline(username);
		}
		username = null;
		userPrincipal = null;
		rolePrincipal = null;
		return true;
	}

}
