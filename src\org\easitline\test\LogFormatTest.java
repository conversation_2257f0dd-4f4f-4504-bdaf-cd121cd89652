package org.easitline.test;

import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;

/**
 * Log format test class
 * Used to verify if log output contains class name and line number information
 */
public class LogFormatTest {

    public static void main(String[] args) {
        System.out.println("Starting log format test...");

        // Test different log instances
        testLogFormat();

        System.out.println("Log format test completed");
    }

    public static void testLogFormat() {
        // Create test log instance
        Logger logger = LogEngine.getLogger("test-log");

        // Test different log levels
        logger.debug("This is DEBUG level log - should show class name and line number");
        logger.info("This is INFO level log - should show class name and line number");
        logger.warn("This is WARN level log - should show class name and line number");
        logger.error("This is ERROR level log - should show class name and line number");

        // Test log with exception
        try {
            throw new RuntimeException("Test exception");
        } catch (Exception e) {
            logger.error("Caught exception - should show class name and line number", e);
        }

        // Test log in different method
        testMethodLog();
    }

    private static void testMethodLog() {
        Logger logger = LogEngine.getLogger("method-test");
        logger.info("Log in testMethodLog method - should show method name");
    }
}
