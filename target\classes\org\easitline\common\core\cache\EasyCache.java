package org.easitline.common.core.cache;

public interface EasyCache {
	/**
	 * 加入对象到缓存
	 * @param key 
	 * @param value
	 */
	public  void  put(String key ,Object value);
	
	
	/**
	 * 加入对象到缓存
	 * @param key 
	 * @param value
	 * @param second 
	 */
	public  void  put(String key ,Object value,int second);
	
	/**
	 * 获取缓存对象
	 * @param key
	 * @return
	 */
	public  <T> T get(String key);
	
	public void flushAll();
	
	public  void delete(String key);
	
	/**
	 * 初始化缓存连接对象
	 */
	public void init();
	
	public void stop();
	
	public void reload();
	
}
