<?xml version="1.0" encoding="UTF-8"?>
<project name="easitline-core" default="build" basedir=".">
	<property name="version" value="easitline-core-3.4"/>
	<property name="src" value="src"/>
	<property name="doc" value="docs"/>
	<property name="build" value="bin"/>
	<property name="bin" location="bin"/>
	<tstamp>
			<format property="day" pattern="yyyyMMdd" locale="cn"/>
	</tstamp>
	<property name="JarFile" value="${version}.jar"/>
	<target name="build" depends="clean">
		<jar jarfile="${JarFile}" compress="yes">
			<fileset dir="src" includes="**/*.java"/>
			<fileset dir="${build}" excludes=".svn"/>
		</jar>
	</target>
	<target name="clean">
		<delete verbose="true" includeemptydirs="true">
			<fileset dir="${basedir}">
			<include name="*.jar"/>
		</fileset>
		</delete>
	</target>
</project>