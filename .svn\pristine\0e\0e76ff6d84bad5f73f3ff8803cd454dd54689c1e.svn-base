package org.easitline.common.core.activemq;

import java.io.Serializable;
import java.util.HashMap;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.easitline.common.core.activemq.log.ActivieMQLogger;

/**
 * MQ消息生成者
 * 
 * <AUTHOR>
 * 
 */
public class MQProducer {

	private BlockingQueue<Serializable> blockingQueue;

	private String connector;

	private String brokerName;

	// 添加队列成功数
	private long addQueueSuccessCount = 0;

	// 添加队列失败数
	private long addQueueErrorCount = 0;

	// 发送消息成功数
	private long sendMessageSuccessCount = 0;

	// 发送消息失败数
	private long sendMessageErrorCount = 0;

	private long sendingMessageCount = 0;

	// 线程执行状态，缺省为true，当为false时候，相关执行线程将会关闭
	private boolean threadRunStatus = true;

	// 队列长度
	private int maxQueueLength;

	// 发送消息producer数
	private int prodecerThreadCount;

	private static String INNER_BROKER_URL = "vm://broker";

	private BrokerHelper[] brokerHelpers;

	private Thread[] threads;

	private Object syncObj = new Object();

	private static HashMap<String, MQProducer> producerHandles = new HashMap<String, MQProducer>();

	private MQProducer(String connector, String brokerName, int maxQueueLength,int prodecerThreadCount) {
		this.connector = connector;
		this.brokerName = brokerName;
		this.maxQueueLength = maxQueueLength;
		this.prodecerThreadCount = prodecerThreadCount;
		this.blockingQueue = new ArrayBlockingQueue<Serializable>(this.maxQueueLength); // 初始化队列容量最大为1w
		this.brokerHelpers = new BrokerHelper[this.prodecerThreadCount];
		this.threads = new Thread[this.prodecerThreadCount];
		init();
	}

	private void init() {
		for (int i = 0; i < this.prodecerThreadCount; i++) {
			brokerHelpers[i] = new BrokerHelper(i);
			// 创建新的线程消费者 
			threads[i] = new Thread(new Consumer(brokerHelpers[i]));
			threads[i].start();
		}
		new Thread(new ProducerGuard()).start();
	}

	public static MQProducer getInstance(String brokerName) {
		return MQProducer.getInstance(INNER_BROKER_URL, brokerName, 20000, 10);
	}

	/**
	 * 创建消息队列Producer
	 * 
	 * @param brokerName
	 * @return
	 */
	public static MQProducer getInstance(String brokerName, int maxQueueLength,
			int prodecerThreadCount) {
		return MQProducer.getInstance(INNER_BROKER_URL, brokerName,
				maxQueueLength, prodecerThreadCount);
	}

	/**
	 * 创建消息队列Producer
	 * 
	 * @param brokerUrl
	 *            连接URL，brokerName队列名称
	 * @return
	 */
	public static synchronized MQProducer getInstance(String brokerUrl,String brokerName, int maxQueueLength, int prodecerThreadCount) {
		
		MQProducer producer = producerHandles.get(brokerName);
		if (producer != null)
			return producer;
		ActivieMQLogger.getLogger().debug("Create  new MQProducer(" + brokerName + ")..!");
		try{
			throw new  java.lang.RuntimeException("print MQProducer.getInstance(URL:"+brokerUrl+",brokerName:"+brokerName
					+",queueCount:"+maxQueueLength+",producerCount:"+prodecerThreadCount+") info.");
			
		}catch(Exception ex){
			ActivieMQLogger.getLogger().warn(ex,ex);
		}
		//maxQueueLength  = 10000;
		producer = new MQProducer(brokerUrl, brokerName, maxQueueLength,
				prodecerThreadCount);
		producerHandles.put(brokerName, producer);
	
		return producer;
	}

	public void close() {
		// 设置线程状态
		this.threadRunStatus = false;
		for (int i = 0; i < brokerHelpers.length; i++) {
			threads[i].interrupt();
			brokerHelpers[i].close(); // 关闭broker
			// brokerHelpers[i].
		}
		producerHandles.remove(this.brokerName);
		ActivieMQLogger.getLogger().info("ProducerBroker(" + brokerName+ ")-> Destory MQProducer complete!");
	}

	/**
	 * 发送消息
	 * 
	 * @param obj
	 * @return true 发送成功，false 队列满
	 */
	public boolean sendMessage(Serializable obj) {

		// if(!threadRunStatus) return false;
		// if(true) return true;
		if (obj == null) {
			ActivieMQLogger.getLogger().warn("ProducerBroker("
							+ brokerName
							+ ").sendMessage()-> Serializable obj can not null,obj:"
							+ obj);
		}

		boolean bl = true;
		try {
			this.blockingQueue.put(obj);
		} catch (Exception ex) {
			bl = false;
		}
		synchronized (syncObj) {
			if (bl) {
				addQueueSuccessCount++;
			} else {
				addQueueErrorCount++;
			}
		}
		return bl;
	}

	/**
	 * 消费者守护线程，保护消费者阻塞，假死情况.
	 * 
	 * <AUTHOR>
	 * 
	 */
	private class ProducerGuard implements Runnable {
		int sleepErrorCount = 0;

		public void run() {
			ActivieMQLogger.getLogger().info(
					"ProducerGuard.run(" + brokerName + ")->thread start;");
			while (true) {
				ActivieMQLogger.getLogger().warn(
						"ProducerGuard.healthCheck(" + brokerName
								+ ")->threadRunStatus:" + threadRunStatus
								+ ",addQueueSuccessCount:"
								+ addQueueSuccessCount + ",addQueueErrorCount:"
								+ addQueueErrorCount
								+ ",sendMessageSuccessCount:"
								+ sendMessageSuccessCount
								+ ",sendingMessageCount:" + sendingMessageCount
								+ ",sendMessageErrorCount:"
								+ sendMessageErrorCount + ",queueCount:"+maxQueueLength+"("+
								+ blockingQueue.size()+")");
				// 每隔10秒检查一次
				if (!threadRunStatus)
					return;
				try {
					Thread.sleep(30 * 1000);
					sleepErrorCount = 0;
				} catch (Exception ex) {
					sleepErrorCount++;
					if (sleepErrorCount > 100)
						return;
					ActivieMQLogger.getLogger().error("ProducerGuard.healthCheck("
									+ brokerName
									+ ")->execute ProducerGuard.run() exception,cause:"
									+ ex.getMessage());
					ActivieMQLogger.getLogger().error(ex, ex);
				}
				for (int i = 0; i < brokerHelpers.length; i++) {
					try {
						if (brokerHelpers[i] != null) {
							brokerHelpers[i].check(120 * 1000); // 如果发送时间超过5秒，则直接关闭producer重新连接MQ
						}
					} catch (Exception ex) {
						ActivieMQLogger.getLogger().error(
								"ProducerGuard.healthCheck(" + brokerName
										+ ")->execute brokerHelpers[" + i
										+ "].check(40*1000) exception,cause:"
										+ ex.getMessage());
					}
				}

			}
		}
	}

	/**
	 * MQ消息队列
	 * 
	 * <AUTHOR>
	 * 
	 */
	private class BrokerHelper {

		Long lastSendTime = System.currentTimeMillis(); // 最后更新时间
		boolean sendStatus = false; // 发送状态
		Broker broker = null;
		int brokerOrder;

		int brokerSendCount = 0;
		int brokerSendedCount = 0;
		int brokerSendingCount = 0;
		int brokerErrorCount = 0;

		BrokerHelper(int brokerOrder) {
			this.brokerOrder = brokerOrder;
		}

		/**
		 * 检查producer是否阻塞
		 */
		void check(int maxKeepAlive) {
			if(ActivieMQLogger.getLogger().isInfoEnabled()){
			ActivieMQLogger.getLogger().debug(
					"ProducerBroker(" + brokerName + ":" + this.brokerOrder
							+ ")->sendStatus:" + sendStatus + ",lastSendTime:"
							+ lastSendTime + ",brokerSendCount:"
							+ brokerSendCount + ",brokerSendedCount:"
							+ brokerSendedCount + ",brokerSendingCount:"
							+ brokerSendingCount + ",brokerErrorCount:"
							+ brokerErrorCount);
			}
			if (!sendStatus) {
				this.lastSendTime = System.currentTimeMillis();
				return; // 如果不在发送状态中，则不做任何检查
			}
//			if (System.currentTimeMillis() - lastSendTime > maxKeepAlive) {
//				ActivieMQLogger.getLogger().warn(
//						"ProducerBroker(" + brokerName + ":" + this.brokerOrder
//								+ ")->Message send timeout("
//								+ (System.currentTimeMillis() - lastSendTime)
//								+ ">" + maxKeepAlive
//								+ "), reconnection the broker(" + connector
//								+ "," + brokerName + ") !");
//			}
//			if (System.currentTimeMillis() - lastSendTime > maxKeepAlive) { // 如果超过访问时间没有
//				reconnectBroker(); // 则关闭队列，重新进行
//				this.lastSendTime = System.currentTimeMillis();
//			}
		}

		/**
		 * 重新连接Broker
		 */
		void reconnectBroker() {
			if (broker != null) {
				broker.close();
			}
			broker = BrokerFactory.getProducerQueueBroker(connector,brokerName, "", "");
			ActivieMQLogger.getLogger().info("ProducerBroker(" + brokerName + ":" + this.brokerOrder
							+ ")-> reconnectBroker , New broker Object:"+ broker);
		}

		/**
		 * 关闭队列
		 */
		void close() {
			ActivieMQLogger.getLogger().debug("ProducerBroker(" + brokerName + ":" + this.brokerOrder+ ")-> Start destory MQProducer!");
			try {
				 if(broker != null)broker.close();
			} catch (Exception ex) {

			}
			ActivieMQLogger.getLogger().info("ProducerBroker(" + brokerName + ":" + this.brokerOrder+ ")-> Destory MQProducer complete!");
		}

		/**
		 * 发送消息
		 * 
		 * @param obj
		 */
		boolean send(Serializable obj) {
			brokerSendCount++;
			try {
				if (broker == null)
					reconnectBroker();
				this.lastSendTime = System.currentTimeMillis(); // 更新发送时间
				broker.sendMessage(obj);
				if(ActivieMQLogger.getLogger().isDebugEnabled()){
					ActivieMQLogger.getLogger().debug("<send> broker("+brokerName+")->"+ReflectionToStringBuilder.toString(obj,
								ToStringStyle.SHORT_PREFIX_STYLE));
				}
				return true;
			} catch (Exception ex) {
				ActivieMQLogger.getLogger().error("ProducerBroker("+ brokerName+ ":"+ this.brokerOrder+ ","+ connector+ ")-> send  message  error !,cause:"+ ex.getMessage(), ex);
				return false;
			}

		}
	}

	/**
	 * 消息消费者
	 * 
	 * <AUTHOR>
	 * 
	 */
	private class Consumer implements Runnable {
		BrokerHelper brokerHelper;
		
		Consumer(BrokerHelper brokerHelper) {
			this.brokerHelper = brokerHelper;
		}

		public void run() {

			while (threadRunStatus) {
				try {
					Serializable obj = blockingQueue.take(); // 获取下一个消息
					brokerHelper.sendStatus = true; // 更新状态为空闲
					synchronized (syncObj) {
						sendingMessageCount++; // 正在发送消息数+1
					}
					brokerHelper.brokerSendingCount++;
					boolean bl = brokerHelper.send(obj); // 消费队列信息。
					brokerHelper.brokerSendingCount--;
					synchronized (syncObj) {
						sendingMessageCount--; // 正在发送消息数-1
						if (bl)
							sendMessageSuccessCount++;
						else
							sendMessageErrorCount++;
					}
					brokerHelper.sendStatus = false; // 更新状态为空闲
					// System.out.println("处理队列信息");
				} catch (Exception ex) {
					synchronized (syncObj) {
						sendMessageErrorCount++;
					}
					try {
						Thread.sleep(1000);
					} catch (Exception ex1) {
					}
					ActivieMQLogger.getLogger().error("ProducerBroker("+ brokerName+ ":"
									+ brokerHelper.brokerOrder
									+ ")->blockingQueue.take() run error ,cause:"
									+ ex.getMessage());
				}
			}
		}

	}

}
