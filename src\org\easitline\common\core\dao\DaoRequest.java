package org.easitline.common.core.dao;

import java.io.IOException;
import java.lang.reflect.Method;
import java.util.Iterator;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.engine.MethodEntity;
import org.easitline.common.annotation.engine.TypeEntity;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.common.core.web.render.Render;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
/**
 * 外部web框架继承此类实现dao请求处理
 * 执行excute方法
 */
public class DaoRequest {
	
	private static JSONObject requestToJsonObject(HttpServletRequest request){
        JSONObject jsonObject = new JSONObject();
        Map<?, ?> pmap = request.getParameterMap();
		Iterator<?> it = pmap.keySet().iterator();
		while (it.hasNext()){
		    String key = it.next().toString();
		    String[] values = (String[])pmap.get(key);
		    if(key.equals("data")){
		    	jsonObject.putAll(JSONObject.parseObject(values[0]));
		    }else{
		    	jsonObject.put(key, values[0]);
		    }
		}
        return jsonObject;
	} 
	private static void actionRequest(HttpServletRequest request, HttpServletResponse response,String action){
		String contextPath = request.getContextPath();
		if(action.indexOf(".")==-1){
			CoreLogger.getMarsReq().error(contextPath+"->请求格式错误，原因：controls域为空！");
			Render.renderJson(request,response,"{}");
			return;
		}
		String[] controls=action.split("\\.");
		if(controls.length!=2){
			Render.renderJson(request,response,"{}");
			return;
		}
		JSONObject jsonObject =requestToJsonObject(request);
		TypeEntity typeEntity = DaoFactory.getObject(contextPath, controls[0]);
		if(typeEntity == null){
			CoreLogger.getMarsReq().error(controls[0]+" is not exist.");
			Render.renderJson(request,response,"{}");
			return;
		}
		String method=controls[1];
		if(StringUtils.isNotBlank(method) &&method.lastIndexOf(")")>-1) {
			method = tranMethodParams(method, jsonObject);  
		}
		MethodEntity methodEntity  = typeEntity.getMethodEntity(method);
		if(methodEntity == null){
			CoreLogger.getMarsReq().error(method+" is exist.");
			Render.renderJson(request,response,"{}");
			return;
		}
		JSONObject resultObject = null;
		try {
			resultObject = (JSONObject)invokeMethod(typeEntity.getClazz(), methodEntity.getMothedName(),methodEntity.getType(), jsonObject, request,response);
		} catch (Exception e) {
			CoreLogger.getMarsReq().error(e.getMessage(),e);
			Render.renderJson(request,response,"{}");
			return;
		}
		Render.renderJson(request,response,resultObject);
	}
	public  static void excute(HttpServletRequest request, HttpServletResponse response)throws ServletException, IOException {
		//获得请求对象
		String contextPath = request.getContextPath();
		String action=request.getParameter("action");
		if(StringUtils.notBlank(action)){
			actionRequest(request,response,action);
			return;
		}
		String data = request.getParameter("data");
		if(data == null) {
			CoreLogger.getMarsReq().error(contextPath+"->请求格式错误，原因：data域为空！");
			Render.renderJson(request,response,"{}");
		}
		if(ServerContext.isDebug()){
			CoreLogger.getMarsReq().debug(contextPath+"->data:"+data);
		}
		JSONObject jsonObject  = JSONObject.parseObject(data);
		//请求参数
		JSONObject params = jsonObject.getJSONObject("params");
		if(params == null) {
			CoreLogger.getMarsReq().error(contextPath+"->请求格式错误，原因：params域为空！");
			Render.renderJson(request,response,"{}");
			return;
		}
		//控件
		JSONArray  controls = jsonObject.getJSONArray("controls");
		if(controls == null) {
			CoreLogger.getMarsReq().error(contextPath+"->请求格式错误，原因：controls域为空！");
			Render.renderJson(request,response,"{}");
			return;
		}
		JSONObject result = new JSONObject();
		
		int size = controls.size();
		for(int i = 0 ; i < size ; i++){
			String controlName = controls.getString(i);
			if(controlName.indexOf(".")==-1) {
				CoreLogger.getMarsReq().error(contextPath+"->@WebObject["+controlName+"]命名错误，正确格式为：@WebObject.@WebControl");
				continue;
			}
		
			try{
				String[] item = controlName.split("\\.");
				if(item.length!=2){
					CoreLogger.getMarsReq().error(contextPath+"->@WebObject["+controlName+"]命名错误，正确格式为：@WebObject.@WebControl");
					continue;
				}
				TypeEntity typeEntity = DaoFactory.getObject(contextPath, item[0]);
				if(typeEntity == null){
					CoreLogger.getMarsReq().error(contextPath+"->@WebObject["+item[0]+"] not defined!");
					continue;
				}
				
				//解析方法的参数
				String method = item[1];
				if(StringUtils.isNotBlank(method) &&method.lastIndexOf(")")>-1) {
					method = tranMethodParams(method, params);  
				}
				
				MethodEntity methodEntity  = typeEntity.getMethodEntity(method);
				if(methodEntity == null){
					CoreLogger.getMarsReq().error(contextPath+"->@WebControl["+controlName+"] not found! "+typeEntity);
					continue;
				}
				
				JSONObject  resultObject = (JSONObject)invokeMethod(typeEntity.getClazz(),methodEntity.getMothedName(),methodEntity.getType(), params, request,response);
				if(resultObject == null){
					CoreLogger.getMarsReq().error(contextPath+"->@WebControl["+controlName+"]调用失败，原因：返回值为空或者不是有效的JSONObject!");
					continue;
				}
				resultObject.put("type", methodEntity.getType());
				result.put(controlName, resultObject);
			}catch(Exception ex){
				CoreLogger.getMarsReq().error(contextPath+"->@WebControl["+controlName+"]调用失败，原因："+ex.getMessage(),ex);
			}
		}
		Render.renderJson(request,response,result);
		
	}
	/**
	 * 解析方法参数
	 * @return
	 */
    private static String tranMethodParams(String method, JSONObject params) {
    	 String methodParams = method.substring(method.indexOf("(")+1, method.indexOf(")"));
    	 if(StringUtils.isNotEmpty(methodParams)){
    		 methodParams= methodParams.replaceAll("'","").replaceAll("\"", "");
    		 String[] mParamAry = methodParams.split(",");
    		 for(int j=0; j<mParamAry.length; j++) {
    			 params.put("param["+j+"]", mParamAry[j]);
    		 }
    	 }
    	 method = method.substring(0, method.indexOf("("));
	     return method;
    } 
	
	
	/**
	 * 执行动态方法调用
	 * @param methodName 要执行的方法名
	 * @return 返回结果
	 * @throws Exception
	 */
	private  static Object invokeMethod(Class<?> clazz,String methodName,Types type,JSONObject params,HttpServletRequest request,HttpServletResponse response) throws Exception{
		Object obj = clazz.newInstance();
		Method method = null;

		if (obj instanceof DaoContext) {
			((DaoContext) obj).init(request,response,type,params);
		}
		method = clazz.getDeclaredMethod(methodName);
		return method.invoke(obj);

	}
	
}
