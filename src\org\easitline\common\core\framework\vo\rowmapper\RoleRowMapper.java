package org.easitline.common.core.framework.vo.rowmapper;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.easitline.common.core.framework.vo.Role;
import org.easitline.common.db.EasyRowMapper;


/**

create table EASI_ROLE
(
   ROLE_ID              varchar(32) not null comment '角色ID',
   ROLE_NAME            varchar(30) not null comment '角色名称',
   ROLE_TYPE            int not null comment '角色类型： 0:系统角色，SYS_开头，不可更改，1：业务角色，由应用根据需要定义，2：自定义角色，由系统提供维护',
   APP_ID               varchar(30) comment '所属应用，这里指针对应用的自定义业务角色来确定',
   ROLE_DESC            varchar(100) comment '角色描述',
   primary key (ROLE_ID)
)
 * <AUTHOR>
 *
 */
public class RoleRowMapper implements EasyRowMapper<Role> {

	@Override
	public Role mapRow(ResultSet rs, int rowNum) {
		Role vo = new Role();
		try {
			vo.setAppId(rs.getString("APP_ID"));
			vo.setRoleName(rs.getString("ROLE_NAME"));
			vo.setRoleId(rs.getString("ROLE_ID"));
			vo.setRoleType(rs.getInt("ROLE_TYPE"));
			vo.setRoleDesc(rs.getString("ROLE_DESC"));
		} catch (SQLException ex) {
			ex.printStackTrace();
		}
		return vo;
	}

}
