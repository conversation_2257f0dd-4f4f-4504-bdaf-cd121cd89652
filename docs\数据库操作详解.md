# 数据库操作详解

## 概述

EasitLine Core 提供了强大而简洁的数据库操作接口，本文档详细介绍数据库相关类的使用方法和最佳实践。

## 核心类介绍

### EasyQuery - 数据库查询核心类

EasyQuery 是框架的数据库操作核心类，提供了统一的数据库访问接口。

#### 获取EasyQuery实例

```java
// 方式1：使用默认数据源
EasyQuery query = EasyQuery.getQuery("default-ds");

// 方式2：使用指定应用的数据源
EasyQuery query = EasyQuery.getQuery("myapp", "myapp-ds");

// 方式3：在Servlet中获取（推荐）
public class UserServlet extends EasyBaseServlet {
    public void handleRequest() {
        EasyQuery query = getQuery(); // 自动根据配置获取
    }
}

// 方式4：在DAO中获取（推荐）
public class UserDao extends DaoContext {
    public void queryUsers() {
        EasyQuery query = getQuery(); // 自动根据配置获取
    }
}
```

#### 查询操作详解

##### 1. 基础查询方法

```java
EasyQuery query = getQuery();

// 查询多条记录
List<EasyRow> users = query.queryForList("SELECT * FROM users");

// 带参数查询
List<EasyRow> activeUsers = query.queryForList(
    "SELECT * FROM users WHERE status = ?", 1);

// 多参数查询
List<EasyRow> deptUsers = query.queryForList(
    "SELECT * FROM users WHERE dept_id = ? AND status = ?", 
    deptId, 1);

// 查询单条记录
EasyRow user = query.queryForObject(
    "SELECT * FROM users WHERE user_id = ?", userId);

// 查询数值
int userCount = query.queryForInt("SELECT COUNT(*) FROM users");
long totalAmount = query.queryForLong("SELECT SUM(amount) FROM orders");

// 查询字符串
String userName = query.queryForString(
    "SELECT user_name FROM users WHERE user_id = ?", userId);
```

##### 2. 分页查询

```java
public JSONObject getUserList(int pageNo, int pageSize) {
    EasyQuery query = getQuery();
    
    // 计算偏移量
    int offset = (pageNo - 1) * pageSize;
    
    // 查询数据
    List<EasyRow> users = query.queryForList(
        "SELECT user_id, user_name, email, create_time " +
        "FROM users WHERE status = 1 " +
        "ORDER BY create_time DESC LIMIT ?, ?", 
        offset, pageSize);
    
    // 查询总数
    int total = query.queryForInt(
        "SELECT COUNT(*) FROM users WHERE status = 1");
    
    // 构造返回结果
    JSONObject result = new JSONObject();
    result.put("data", users);
    result.put("total", total);
    result.put("pageNo", pageNo);
    result.put("pageSize", pageSize);
    result.put("totalPages", (total + pageSize - 1) / pageSize);
    
    return result;
}
```

##### 3. 复杂查询

```java
// 动态条件查询
public List<EasyRow> searchUsers(String userName, String email, Integer status) {
    StringBuilder sql = new StringBuilder();
    List<Object> params = new ArrayList<>();
    
    sql.append("SELECT * FROM users WHERE 1=1 ");
    
    if (StringUtils.notBlank(userName)) {
        sql.append("AND user_name LIKE ? ");
        params.add("%" + userName + "%");
    }
    
    if (StringUtils.notBlank(email)) {
        sql.append("AND email = ? ");
        params.add(email);
    }
    
    if (status != null) {
        sql.append("AND status = ? ");
        params.add(status);
    }
    
    sql.append("ORDER BY create_time DESC");
    
    return query.queryForList(sql.toString(), params.toArray());
}

// 关联查询
public List<EasyRow> getUsersWithDepartment() {
    String sql = "SELECT u.user_id, u.user_name, u.email, " +
                 "d.dept_name, d.dept_code " +
                 "FROM users u " +
                 "LEFT JOIN departments d ON u.dept_id = d.dept_id " +
                 "WHERE u.status = 1 " +
                 "ORDER BY d.dept_name, u.user_name";
    
    return query.queryForList(sql);
}

// 聚合查询
public JSONObject getUserStatistics() {
    EasyQuery query = getQuery();
    
    // 按部门统计用户数
    List<EasyRow> deptStats = query.queryForList(
        "SELECT d.dept_name, COUNT(u.user_id) as user_count " +
        "FROM departments d " +
        "LEFT JOIN users u ON d.dept_id = u.dept_id AND u.status = 1 " +
        "GROUP BY d.dept_id, d.dept_name " +
        "ORDER BY user_count DESC");
    
    // 按状态统计
    List<EasyRow> statusStats = query.queryForList(
        "SELECT status, COUNT(*) as count " +
        "FROM users " +
        "GROUP BY status");
    
    JSONObject result = new JSONObject();
    result.put("deptStats", deptStats);
    result.put("statusStats", statusStats);
    
    return result;
}
```

#### 更新操作详解

##### 1. 使用SQL语句更新

```java
EasyQuery query = getQuery();

// 更新单条记录
int affected = query.update(
    "UPDATE users SET user_name = ?, email = ? WHERE user_id = ?",
    "新用户名", "<EMAIL>", userId);

// 批量更新
int affected = query.update(
    "UPDATE users SET status = 0 WHERE dept_id = ?", deptId);

// 删除操作
int affected = query.update(
    "DELETE FROM users WHERE user_id = ?", userId);

// 插入操作
int affected = query.update(
    "INSERT INTO users (user_id, user_name, email, create_time) " +
    "VALUES (?, ?, ?, ?)",
    RandomKit.uuid(), "张三", "<EMAIL>", new Date());
```

##### 2. 使用EasyRecord更新

```java
// 插入记录
EasyRecord newUser = new EasyRecord("users", "user_id");
newUser.set("user_id", RandomKit.uuid());
newUser.set("user_name", "李四");
newUser.set("email", "<EMAIL>");
newUser.set("dept_id", deptId);
newUser.set("status", 1);
newUser.set("create_time", new Date());

query.save(newUser);

// 更新记录
EasyRecord updateUser = new EasyRecord("users", "user_id");
updateUser.set("user_id", userId);
updateUser.set("user_name", "李四修改");
updateUser.set("email", "<EMAIL>");
updateUser.set("update_time", new Date());

query.update(updateUser);

// 删除记录
EasyRecord deleteUser = new EasyRecord("users", "user_id");
deleteUser.set("user_id", userId);

query.delete(deleteUser);
```

#### 事务管理详解

##### 1. 基础事务操作

```java
EasyQuery query = getQuery();

// 开始事务
query.begin();

try {
    // 操作1：创建用户
    EasyRecord user = new EasyRecord("users", "user_id");
    user.set("user_id", userId);
    user.set("user_name", "张三");
    query.save(user);
    
    // 操作2：创建用户登录信息
    EasyRecord login = new EasyRecord("user_login", "user_id");
    login.set("user_id", userId);
    login.set("login_name", "zhangsan");
    login.set("password", MD5Util.getHexMD5("123456"));
    query.save(login);
    
    // 操作3：分配角色
    EasyRecord userRole = new EasyRecord("user_roles", "id");
    userRole.set("id", RandomKit.uuid());
    userRole.set("user_id", userId);
    userRole.set("role_id", roleId);
    query.save(userRole);
    
    // 提交事务
    query.commit();
    
    return EasyResult.ok("用户创建成功");
    
} catch (Exception e) {
    // 回滚事务
    query.rollback();
    error("用户创建失败", e);
    return EasyResult.error("用户创建失败");
}
```

##### 2. 嵌套事务处理

```java
public EasyResult createUserWithProfile(JSONObject userInfo, JSONObject profileInfo) {
    EasyQuery query = getQuery();
    
    // 检查是否已在事务中
    boolean inTransaction = !query.getConnection().getAutoCommit();
    
    if (!inTransaction) {
        query.begin();
    }
    
    try {
        // 创建用户基本信息
        EasyResult userResult = createUser(userInfo);
        if (!userResult.isOk()) {
            throw new RuntimeException("创建用户失败");
        }
        
        // 创建用户详细信息
        String userId = userResult.getString("userId");
        EasyResult profileResult = createUserProfile(userId, profileInfo);
        if (!profileResult.isOk()) {
            throw new RuntimeException("创建用户详情失败");
        }
        
        if (!inTransaction) {
            query.commit();
        }
        
        return EasyResult.ok("用户创建成功");
        
    } catch (Exception e) {
        if (!inTransaction) {
            query.rollback();
        }
        throw e;
    }
}
```

##### 3. 批量操作事务

```java
public EasyResult batchCreateUsers(List<JSONObject> userList) {
    EasyQuery query = getQuery();
    query.begin();
    
    try {
        int successCount = 0;
        
        for (int i = 0; i < userList.size(); i++) {
            JSONObject userInfo = userList.get(i);
            
            try {
                // 创建用户
                EasyRecord user = new EasyRecord("users", "user_id");
                user.setColumns(userInfo);
                user.set("user_id", RandomKit.uuid());
                user.set("create_time", new Date());
                
                query.save(user);
                successCount++;
                
                // 每100条提交一次，避免事务过大
                if (i > 0 && i % 100 == 0) {
                    query.commit();
                    query.begin();
                }
                
            } catch (Exception e) {
                error("创建用户失败: " + userInfo.toJSONString(), e);
                // 继续处理下一个用户，不中断整个批量操作
            }
        }
        
        query.commit();
        
        return EasyResult.ok("批量创建完成，成功: " + successCount + 
                           ", 失败: " + (userList.size() - successCount));
        
    } catch (Exception e) {
        query.rollback();
        error("批量创建用户失败", e);
        return EasyResult.error("批量创建失败");
    }
}
```

### EasyRecord - 数据记录操作类

EasyRecord 提供了面向对象的数据库记录操作方式。

#### 基础使用

```java
// 创建记录对象
EasyRecord user = new EasyRecord("users", "user_id");

// 设置字段值
user.set("user_id", RandomKit.uuid());
user.set("user_name", "张三");
user.set("email", "<EMAIL>");
user.set("create_time", new Date());

// 批量设置字段
JSONObject userInfo = new JSONObject();
userInfo.put("user_name", "李四");
userInfo.put("email", "<EMAIL>");
user.setColumns(userInfo);

// 获取字段值
String userName = user.get("user_name");
String email = user.get("email");

// 获取所有字段
Map<String, Object> allColumns = user.getColumns();
```

#### 高级功能

```java
// 设置主键值（用于更新和删除）
user.setPrimaryValues(userId);

// 清空所有字段
user.clear();

// 复制记录
EasyRecord newUser = new EasyRecord("users", "user_id");
newUser.setColumns(user.getColumns());

// 字段验证
if (user.get("user_name") == null) {
    throw new IllegalArgumentException("用户名不能为空");
}

// 类型转换
Integer status = user.get("status");
Date createTime = user.get("create_time");
```

### EasyRow - 查询结果行

EasyRow 表示查询结果中的一行数据。

#### 基础使用

```java
// 从查询结果获取数据
List<EasyRow> users = query.queryForList("SELECT * FROM users");

for (EasyRow row : users) {
    // 获取字段值（字符串类型）
    String userId = row.getColumnValue("user_id");
    String userName = row.getColumnValue("user_name");
    
    // 获取指定类型的字段值
    Integer status = row.getColumnValue("status", Integer.class);
    Date createTime = row.getColumnValue("create_time", Date.class);
    
    // 获取所有字段名
    List<String> columnNames = row.getColumnNames();
}
```

#### 转换为其他格式

```java
// 转换为JSONObject
public JSONObject rowToJson(EasyRow row) {
    JSONObject json = new JSONObject();
    
    for (String columnName : row.getColumnNames()) {
        json.put(columnName, row.getColumnValue(columnName));
    }
    
    return json;
}

// 转换为Map
public Map<String, Object> rowToMap(EasyRow row) {
    Map<String, Object> map = new HashMap<>();
    
    for (String columnName : row.getColumnNames()) {
        map.put(columnName, row.getColumnValue(columnName));
    }
    
    return map;
}

// 批量转换
public List<JSONObject> rowsToJsonList(List<EasyRow> rows) {
    List<JSONObject> jsonList = new ArrayList<>();
    
    for (EasyRow row : rows) {
        jsonList.add(rowToJson(row));
    }
    
    return jsonList;
}
```

## 数据库连接池配置

### 基础配置

```java
// 数据源配置示例
DSResource ds = new DSResource();
ds.setDatasourceName("myapp-ds");
ds.setDriverClassName("com.mysql.cj.jdbc.Driver");
ds.setUrl("***********************************************************************");
ds.setUsername("root");
ds.setPassword("password");

// 连接池配置
ds.setMaxActive(20);        // 最大活跃连接数
ds.setMaxIdle(10);          // 最大空闲连接数
ds.setMinIdle(5);           // 最小空闲连接数
ds.setInitialSize(5);       // 初始连接数
ds.setMaxWait(30000);       // 最大等待时间（毫秒）

// 连接验证
ds.setTestOnBorrow(true);   // 借用连接时验证
ds.setTestOnReturn(false);  // 归还连接时验证
ds.setTestWhileIdle(true);  // 空闲时验证
ds.setValidationQuery("SELECT 1"); // 验证查询

// 连接回收
ds.setRemoveAbandoned(true);        // 回收废弃连接
ds.setRemoveAbandonedTimeout(300);  // 废弃连接超时时间（秒）
ds.setLogAbandoned(true);           // 记录废弃连接日志
```

### 多数据源配置

```java
// 主数据库
DSResource mainDs = new DSResource();
mainDs.setDatasourceName("main-ds");
mainDs.setUrl("******************************");
// ... 其他配置

// 从数据库（只读）
DSResource slaveDs = new DSResource();
slaveDs.setDatasourceName("slave-ds");
slaveDs.setUrl("*******************************");
// ... 其他配置

// 在代码中使用
public class UserDao extends DaoContext {
    
    // 写操作使用主库
    public void createUser(EasyRecord user) {
        EasyQuery mainQuery = EasyQuery.getQuery("main-ds");
        mainQuery.save(user);
    }
    
    // 读操作使用从库
    public List<EasyRow> queryUsers() {
        EasyQuery slaveQuery = EasyQuery.getQuery("slave-ds");
        return slaveQuery.queryForList("SELECT * FROM users");
    }
}
```

## 性能优化建议

### 1. 查询优化

```java
// 避免SELECT *，只查询需要的字段
List<EasyRow> users = query.queryForList(
    "SELECT user_id, user_name, email FROM users WHERE status = 1");

// 使用索引字段进行查询
List<EasyRow> users = query.queryForList(
    "SELECT * FROM users WHERE user_id = ?", userId); // user_id有索引

// 避免在WHERE子句中使用函数
// 错误示例
List<EasyRow> users = query.queryForList(
    "SELECT * FROM users WHERE DATE(create_time) = ?", date);

// 正确示例
List<EasyRow> users = query.queryForList(
    "SELECT * FROM users WHERE create_time >= ? AND create_time < ?", 
    startTime, endTime);
```

### 2. 批量操作优化

```java
// 批量插入优化
public void batchInsertUsers(List<JSONObject> userList) {
    EasyQuery query = getQuery();
    
    // 构建批量插入SQL
    StringBuilder sql = new StringBuilder();
    sql.append("INSERT INTO users (user_id, user_name, email, create_time) VALUES ");
    
    List<Object> params = new ArrayList<>();
    for (int i = 0; i < userList.size(); i++) {
        if (i > 0) sql.append(", ");
        sql.append("(?, ?, ?, ?)");
        
        JSONObject user = userList.get(i);
        params.add(RandomKit.uuid());
        params.add(user.getString("user_name"));
        params.add(user.getString("email"));
        params.add(new Date());
    }
    
    query.update(sql.toString(), params.toArray());
}
```

### 3. 连接管理优化

```java
// 及时关闭连接（框架会自动管理，但在特殊情况下需要手动处理）
public void longRunningOperation() {
    EasyQuery query = null;
    try {
        query = getQuery();
        
        // 长时间运行的操作
        for (int i = 0; i < 10000; i++) {
            // 处理逻辑
            
            // 每1000条释放一次连接，避免长时间占用
            if (i > 0 && i % 1000 == 0) {
                query.close(); // 如果框架提供close方法
                query = getQuery(); // 重新获取连接
            }
        }
        
    } finally {
        if (query != null) {
            // 确保连接被释放
        }
    }
}
```

## 常见问题和解决方案

### 1. 连接池耗尽

**问题**：应用运行一段时间后出现"无法获取数据库连接"错误

**解决方案**：
```java
// 检查连接池状态
public void checkConnectionPool() {
    try {
        EasyQuery query = getQuery();
        int result = query.queryForInt("SELECT 1");
        info("数据库连接正常");
    } catch (Exception e) {
        error("数据库连接异常", e);
        
        // 检查连接池配置
        // 1. 增加maxActive值
        // 2. 减少maxWait时间
        // 3. 启用连接回收机制
        // 4. 检查是否有连接泄漏
    }
}
```

### 2. 事务死锁

**问题**：并发操作时出现死锁

**解决方案**：
```java
// 统一事务操作顺序，避免死锁
public void transferMoney(String fromUserId, String toUserId, BigDecimal amount) {
    EasyQuery query = getQuery();
    
    // 按用户ID排序，确保锁定顺序一致
    String firstUserId = fromUserId.compareTo(toUserId) < 0 ? fromUserId : toUserId;
    String secondUserId = fromUserId.compareTo(toUserId) < 0 ? toUserId : fromUserId;
    
    query.begin();
    try {
        // 先锁定ID较小的用户
        EasyRow firstUser = query.queryForObject(
            "SELECT * FROM users WHERE user_id = ? FOR UPDATE", firstUserId);
        
        // 再锁定ID较大的用户
        EasyRow secondUser = query.queryForObject(
            "SELECT * FROM users WHERE user_id = ? FOR UPDATE", secondUserId);
        
        // 执行转账逻辑
        // ...
        
        query.commit();
    } catch (Exception e) {
        query.rollback();
        throw e;
    }
}
```

### 3. 大数据量查询内存溢出

**问题**：查询大量数据时出现内存溢出

**解决方案**：
```java
// 使用分页查询处理大数据量
public void processLargeDataSet() {
    int pageSize = 1000;
    int pageNo = 1;
    
    while (true) {
        int offset = (pageNo - 1) * pageSize;
        
        List<EasyRow> batch = query.queryForList(
            "SELECT * FROM large_table ORDER BY id LIMIT ?, ?", 
            offset, pageSize);
        
        if (batch.isEmpty()) {
            break; // 没有更多数据
        }
        
        // 处理当前批次数据
        processBatch(batch);
        
        pageNo++;
        
        // 可选：添加延迟，减少数据库压力
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            break;
        }
    }
}
```
