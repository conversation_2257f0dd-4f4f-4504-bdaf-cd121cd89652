 package org.easitline.common.core.cache.impl;

import org.easitline.common.core.cache.EasyCache;

/**
 *中国电信 CTG-Cache 分布式缓存中间件
 *commons-pool2-2.11.1.jar,jedis-2.9.0.jar
 *ctg-cache-nclient-2.7.8.jar
 *jedis-2.9.0 无redis.clients.jedis.util.SafeEncoder,只有redis.clients.util.SafeEncoder，JdkSerializer类需要替换下SafeEncoder
 */
public class CtgRedisImpl implements EasyCache {

	@Override
	public void put(String key, Object value) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void put(String key, Object value, int second) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public <T> T get(String key) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void flushAll() {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void delete(String key) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void init() {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void stop() {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void reload() {
		// TODO Auto-generated method stub
		
	}
	
}




