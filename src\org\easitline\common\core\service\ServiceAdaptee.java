package org.easitline.common.core.service;




import com.alibaba.fastjson.JSONObject;

import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.common.core.resource.ServiceResource;

/**
 * 服务调用代理类，实现进行服务的调用。
 * <AUTHOR>
 *
 */
public class ServiceAdaptee extends IService {
	
	 /**
	  * 服务对象
	  */
	private IService serviceStud;
	
	private ServiceResource serviceResource;
	
	
	public ServiceAdaptee(IService serviceStud,ServiceResource serviceResource){
		this.serviceResource = serviceResource;
		this.serviceStud     = serviceStud;
	}
	
	private void startEvent(){
		
	}
	

	@Override
	public JSONObject invoke(JSONObject jsonObject) throws ServiceException {
		this.startEvent();
		try {
			JSONObject result =  serviceStud.invoke(jsonObject);
			return result;
		} catch (ServiceException ex) {
			CoreLogger.getPlatform().warn("ErrorCode:"+ex.getErrorCode()+",服务["+this.serviceResource.serviceId+"]调用失败，原因："+ex.getMessage());
			throw ex;
		}finally{
			this.endEvent();
		}
		
		
	}
	
	private void endEvent(){
		
	}

}
