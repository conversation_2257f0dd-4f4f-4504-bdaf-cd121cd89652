package org.easitline.common.api;

public enum SystemCode {
	API_OK(200,"接口调用成功!"), 
	API_SERVICE_NO_EXIST(500,"接口不存在!"), 
	API_METHOD_EXCEPTION(510,"调用过程发生错误!"), 
	API_INPUT_PARAM_ERROR(511,"输入参数有误!"), 
	API_APP_KEY_NO_REGISTER(512,"APP_KEY未注册!"), 
	API_SIGN_ERROR(513,"数字签名有误!"), 
	API_ACCESS_TOKEN_NO_EXISTENCE(514,"ACCESS_TOKEN不存在!"), 
	API_ACCESS_TOKEN_HAVE_LOGOUT(515,"用户已注销!"), 
	API_ACCESS_TOKEN_OVERTIME(516,"ACCESS_TOKEN超时，需要刷新TOKEN!"), 
	API_TIMSTAMP_OVER_TIME(517,"客户端时间戳超时!"), 
	API_SYSTEM_IS_UPDATING(518,"系统正在维护中!"), 
	API_OTHER_ERROR(519,"未知错误!");
	
	
	public int code;
	public String message;

    // 构造方法
    private SystemCode(int code) {
       this.code = code;
    }
    private SystemCode(int code,String message) {
        this.code = code;
        this.message = message;
     }
    
    public int getCode(){
    	return this.code;
    }
}
