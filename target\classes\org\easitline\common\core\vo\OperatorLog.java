package org.easitline.common.core.vo;

import java.sql.SQLException;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.log.JDBCErrorLogger;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONObject;
/**
 * 操作日志
 */
public class OperatorLog extends EasyRecord {
	
	private static final long serialVersionUID = 9188887943873848741L;
	
	private JSONObject contentJson=new JSONObject();
	
	public OperatorLog(){
		setTableInfo("EASI_OPERATOR_LOG", "SERIAL_ID");
		this.setType(0);
	}
	
	public OperatorLog setUserId(String userId){
		this.set("USER_ID", userId);
		return this;
	}
	public OperatorLog setUserAcct(String userAcct){
		this.set("USER_ACCT", userAcct);
		return this;
	}
	public OperatorLog setIpAddr(String ipAddr){
		this.set("IP_ADDR", ipAddr);
		return this;
	}
	public OperatorLog setModule(String module){
		this.set("OPR_MODULE", module);
		return this;
	}
	/**
	 * 
	 * @param type 0:未知 1:增 2:删 3:改 4:查
	 * @param content 操作内容
	 */
	public OperatorLog(int type,String content){
		this.setType(type);
		contentJson.put("content", content);
	}
	/**
	 * @param type * 0:未知 1:增 2:删 3:改 4:查
	 * @param content 操作内容
	 * @param params  操作参数
	 */
	public OperatorLog(int type,String content,Object params){
		this.setType(type);
		contentJson.put("content", content);
		contentJson.put("params", params);
	}
	/**
	 * 0:未知 1:增 2:删 3:改 4:查
	 * @param type 操作类型
	 * @return
	 */
	
	/**
	 * @param type 0:未知 1:增 2:删 3:改 4:查
	 * @return
	 */
	public EasyRecord setType(int type){
		this.set("OPR_TYPE", type);
		switch (type) {
		case 1:
			contentJson.put("type", "新增");
			break;
		case 2: 
			contentJson.put("type", "删除");
			break;
		case 3: 
			contentJson.put("type", "修改");
			break;
		case 4: 
			contentJson.put("type", "查询");
			break;
		default:
			contentJson.put("type", "未知");
			break;
		}
		return this;
	}
	
	/**
	 * 操作内容
	 * @param url
	 * @return
	 */
	public OperatorLog setContent(String content){
		contentJson.put("content", content);
		return this;
	}
	/**
	 * 执行的sql
	 * @param sql
	 * @return
	 */
	public OperatorLog setSql(String sql){
		contentJson.put("sql", sql);
		return this;
	}
	
	/**
	 * 执行使用的参数
	 * @param params
	 * @return
	 */
	public OperatorLog setParams(Object params){
		contentJson.put("params", params);
		return this;
	}
	
	public OperatorLog setSessionId(String sessionId){
		contentJson.put("sessionId", sessionId);
		return this;
	}
	public OperatorLog setUrl(String url){
		contentJson.put("url", url);
		return this;
	}
	public Object getParams(){
		return contentJson.get("params");
	}
	
	public void save(){
		try {
			this.setTableInfo("EASI_OPERATOR_LOG", "SERIAL_ID");
			this.setPrimaryValues(RandomKit.randomStr());
			this.set("OPR_CONTENT",contentJson.toJSONString());
			EasyQuery easyQuery=ServerContext.getAdminQuery();
			this.set("OPR_TIME",EasyDate.getCurrentDateString());
			easyQuery.save(this);
			/*DBTypes dbType=easyQuery.getTypes();
			if(dbType == DBTypes.ORACLE){
				easyRecord.set("OPR_TIME",EasyDate.getCurrentDateString(null));
				String sql="insert into "+easyRecord.getTableName()+"(OPR_TIME,SERIAL_ID,USER_ID,USER_ACCT,IP_ADDR,OPR_TYPE,OPR_MODULE,OPR_CONTENT) values(to_date(?, 'YYYY-MM-DD hh24:mi:ss'),?,?,?,?,?,?,?)";
				easyQuery.execute(sql, new Object[]{EasyDate.getCurrentDateString(null),easyRecord.get("SERIAL_ID"),easyRecord.get("USER_ID"),easyRecord.get("USER_ACCT"),easyRecord.get("IP_ADDR"),easyRecord.get("OPR_TYPE"),easyRecord.get("OPR_MODULE"),easyRecord.get("OPR_CONTENT")});
			}else{
				easyRecord.set("OPR_TIME",EasyDate.getCurrentDateString(null));
				easyQuery.save(easyRecord);
			}*/
		} catch (SQLException e) {
			CoreLogger.getLogger().error(e.getMessage(),e);
		}
	}
}
