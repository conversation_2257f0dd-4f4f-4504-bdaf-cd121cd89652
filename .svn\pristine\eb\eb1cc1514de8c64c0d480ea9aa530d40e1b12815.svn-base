package org.easitline.common.core.log;

public class MarsLogger{
	/*
	private static Map<String, MarsLogger> loggerCache = new ConcurrentHashMap<String,MarsLogger>();
	
	private String logName;

	public MarsLogger(String logName) {
		this.logName = logName;
	}
	
	public static MarsLogger getLogger(String logName) {
	    if (logName == null) {
	        logName = "easitline-app";
	    }
	    MarsLogger logger = loggerCache.get(logName);
	    if (logger == null) {
	        synchronized (logName.intern()) {
	            logger = loggerCache.get(logName);
	            if (logger == null) {
	                logger = new MarsLogger(logName);
	                loggerCache.put(logName, logger);
	            }
	        }
	    }
	    return logger;
	}
	 
	private void printLog(String logLevel,Object message,Throwable t) {
		if(ServerContext.isLog4j2()) {
			if(message==null) {message = "";}
			LogEngine.getLogger2(logName).log(Level.toLevel(logLevel.toUpperCase(),Level.ALL),message,t);
		}else {
			LogEngine.getLogger(logName).log(org.apache.log4j.Level.toLevel(logLevel.toUpperCase(),org.apache.log4j.Level.ALL), message, t);
		}
	}
	
	public void debug(Object message) {
		this.printLog("debug",message,null);
	}
	
	public void debug(Object message, Throwable t) {
		this.printLog("debug",message,t);
	}
	
	public void info(Object message) {
		this.printLog("info",message,null);
	}
	
	public void info(Object message, Throwable t) {
		this.printLog("info",message,t);
	}
	
	public void warn(Object message) {
		this.printLog("warn",message,null);
	}
	
	public void warn(Object message, Throwable t) {
		this.printLog("warn",message,t);
	}
	
	public void error(Object message) {
		this.printLog("error",message,null);
	}
	
	public void error(Object message, Throwable t) {
		this.printLog("error",message,t);
	}
	
	public void fatal(Object message) {
		this.printLog("fatal",message,null);
	}
	
	public void fatal(Object message, Throwable t) {
		this.printLog("fatal",message,t);
	}
	
	public void trace(Object message) {
		this.printLog("trace",message,null);
	}
	public void trace(Object message, Throwable t) {
		this.printLog("trace",message,t);
	}
	
	*/
}
