package org.easitline.common.core.sso.impl;


import java.sql.SQLException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.security.auth.login.LoginException;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.sso.LoginFactory;
import org.easitline.common.core.sso.LoginSecurityImpl;
import org.easitline.common.core.sso.RolePrincipal;
import org.easitline.common.core.sso.UserPrincipal;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.crypt.MD5Util;

import com.alibaba.fastjson.JSONObject;

public class DefaultLoginFactory extends LoginSecurityImpl implements LoginFactory {
	
	private static String logName = "mars-sso";
	
	private EasyQuery query = ServerContext.getAdminQuery();
	
	@Override
	public boolean login(String username, String password) throws LoginException {
		// 登录频率限制
		if (!DefaultUserManager.checkAndRecordLogin(username)) {
			LogEngine.getLogger(logName).warn("账号[" + username + "] 2秒内重复登录，已拒绝。");
			return failedLogin(username, "操作过于频繁，请稍后再试");
		}
				
		//根据账号获取用户USER_ID
		String sql="SELECT t1.USER_ID,t1.IP_RANGE,t2.TRY_LOGIN_TIMES FROM EASI_USER t1,EASI_USER_LOGIN t2 WHERE t1.USER_ID = t2.USER_ID and t1.STATE = 0 and t2.USER_ACCT=?";
		EasyRow row = null;
		try {
			row = query.queryForRow(sql, new Object[]{username});
			if(row==null){
				return failedLogin("账号或密码错误!");
			}
		} catch (SQLException e) {
			LogEngine.getLogger(logName).error(e.getMessage(),e);
		}
		sql = "SELECT * FROM EASI_USER_LOGIN WHERE LOGIN_TYPE = 1 AND LOCK_STATE = 0 and USER_ACCT=? ";
		try {
			JSONObject userInfo = query.queryForRow(sql, new Object[] {username},new JSONMapperImpl());
			if (userInfo == null)return false;
			String userPwd = userInfo.getString("USER_PWD");
			String userId = userInfo.getString("USER_ID");
			//先判断锁住没有
			String _pwd = MD5Util.getHexMD5(password.toUpperCase()+userId);
			if (!userPwd.equalsIgnoreCase(password)&&!userPwd.equalsIgnoreCase(_pwd)) {
				LogEngine.getLogger(logName).info("DefaultLoginFactory.login("+username+") error->"+"密码错误！");
				return failedLogin(username,"账号或密码错误！");
			}
			
			boolean flag = unLock(username);
			LogEngine.getLogger(logName).info("DefaultLoginFactory.login("+username+") error->unLock 结果为："+flag);
			if(flag) {
				sql="update EASI_USER_LOGIN set TRY_LOGIN_TIMES = TRY_LOGIN_TIMES + 1,LAST_LOGIN_TIME= ? where USER_ID = ?";
				query.execute(sql,EasyDate.getCurrentDateString(),row.getColumnValue("USER_ID"));
				DefaultUserManager.userOnline(userId,username);
			}
			return flag;
		} catch (Exception ex) {
			LogEngine.getLogger(logName).error(ex.getMessage(),ex);
			return false;
		}
	}
	
	@Override
	public UserPrincipal createUserPrincipal(String username) throws LoginException {
		DefaultUserPrincipal userPrincipal = new DefaultUserPrincipal();
		String sql = "select USER_ID from EASI_USER_LOGIN  where  LOGIN_TYPE = 1 AND LOCK_STATE=0 and USER_ACCT=?";
		try {
			String userId = query.queryForString(sql, new Object[] { username });
			sql = "select IP_RANGE,USER_ID,USERNAME,MOBILE,EMAIL,NIKE_NAME from EASI_USER  where STATE<>9 and USER_ID=?";
			EasyRow easyRow = query.queryForRow(sql, new Object[] { userId });
			userPrincipal.setUserId(userId);
			userPrincipal.setLoginAcct(username);
			userPrincipal.setLoginType(1);
		  //userPrincipal.setRootId(easyRow.getColumnValue("ROOT_ID"));
			userPrincipal.setMobile(easyRow.getColumnValue("MOBILE"));
			userPrincipal.setEmail(easyRow.getColumnValue("EMAIL"));
			userPrincipal.setNikeName(easyRow.getColumnValue("NICK_NAME"));
			userPrincipal.setUserName(easyRow.getColumnValue("USERNAME"));
			userPrincipal.setRoles(this.getUserRoles(userId));
			userPrincipal.setResources(this.getUserResources(userId));
			userPrincipal.setAttribute("ipRange", easyRow.getColumnValue("IP_RANGE"));
			//userPrincipal.getUserInfo();
		} catch (SQLException ex) {
			LogEngine.getLogger(logName).error("DefaultLoginFactory.createUserPrincipal("+username+")->生成当前登录的Principal失败，原因："+ex.getMessage(),ex);
			LoginException loginException  = new LoginException(ex.getMessage());
			loginException.setStackTrace(ex.getStackTrace());
			throw loginException;
		}
		return userPrincipal;
	}
	
	/**
	 * 获得当前用户的资源列表
	 * @param userId
	 * @return
	 * @throws SQLException
	 */
	private Set<String>  getUserResources(String userId) throws SQLException{
		HashSet<String> set = new HashSet<String>();
		String sql = "SELECT DISTINCT RES_ID FROM( SELECT t1.RES_ID FROM easi_res t1 JOIN easi_user_res t2 ON t1.RES_ID = t2.RES_ID WHERE t2.USER_ID = ? UNION SELECT t1.RES_ID FROM easi_res t1 JOIN easi_role_res t2 ON t1.RES_ID = t2.RES_ID JOIN easi_role_user t3 ON t2.ROLE_ID = t3.ROLE_ID WHERE t3.USER_ID = ? UNION SELECT t1.RES_ID FROM easi_res t1 JOIN easi_dept_res t3 ON t3.RES_ID = t1.RES_ID JOIN easi_dept_user t2 ON t3.DEPT_ID = t2.DEPT_ID WHERE t2.USER_ID = ?) tt";
		List<EasyRow> list;
		try {
			list = query.queryForList(sql, new Object[] { userId,userId,userId} );
			for(EasyRow row : list){
				set.add(row.getColumnValue("RES_ID"));
			}
		} catch (SQLException ex) {
			LogEngine.getLogger(logName).error("DefaultLoginFactory.getUserResources("+userId+")->获取用户的角色列表异常，原因："+ex.getMessage(),ex);
			throw ex;
		}
		
		return set;
	}
	
	/**
	 * 获得当前用户角色列表
	 * @param userId
	 * @return
	 * @throws SQLException
	 */
	private Set<String>  getUserRoles(String userId) throws SQLException{
		HashSet<String> set = new HashSet<String>();
		String sql = "select ROLE_ID  from EASI_ROLE_USER  where USER_ID = ?";
		List<EasyRow> list;
		try {
			list = query.queryForList(sql, new Object[] { userId } );
			for(EasyRow row : list){
				set.add(row.getColumnValue("ROLE_ID"));
			}
		} catch (SQLException ex) {
			LogEngine.getLogger(logName).error("DefaultLoginFactory.getUserRoles("+userId+")->获取用户的角色列表异常，原因："+ex.getMessage(),ex);
			throw ex;
		}
		
		return set;
	}

	@Override
	public RolePrincipal createRolePrincipal(String user) throws LoginException {
		return new DefaultRolePrincipal("everyone");
	}
	
	  /**
     * 查询所有在线用户
     */
    public java.util.Collection<String> listOnlineUsers() {
        return DefaultUserManager.listOnlineUsers();
    }

    /**
     * 踢指定用户下线
     */
    public boolean kickUserOffline(String userAcct) {
        return DefaultUserManager.kickUserOffline(userAcct);
    }
  
}
