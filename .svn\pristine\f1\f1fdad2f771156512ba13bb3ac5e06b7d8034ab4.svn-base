package org.easitline.common.core.log.impl;

/*
import java.io.File;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.core.Appender;
import org.apache.logging.log4j.core.LoggerContext;
import org.apache.logging.log4j.core.appender.RollingFileAppender;
import org.apache.logging.log4j.core.appender.rolling.CompositeTriggeringPolicy;
import org.apache.logging.log4j.core.appender.rolling.DefaultRolloverStrategy;
import org.apache.logging.log4j.core.appender.rolling.SizeBasedTriggeringPolicy;
import org.apache.logging.log4j.core.config.AppenderRef;
import org.apache.logging.log4j.core.config.Configuration;
import org.apache.logging.log4j.core.config.Configurator;
import org.apache.logging.log4j.core.config.LoggerConfig;
import org.apache.logging.log4j.core.config.builder.api.ConfigurationBuilder;
import org.apache.logging.log4j.core.config.builder.api.ConfigurationBuilderFactory;
import org.apache.logging.log4j.core.config.builder.impl.BuiltConfiguration;
import org.apache.logging.log4j.core.layout.PatternLayout;
import org.easitline.common.core.Globals;
import org.easitline.common.core.context.ServerContext;
*/
public class Log4j2Impl {
	/*
	private static Map<String, Logger> logs = new ConcurrentHashMap<String, Logger>();
	   
    private static Configuration sharedConfiguration;

    private Log4j2Impl() {
    	
    }
    
    public static void remove(String logName) {
        logs.remove(logName);
    }
    
    public static void removeAll() {
        for (Map.Entry<String, Logger> entry : logs.entrySet()) {
            String logName = entry.getKey();
            // 从Log4j配置中移除logger
            LoggerContext context = LoggerContext.getContext(false);
            Configuration config = context.getConfiguration();
            config.removeLogger(logName);
            
            // 停止并移除相关的Appender
            LoggerConfig loggerConfig = config.getLoggerConfig(logName);
            for (Appender appender : loggerConfig.getAppenders().values()) {
                loggerConfig.removeAppender(appender.getName());
                appender.stop();
            }
        }
        // 更新Log4j配置
        LoggerContext.getContext(false).updateLoggers();
        // 清空logs映射
        logs.clear();
    }
    
    public static Logger getLogger(String logName) {
        return getLogger("undefine", logName);
    }
    
    public static Logger getLogger(String appName, String logName) {
        if (logs.containsKey(logName)) {
            return logs.get(logName);
        } 
        Logger logger = createLogger(appName, logName);
        logs.put(logName, logger);
        return logger;
    }

    private static synchronized Configuration getSharedConfiguration() {
        if (sharedConfiguration == null) {
            ConfigurationBuilder<BuiltConfiguration> builder = ConfigurationBuilderFactory.newConfigurationBuilder();
            builder.setConfigurationName("SharedLogConfiguration");
            builder.setStatusLevel(getLogLevel());
            builder.add(builder.newRootLogger(getLogLevel()));
            sharedConfiguration = builder.build();
            Configurator.reconfigure(sharedConfiguration);
        }
        return sharedConfiguration;
    }
    
    private static Logger createLogger(String appName, String logName) {
        try {
            if (logName == null || logName.trim().isEmpty()) {
                logName = "easitline-out";  
            }
            
            Configuration config = getSharedConfiguration();
            
            RollingFileAppender fileAppender = createRollingFileAppender(config, logName);
            config.addAppender(fileAppender);

            fileAppender.start();

            LoggerConfig loggerConfig = LoggerConfig.newBuilder()
                .withAdditivity(false)
                .withLevel(getLogLevel())
                .withLoggerName(logName)
                .withIncludeLocation("true")
                .withConfig(config)
                .withRefs(new AppenderRef[]{AppenderRef.createAppenderRef(fileAppender.getName(), null, null)})
                .build();
            loggerConfig.addAppender(fileAppender, null, null);
            config.addLogger(logName, loggerConfig);

            LoggerContext ctx = LoggerContext.getContext(false);
            ctx.updateLoggers(config);

            return LogManager.getLogger(logName);
        } catch (Exception e) {
            e.printStackTrace();
            System.err.println("创建日志记录器时出错: " + e.getMessage());
            return LogManager.getRootLogger();
        }
    }

    private static RollingFileAppender createRollingFileAppender(Configuration config, String logName) {
        PatternLayout layout = PatternLayout.newBuilder()
            .withPattern("%d{yyyy-MM-dd HH:mm:ss} %-5level %logger{1} - %msg%n")
            .withConfiguration(config)
            .build();

        SizeBasedTriggeringPolicy sizePolicy = SizeBasedTriggeringPolicy.createPolicy((ServerContext.getLogBakSize()/1048576)+"MB");

        CompositeTriggeringPolicy policy = CompositeTriggeringPolicy.createPolicy(sizePolicy);

        DefaultRolloverStrategy strategy = DefaultRolloverStrategy.newBuilder()
            .withMax(String.valueOf(ServerContext.getLogBakCount()))
            .withFileIndex("min")
            .withConfig(config)
            .build();

        return RollingFileAppender.newBuilder()
            .setName(logName)
            .setConfiguration(config)
            .setLayout(layout)
            .withFileName(getLoggerPath(logName))
            .withFilePattern(getLoggerPath(logName) + ".%i")
            .withPolicy(policy)
            .withStrategy(strategy)
            .build();
    }

    private static String getLoggerPath(String logName) {
        String path = appLogDir() + File.separator + logName + ".log";
        return path;
    }

    private static String appLogDir() {
        return System.getProperty("appLogDir", Globals.LOG_DIR);
    }
    
    public static Level getLogLevel(){
		String str = ServerContext.getLogLevelStr();
		Level level=Level.ALL;
		switch (str) {
		case "INFO":
			level=Level.INFO;
			break;
		case "ERROR":
			level=Level.ERROR;
			break;
		case "WARN":
			level=Level.WARN;
			break;
		default:
			level=Level.DEBUG;
			break;
		}
		return level;
	}
	*/
}
