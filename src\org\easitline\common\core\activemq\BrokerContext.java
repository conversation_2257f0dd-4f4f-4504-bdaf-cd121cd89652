package org.easitline.common.core.activemq;

import java.io.Serializable;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageConsumer;
import javax.jms.MessageProducer;
import javax.jms.Session;

import org.apache.commons.lang.StringUtils;
import org.easitline.common.core.activemq.log.ActivieMQLogger;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.impl.CoreLogger;



/**
 * 消息队列上下文信息
 * 
 * <AUTHOR>
 * 
 */
public abstract class BrokerContext {
	
	
	private static int INSTANCE_INDEX = 0;
	
	/**
	 * 链接类型
	 */
	private  int connectorType;
	

	/**
	 * 队列名称
	 */
	protected String brokerName;

	/**
	 * 连接URl
	 */
	protected String connector;

	/**
	 * 用户名
	 */
	private String username;

	/**
	 * 密码
	 */
	private String password;
	
	private String clusterName;  //MQ_CLUSTER_NAME 集群名称,需要提供配置信息。
	
	private String tenantId ; //MQ_TENANTID  租户ID,需要提供配置信息。
	
	private String consumerGroup;  //MQ_CONSUMER_GROUP 消费组名称，需要提供配置信息。
	
	
	public String getConectorTypeName(){
		if(this.getConnectorType() == 1) return "1:CONNECTOR_TYPE_PRODUCER";
		if(this.getConnectorType() == 2) return "2:CONNECTOR_TYPE_CONSUMER";
		if(this.getConnectorType() == 3) return "3:CONNECTOR_TYPE_TOPIC_PRODUCER";
		if(this.getConnectorType() == 4) return "4:CONNECTOR_TYPE_TOPIC_CONSUMER";
		return null;
	}

	public int getConnectorType() {
		return connectorType;
	}

	public String getClusterName() {
		return clusterName;
	}

	public String getTenantId() {
		return tenantId;
	}

	public String getUsername() {
		return username;
	}

	public String getPassword() {
		return password;
	}
	

	public String getConsumerGroup() {
		return consumerGroup;
	}

	public void setConsumerGroup(String consumerGroup) {
		this.consumerGroup = consumerGroup;
	}

	/**
	 * 创建MQ上下文
	 * @param connectorType  链接类型，包括：消息队列的类型
	 * @param connector  MQ链接地址
	 * @param brokerName  队列名称
	 * @param username 账号
	 * @param password 密码
	 */
	public BrokerContext(Integer connectorType, String connector,String brokerName, String username, String password){
		
		this.connectorType = connectorType;
		this.brokerName = StringUtils.trimToEmpty(brokerName);
		this.connector = ServerContext.getMQAddr();
		this.username = StringUtils.trimToEmpty(ServerContext.getMQUser());
		this.password = StringUtils.trimToEmpty(ServerContext.getMQPassword());
		this.clusterName = StringUtils.trimToEmpty(ServerContext.getMQClusterName());
		this.tenantId = ServerContext.getMQTenantId();
		this.consumerGroup = ServerContext.getMQConsumerGroup(this.brokerName);
		
		
		ActivieMQLogger.getLogger().info("init BrokerContext("+ServerContext.getMQType()+","+brokerName+")->connectorType:"+this.getConectorTypeName()+","
				+ "brokerName:"+this.brokerName+",connector:"+this.connector+",username:"+this.username+",password:"+this.password
				+",clusterName:"+this.getClusterName()+",tenantId:"+this.getTenantId()+",consumerGroup:"+this.consumerGroup);
	}
	
	protected static synchronized String getConsumerInstanceName(){
		return "comsumer-"+ServerContext.getNodeName()+"-"+(++INSTANCE_INDEX);
	}
	
	protected static synchronized String getProducerInstanceName(){
		return "producer-"+ ServerContext.getNodeName()+"-"+(++INSTANCE_INDEX);
	}
	
	/**
	 * 
	 * @throws JMSException
	 */
	public abstract void init()  ;

	/**
	 * 关闭JMS相关链接
	 * 
	 * @throws JMSException
	 */
	public abstract void close();
	
	
	public abstract void reload();
	
	/**
	 * 获得session对象
	 * 
	 * @return
	 */
	public abstract MessageProducer getProducer() ;

	public abstract MessageConsumer getConsumer() ;

//	public abstract Session getSession() ;

	public abstract String getConnector() ;

	public abstract String getBrokerName() ;
	
	public abstract Message  createObjectMessage(Serializable messageObj)  throws JMSException;
	
}
