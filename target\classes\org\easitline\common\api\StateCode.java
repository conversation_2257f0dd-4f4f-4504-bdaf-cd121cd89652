package org.easitline.common.api;

public enum StateCode {
	API_METHOD_OK(0,"接口调用成功!"), 
	API_METHOD_ERROR(100,"接口处理时发生错误!"), 
	API_METHOD_PARAM_NOT_NULL(101,"接口必填参数为空!"), 
	API_METHOD_PARAM_ERROR(102,"接口参数格式错误!");
	
	
	public int code;
	public String message;

    // 构造方法
    private StateCode(int code) {
       this.code = code;
    }
    private StateCode(int code,String message) {
        this.code = code;
        this.message = message;
     }
    
    public int getCode(){
    	return this.code;
    }
}
