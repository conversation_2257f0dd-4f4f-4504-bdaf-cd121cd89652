package org.easitline.common.core;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.impl.CoreLogger;

/**
 *  1： 加载应用信息 （配置文件）
 *  2：加载数据源
 *  console 更改了相关应用或系统数据源的时候，需要调用core的方法来做动态更新。core提供相关接口给console调用。
 */
public class EasyEngine {
	
	private static  boolean start = false;

	public void start() {
		if(start) return ;
		else start = true;
		try {
			CoreLogger.getPlatform().info("EasyEngine->Start mars server...");
			CoreLogger.getPlatform().info("EasyEngine->mars version："+EasyServer.getVersion()+"  build-date："+EasyServer.getVersionDate());
			CoreLogger.getPlatform().info("EasyEngine->初始化数据源...");
			EasyPool.getInstance();	     //初始化系统数据
			//初始化mars上下文信息
		} catch(Exception e) {
			CoreLogger.getPlatform().error(e);
		}
		
		try {
			CoreLogger.getPlatform().info("EasyEngine->初始化全局配置信息...");
			ServerContext.init();
		} catch (Exception ex) {
			CoreLogger.getPlatform().error(ex);
		}
	}

}
