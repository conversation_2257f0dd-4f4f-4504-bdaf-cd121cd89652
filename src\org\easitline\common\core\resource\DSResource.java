package org.easitline.common.core.resource;

import java.util.HashMap;
import java.util.Map;

import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.druid.pool.DruidDataSourceFactory;


/**
 *    <property name="url" value="${jdbc_url}" />  
3.    <property name="username" value="${jdbc_user}" />  
4.    <property name="password" value="${jdbc_password}" />  
5.       
6.    <property name="filters" value="stat" />  
7.   
8.    <property name="maxActive" value="20" />  
9.    <property name="initialSize" value="1" />  
10.    <property name="maxWait" value="60000" />  
11.    <property name="minIdle" value="1" />  
12.   
13.    <property name="timeBetweenEvictionRunsMillis" value="60000" />  
14.    <property name="minEvictableIdleTimeMillis" value="300000" />  
15.   
16.    <property name="validationQuery" value="SELECT 'x'" />  
17.    <property name="testWhileIdle" value="true" />  
18.    <property name="testOnBorrow" value="false" />  
19.    <property name="testOnReturn" value="false" />  
20.       
21.    <property name="poolPreparedStatements" value="true" />  
22.    <property name="maxPoolPreparedStatementPerConnectionSize" value="50" />  

druid链接参数：
url：数据库的JDBC URL。
username：数据库的用户名。
password：数据库的密码。
driverClassName：数据库驱动的类名。
initialSize：连接池初始化时创建的连接数。
minIdle：连接池中保持的最小空闲连接数。
maxActive：连接池中允许的最大活动连接数。
maxWait：获取连接的最大等待时间（以毫秒为单位）。
validationQuery：用于检测连接是否有效的SQL查询语句。
testWhileIdle：是否在连接空闲时进行连接有效性检测。
testOnBorrow：是否在从连接池获取连接时进行连接有效性检测。
testOnReturn：是否在归还连接到连接池时进行连接有效性检测。
timeBetweenEvictionRunsMillis：两次空闲连接回收操作之间的时间间隔（以毫秒为单位）。
minEvictableIdleTimeMillis连接空闲时间大于该值并且池中空闲连接大于minIdle则关闭该连接
maxEvictableIdleTimeMillis连接空闲时间大于该值，不管minIdle都关闭该连接
removeAbandoned：是否自动回收废弃的连接。
removeAbandonedTimeout：废弃连接的超时时间（以秒为单位）。当连接从连接池中被标记为废弃连接时，如果在指定的超时时间内该连接没有被归还到连接池或重新激活，连接池会自动回收该连接。
filters：连接过滤器的配置，用于设置Druid的扩展功能和性能监控等。
这些参数可以通过在Properties对象中设置相应的属性来进行配置，然后传递给DruidDataSourceFactory.createDataSource(properties)方法来创建Druid连接池。




请注意，以上列出的参数仅是一部分常用的配置参数，还有其他更多的配置选项和扩展功能可以使用。你可以参考Druid官方文档或源代码中的注释来了解更详细的配置说明和参数列表。

oracle JDBC链接参数：
user：连接 Oracle 数据库的用户名。
password：连接 Oracle 数据库的密码。
url：连接 Oracle 数据库的 URL，格式为 ************************************。
driverType：指定使用的 JDBC 驱动类型，取值为 thin 或 oci。
driverClass：指定使用的 JDBC 驱动程序类名，一般不需要设置。
serverName：Oracle 数据库的主机名或 IP 地址。
portNumber：Oracle 数据库的端口号，默认为 1521。
databaseName：Oracle 数据库的名称。
serviceName：Oracle 数据库的服务名称。
SID：Oracle 数据库的 SID。
oracle.net.CONNECT_TIMEOUT：连接超时时间，单位为秒。
oracle.jdbc.ReadTimeout：读取超时时间，单位为秒。
oracle.jdbc.RetainV9LongBindBehavior：是否保留 V9 版本的长绑定行为。
oracle.jdbc.RetainV9LongBindBehavior：是否保留 V9 版本的长绑定行为。
oracle.jdbc.TcpNoDelay：是否启用 TCP_NODELAY 选项。
oracle.jdbc.defaultNChar：是否使用默认的 NCHAR 字符集。
oracle.jdbc.defaultLobPrefetchSize：LOB 类型数据的预取大小。
oracle.jdbc.maxCachedBufferSize：缓存的最大字节数。
oracle.jdbc.maxCachedBuffersize：缓存的最大字节数。
oracle.jdbc.defaultRowPrefetch：默认的行预取数。
oracle.jdbc.disableDefineColumnType：是否禁用定义列类型。
oracle.jdbc.defaultExecuteBatch：默认的批量执行数。
oracle.jdbc.j2ee.ddlBehavior：DDL 操作的行为。
oracle.jdbc.defaultLobPrefetchSize：LOB 类型数据的预取大小。
oracle.jdbc.defaultRowPrefetch：默认的行预取数。
oracle.jdbc.useFetchSizeWithLongColumn：是否启用使用 FetchSize 来处理长列。
oracle.jdbc.defaultNChar：是否使用默认的 NCHAR 字符集。






 *
 */

public class DSResource {
	
	private String dbType;
	
	private String datasourceName;

	private String url ;
	
	private String driverName;

	private String username;
	
	private String password;
	
	private String dbMaster;
	
	private String filters = "stat";
	
	private int maxActive = 30;

	private int initialSize = 1;
	
	//获取连接时最大等待时间，单位毫秒。配置了maxWait之后，缺省启用公平锁，并发效率会有所下降，如果需要可以通过配置useUnfairLock属性为true使用非公平锁。
	private int maxWait = 5*1000;
	
	private int minIdle = 5;
	
//	//超过时间限制是否回收
//	private boolean removeAbandoned = true;
//	
//	//关闭abanded连接时输出错误日志
//	private boolean logAbandoned= true;
//	
//	//conn关闭超时时间；单位为秒。180秒=3分钟
//	private int removeAbandonedTimeout = 180; //60 * 60 * 2 * 1000;
	
	private Map<String,String> properties = new HashMap<String,String>();
	
//	//连接空闲时间大于该值并且池中空闲连接大于minIdle则关闭该连接 ,单位：毫秒  ,这个参数是在连接池空闲数少于配置的数才会出现。
//	private int minEvictableIdleTimeMillis = 300*1000;
//	
//	//连接空闲时间大于该值，不管minIdle都关闭该连接,单位：毫秒
//	private int maxEvictableIdleTimeMillis = 300*1000;
	
	//两次空闲检查时间，60秒检查一次空闲连接的情况。
//	private int timeBetweenEvictionRunsMillis = 60*1000;
	
	
	private String validationQuery = "SELECT 1";

	public String getDatasourceName() {
		return datasourceName;
	}

	public void setDatasourceName(String datasourceName) {
		this.datasourceName = datasourceName;
	}

	
	public Map<String,String> toMap(){
		try
		{
			HashMap<String,String> map = new HashMap<String,String>();
			map.put(DruidDataSourceFactory.PROP_USERNAME, username);
			map.put(DruidDataSourceFactory.PROP_URL, url);
			map.put(DruidDataSourceFactory.PROP_PASSWORD, password);
			map.put(DruidDataSourceFactory.PROP_MAXACTIVE , String.valueOf(maxActive));
			map.put(DruidDataSourceFactory.PROP_INITIALSIZE, String.valueOf(initialSize));
			if(!isMaster()){
				maxWait = 1000;
			}
			map.put(DruidDataSourceFactory.PROP_MAXWAIT, String.valueOf(maxWait));
			map.put(DruidDataSourceFactory.PROP_MINIDLE, String.valueOf(minIdle));
			
//			map.put(DruidDataSourceFactory.PROP_REMOVEABANDONED, String.valueOf(removeAbandoned));
//			map.put(DruidDataSourceFactory.PROP_REMOVEABANDONEDTIMEOUT, String.valueOf(removeAbandonedTimeout));
//			map.put(DruidDataSourceFactory.PROP_LOGABANDONED, String.valueOf(logAbandoned));
			
//			map.put(DruidDataSourceFactory.PROP_TESTONBORROW, "true");
//			map.put(DruidDataSourceFactory.PROP_CONNECTIONPROPERTIES, "true");
			
			
//			////fix by tzc 20220114，需要在run.sh中增加才能生效。
//			String testWhileIdle  = System.getProperty("PROP_TESTWHILEIDLE");
//			if("true".equalsIgnoreCase(testWhileIdle)){
//		
//			}
			
			//fix by tzc ,增加了下面的三个配置，防止空闲链接没有使用情况下，超过一定的时间后，防火墙会把空连接给关闭导致系统异常。
			//表示在连接处于空闲状态时进行连接的有效性验证。
			//map.put(DruidDataSourceFactory.PROP_MINEVICTABLEIDLETIMEMILLIS, String.valueOf(minEvictableIdleTimeMillis));
			//map.put(DruidDataSourceFactory.prop_, String.valueOf(minEvictableIdleTimeMillis));
			
			
			
			//将其设置为true，表示在连接处于空闲状态时进行连接的有效性验证。
//			map.put(DruidDataSourceFactory.PROP_TESTWHILEIDLE, "true");
			//表示在连接处于空闲状态时进行连接的有效性验证，单位毫秒。
//			map.put(DruidDataSourceFactory.PROP_TIMEBETWEENEVICTIONRUNSMILLIS, String.valueOf(timeBetweenEvictionRunsMillis));
			
			map.put("connectTimeout", String.valueOf(1000*60));
			map.put("socketTimeout", String.valueOf(1000*60));
			map.put("killWhenSocketReadTimeout",String.valueOf(true));
			
			if("Oracle".equals(dbType)){
				map.put(DruidDataSourceFactory.PROP_VALIDATIONQUERY, "SELECT 1 FROM DUAL");
			}else{
				map.put(DruidDataSourceFactory.PROP_VALIDATIONQUERY, validationQuery);
			}
			if(isSuppportWall(dbType)) {
				filters = "wall,stat";
			}
			map.put(DruidDataSourceFactory.PROP_FILTERS, filters);
			map.putAll(getProperties());
			/*
			map.put(DruidDataSourceFactory.PROP_MINEVICTABLEIDLETIMEMILLIS, String.valueOf(minEvictableIdleTimeMillis));
			map.put(DruidDataSourceFactory.PROP_VALIDATIONQUERY, validationQuery);
			map.put(DruidDataSourceFactory.PROP_TIMEBETWEENEVICTIONRUNSMILLIS, String.valueOf(idleConnectionTestPeroid));
			map.put(DruidDataSourceFactory.PROP_MINEVICTABLEIDLETIMEMILLIS, String.valueOf(minEvictableIdleTimeMillis));
			
			map.put(DruidDataSourceFactory.PROP_TESTONBORROW, "false");
			map.put(DruidDataSourceFactory.PROP_TESTONRETURN, "false");
			
			map.put(DruidDataSourceFactory.PROP_TESTONBORROW, "false");
			map.put(DruidDataSourceFactory.PROP_POOLPREPAREDSTATEMENTS, "true");
			map.put(DruidDataSourceFactory.PROP_MAXOPENPREPAREDSTATEMENTS, "50");*/
			   
			return map;
		}catch(Exception ex){
			CoreLogger.getLogger().error(ex,ex);
		}
		return null;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getFilters() {
		return filters;
	}

	public void setFilters(String filters) {
		this.filters = filters;
	}

	public int getMaxActive() {
		return maxActive;
	}

	public void setMaxActive(int maxActive) {
		this.maxActive = maxActive;
	}

	public int getInitialSize() {
		return initialSize;
	}

	public void setInitialSize(int initialSize) {
		this.initialSize = initialSize;
	}

	public int getMaxWait() {
		return maxWait;
	}

	public void setMaxWait(int maxWait) {
		this.maxWait = maxWait;
	}

	public int getMinIdle() {
		return minIdle;
	}

	public void setMinIdle(int minIdle) {
		this.minIdle = minIdle;
	}
	
	public String getDriverName() {
		return driverName;
	}

	public void setDriverName(String driverName) {
		this.driverName = driverName;
	}

	public Map<String, String> getProperties() {
		return properties;
	}

	public void setProperties(Map<String, String> properties) {
		this.properties = properties;
	}

	public String getDbType() {
		return dbType;
	}

	public void setDbType(String dbType) {
		this.dbType = dbType;
	}

	public boolean isMaster() {
		if(StringUtils.isBlank(dbMaster)) {
			return true;
		}
		return Boolean.parseBoolean(dbMaster);
	}

	public void setDbMaster(String dbMaster) {
		this.dbMaster = dbMaster;
	}
	
	public static boolean isSuppportWall(String dbType) {
		if(StringUtils.notBlank(dbType)) {
			dbType = dbType.toLowerCase();
			String[] databaseTypes = {"clickhouse", "mysql", "oracle", "postgres", "sqlserver"};
			for (String type : databaseTypes) {
				if (dbType.contains(type)) {
					return true;
				}
			}
		}
        return false;
	}
	
}
