<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <parent>
        <groupId>com.yunqu.mars</groupId>
        <artifactId>mars-all</artifactId>
        <version>3.5</version>
    </parent>
    
    <artifactId>easitline-core</artifactId>
    <packaging>jar</packaging>
    
    <name>easitline-core</name>
    <url>https://www.yunqu-info.com</url>
    <description>Mars开发Core包,提供Web,数据源,日志,MQ,Redis等能力</description>
  
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.source.plugin.version>3.3.0</maven.source.plugin.version>
        <java.version>1.8</java.version>
    </properties>
    
    <dependencies>
       <dependency>
            <groupId>com.yunqu.mars</groupId>
            <artifactId>easitline-core</artifactId>
            <version>3.4</version>
            <scope>provided</scope>
        </dependency>
   	   <dependency>
		    <groupId>net.sf.ehcache</groupId>
		    <artifactId>ehcache</artifactId>
		    <version>********</version>
		    <scope>provided</scope>
		</dependency>
		<dependency>
	        <groupId>org.apache.rocketmq</groupId>
	        <artifactId>rocketmq-client</artifactId>
	        <version>4.8.0</version>
	        <scope>provided</scope>
    	</dependency>
		<dependency>
		    <groupId>org.apache.rocketmq</groupId>
		    <artifactId>rocketmq-acl</artifactId>
		    <version>4.8.0</version>
		    <scope>provided</scope>
		</dependency>
		<dependency>
		    <groupId>org.apache.rocketmq</groupId>
		    <artifactId>rocketmq-common</artifactId>
		    <version>4.8.0</version>
		    <scope>provided</scope>
		</dependency>
		
    	<dependency>
		    <groupId>org.apache.kafka</groupId>
		    <artifactId>kafka_2.13</artifactId>
		    <version>3.9.0</version>
		     <scope>provided</scope>
		</dependency>
		<dependency>
		    <groupId>org.apache.kafka</groupId>
		    <artifactId>kafka-clients</artifactId>
		    <version>3.9.0</version>
		    <scope>provided</scope>
		</dependency>
		<dependency>
		   <groupId>com.ctg.mq</groupId>
		   <artifactId>ctg-mq-api</artifactId>
		   <version>2.7.7</version>
		   <scope>provided</scope>
		</dependency>
		<dependency>
		    <groupId>spy</groupId>
		    <artifactId>memcached</artifactId>
		    <version>2.4rc1</version>
		    <scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.tongtech</groupId>
			<artifactId>jms</artifactId>
			<version>1.0</version>
			<scope>provided</scope>
		</dependency>
		
		
		<!--log4j2-->
		<dependency>
		    <groupId>org.apache.logging.log4j</groupId>
		    <artifactId>log4j-core</artifactId>
		    <version>2.23.1</version>
		    <scope>provided</scope>
		</dependency>
		<dependency>
		    <groupId>org.apache.logging.log4j</groupId>
		    <artifactId>log4j-api</artifactId>
		    <version>2.23.1</version>
		    <scope>provided</scope>
		</dependency>
		<dependency>
		    <groupId>org.apache.logging.log4j</groupId>
		    <artifactId>log4j-1.2-api</artifactId>
		    <version>2.23.1</version>
		    <scope>provided</scope>
		</dependency>

	<!-- 
		<dependency>
		  <groupId>com.ctg.itrdc.cache</groupId>
		  <artifactId>ctg-cache-nclient</artifactId>
		  <version>2.7.9</version>
		</dependency>
		<dependency>
		    <groupId>org.apache.commons</groupId>
		    <artifactId>commons-pool2</artifactId>
		    <version>2.12.1</version> 
		</dependency>
		<dependency>
		    <groupId>redis.clients</groupId>
		    <artifactId>jedis</artifactId>
		    <version>2.9.0</version>
		</dependency>
		<dependency>
		    <groupId>redis.clients</groupId>
		    <artifactId>jedis</artifactId>
		    <version>4.4.8</version>
		</dependency>
		-->
    </dependencies>
  

    <build>
        <sourceDirectory>src</sourceDirectory>
        <resources>
	        <resource>
	            <directory>src</directory>
	            <includes>
	                <include>**/*</include>
	            </includes>
	        </resource>
	        <resource>
                 <directory>src/resource</directory>
                 <includes>
                     <include>**/*</include>
                 </includes>
             </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.3.0</version>
            </plugin>
            <plugin>
	            <groupId>org.apache.maven.plugins</groupId>
	            <artifactId>maven-resources-plugin</artifactId>
	            <version>3.2.0</version>
	            <executions>
	                <execution>
	                    <phase>process-resources</phase>
	                    <goals>
	                        <goal>copy-resources</goal>
	                    </goals>
	                    <configuration>
	                    	 <outputDirectory>${project.build.outputDirectory}</outputDirectory>
	                        <resources>
	                            <resource>
	                                <directory>src/resource</directory>
	                                <includes>
	                                    <include>**/*</include>
	                                </includes>
	                            </resource>
	                        </resources>
	                    </configuration>
	                </execution>
	            </executions>
	        </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>${maven.source.plugin.version}</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>