package org.easitline.common.core.activemq;

import javax.jms.JMSException;

/**
 * 异常类
 * 
 * <AUTHOR>
 * @date 2011-9-4 下午04:04:52
 * @version V1.0
 */
public class MessageException extends JMSException {

	/**
	 * 
	 */
	private static final long serialVersionUID = -9177164752953097505L;

	public MessageException(java.lang.String reason, java.lang.String errorCode) {
		super(reason, errorCode);
	}

	public MessageException(java.lang.String reason) {
		super(reason);
	}

}
