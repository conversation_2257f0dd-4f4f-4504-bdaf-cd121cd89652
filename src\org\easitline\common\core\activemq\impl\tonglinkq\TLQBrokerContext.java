package org.easitline.common.core.activemq.impl.tonglinkq;

import java.io.Serializable;


import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageConsumer;
import javax.jms.MessageProducer;
import javax.jms.Connection;
import javax.jms.Session;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.activemq.Broker;
import org.easitline.common.core.activemq.BrokerContext;
import org.easitline.common.core.activemq.impl.proxy.ConsumerProxy;
import org.easitline.common.core.activemq.impl.proxy.ProducerProxy;
import org.easitline.common.core.activemq.log.ActivieMQLogger;

import com.tongtech.tmqi.ConnectionFactory;
import com.tongtech.tmqi.Queue;
import com.tongtech.tmqi.Topic;


/**
 * 消息队列上下文信息
 * 
 * <AUTHOR>
 * 
 */
public class TLQBrokerContext extends BrokerContext{

	
	private MessageConsumer tlqConsumer = null;
	
	private MessageProducer tlqProducer = null;
	
	
	/**
	 * 消息的会话对象
	 */
	private Session session;
	
	
	/**
	 * 消息队列链接
	 */
	private Connection connection;

	
	/**
	 * 消息的生产者
	 */
	private ProducerProxy producer;

	/**
	 * 消息的消费者
	 */
	private ConsumerProxy consumer;
	
	
	private static String CONSUMER_TYPE_TOPIC = "topic";
	
	private static String CONSUMER_TYPE_QUEUE = "queue";
	
	public Session getSession() {
		return session;
	}
	

	public TLQBrokerContext(Integer connectorType, String connector, String brokerName, String username,String password) {
		super(connectorType, connector, brokerName.replace("-", "_"), username, password);
	}

	@Override
	public void init() {
		
		
		
		try{
			ConnectionFactory connectionFactory = new ConnectionFactory();
			connectionFactory.setProperty("tmqiAddressList", this.getConnector());
			
			if(StringUtils.isNotBlank(this.getUsername())){
				connection = connectionFactory.createConnection(this.getUsername(),this.getPassword());
			}else{
				connection = connectionFactory.createConnection();
			}
			
			connection.start();
	        this.session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE);
	        
	        ActivieMQLogger.getLogger().info("TLQBrokerContext.init("+this.getBrokerName()+") connection->"+connection+",session->"+session);
	        
			
		} catch (Exception ex) {
			ActivieMQLogger.getLogger().error(ex,ex);
		}
		
		
		try {
			if(this.getConnectorType() == Broker.CONNECTOR_TYPE_CONSUMER) {
				this.initQueueConsumer(CONSUMER_TYPE_QUEUE);
			}
			
			if(this.getConnectorType() == Broker.CONNECTOR_TYPE_PRODUCER) {
				this.initQueueProducer();
			}
			
			if(this.getConnectorType() == Broker.CONNECTOR_TYPE_TOPIC_CONSUMER) {
				this.initQueueConsumer(CONSUMER_TYPE_TOPIC);
			}
			
			if(this.getConnectorType() == Broker.CONNECTOR_TYPE_TOPIC_PRODUCER) {
				this.initQueueProducer();
			}
		} catch (Exception ex) {
			ActivieMQLogger.getLogger().error("TLQBrokerContext.init("+this.getBrokerName()+") fail,cause:"+ex.getMessage(),ex);
			try {
				Thread.sleep(3000);  //防止业务死循环
			} catch (Exception ex1) {
				ActivieMQLogger.getLogger().error(ex,ex);
			}
		}
	}
	
	/**
	 * 创建消费者
	 * @param typeName 取值 TOPIC 或 QUEUE
	 * @throws Exception
	 */
	private void initQueueConsumer(String typeName) throws Exception{
		try {
			
			if(this.tlqConsumer!=null){
				this.tlqConsumer.close();
			}
		} catch (Exception ex) {
			ActivieMQLogger.getLogger().error(ex,ex);
		}
		
		
        if(CONSUMER_TYPE_TOPIC.equals(typeName)){
        	//创建发版订阅队列
        	Topic topic = new Topic(this.getBrokerName());
      	  	//创建消费者
        	this.tlqConsumer = session.createConsumer(topic);
        }else{
        	//创建点对点消息队列
        	Queue queue = new Queue(this.getBrokerName());
        	  //创建消费者
            this.tlqConsumer = session.createConsumer(queue);
        }
        
       ActivieMQLogger.getLogger().info("TLQBrokerContext.initQueueConsumer("+this.getBrokerName()+") -> "+tlqConsumer);
       ActivieMQLogger.getLogger().info("Consumer["+this.brokerName+"["+this.tlqConsumer+"] Started!");
       if(this.consumer == null)    this.consumer = new ConsumerProxy(this,this.tlqConsumer);
       else{
    	   this.consumer.setConsumer(this.tlqConsumer);
       }
        
	}
	
	private void initQueueProducer()throws Exception{
		
		ActivieMQLogger.getLogger().info("initQueueProducer(broker:"+this.brokerName+") ... ");
		
		try {
			if(this.tlqProducer!=null){
				this.tlqProducer.close();
			}
		} catch (Exception ex) {
			ActivieMQLogger.getLogger().error(ex,ex);
		}

        
        Queue queue = new Queue(this.getBrokerName());
        
        //创建消费者
        this.tlqProducer= session.createProducer(queue);
		
        ActivieMQLogger.getLogger().info("TLQBrokerContext.initQueueProducer("+this.getBrokerName()+") -> "+tlqProducer);
    	ActivieMQLogger.getLogger().info("Producer["+this.brokerName+"] Started!");
    	if(this.producer == null)  	this.producer = new ProducerProxy(this, tlqProducer);
    	this.producer.setProducer(tlqProducer);
	}


	@Override
	public void close() {
	}

	@Override
	public MessageProducer getProducer() {
		return this.producer;
	}

	@Override
	public MessageConsumer getConsumer() {
		return this.consumer;
	}


	@Override
	public String getConnector() {
		return this.connector;
	}

	@Override
	public String getBrokerName() {
		return this.brokerName;
	}

	@Override
	public Message createObjectMessage(Serializable messageObj) throws JMSException {
		return this.getSession().createObjectMessage(messageObj);
	}
	
	
	public  Message receive(long timeout) throws JMSException{
		return this.tlqConsumer.receive(timeout);
	}

	@Override
	public synchronized void reload() {
		
		ActivieMQLogger.getLogger().info("TLQBrokerContext.init("+this.getBrokerName()+") run reload, connectorType -> "+ this.getConectorTypeName());
		try {
			if (this.tlqConsumer != null){
				this.tlqConsumer.close();
			}
		} catch (Exception ex) {
			ActivieMQLogger.getLogger().error(ex, ex);
		}
		try {
			if (this.tlqProducer != null)
				this.tlqProducer.close();
		} catch (Exception ex) {
			ActivieMQLogger.getLogger().error(ex, ex);
		}
		
		
		try {
			if (session != null)
				session.close();
		} catch (Exception ex) {
			ActivieMQLogger.getLogger()
					.error("<" + brokerName + "> BrokerContext.close(session) error , cause:" + ex.getMessage());
		}

		try {
			if (connection != null) {
				connection.close();
			}
		} catch (Exception ex) {
			ActivieMQLogger.getLogger()
					.error("<" + brokerName + "> BrokerContext.close(connection) error , cause:" + ex.getMessage());
		}
		
		this.session = null;
		this.connection = null;
	
		this.init();
	}
	
}
