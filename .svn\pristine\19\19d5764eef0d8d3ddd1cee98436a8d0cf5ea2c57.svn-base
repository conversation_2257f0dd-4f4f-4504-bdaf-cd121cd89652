package org.easitline.common.core.sso.impl;

import java.util.Collection;
import java.util.Deque;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.concurrent.ConcurrentMap;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.LogEngine;

/**
 * 在线用户管理器，负责记录和管理当前在线的用户。
 * 支持用户上线、下线、查询、踢下线等操作。
 * 后续可扩展为分布式实现（如Redis）。
 */
public class DefaultUserManager {
	
    // 以userAcct为key，存储在线用户信息
    private static final ConcurrentMap<String, String> onlineUsers = new ConcurrentHashMap<>();
    
    // 记录每个账号上次登录时间，防止频繁登录
    private static final ConcurrentMap<String, Long> lastLoginTimeMap = new ConcurrentHashMap<>();
    private static final long LOGIN_INTERVAL_MILLIS = 2000; // 2秒
    
    // 记录每个账号最近一分钟内的登录时间戳
    private static final ConcurrentMap<String, Deque<Long>> loginHistoryMap = new ConcurrentHashMap<>();
    private static final long LOGIN_WINDOW_MILLIS = 60_000; // 1分钟
    private static final int LOGIN_WINDOW_LIMIT = 10;

    /**
     * 用户上线（登录成功后调用）
     */
    public static void userOnline(String userAcct,String userId) {
        if (userId != null && userAcct != null) {
            onlineUsers.put(userAcct, userId);
            LogEngine.getLogger("mars-sso").info("登录成功："+userAcct);
            LogEngine.getLogger("mars-sso").info(ServerContext.getNodeName()+"-系统登录用户数："+onlineUsers.size());
        }
    }

    /**
     * 用户下线（退出或被踢下线时调用）
     */
    public static void userOffline(String userAcct) {
        if (userAcct != null) {
            onlineUsers.remove(userAcct);
            LogEngine.getLogger("mars-sso").info("userOffline下线成功："+userAcct);
            LogEngine.getLogger("mars-sso").info(ServerContext.getNodeName()+"-系统在线用户数："+onlineUsers.size());
        }
    }

    /**
     * 查询所有在线用户
     */
    public static Collection<String> listOnlineUsers() {
        return onlineUsers.values();
    }

    /**
     * 踢指定用户下线
     */
    public static boolean kickUserOffline(String userAcct) {
        return onlineUsers.remove(userAcct) != null;
    }

    /**
     * 判断用户是否在线
     */
    public static boolean isUserOnline(String userAcct) {
        return onlineUsers.containsKey(userAcct);
    }

    /**
     * 用户上线（登录成功前调用，返回是否允许登录）
     * @return true=允许，false=频繁
     */
    public static boolean checkAndRecordLogin(String userAcct) {
        if (userAcct == null) return true;
        long now = System.currentTimeMillis();
        Long last = lastLoginTimeMap.get(userAcct);
        if (last != null && now - last < LOGIN_INTERVAL_MILLIS) {
            // 2秒内重复登录
            return false;
        }
        lastLoginTimeMap.put(userAcct, now);

        // 统计1分钟内登录次数
        Deque<Long> deque = loginHistoryMap.computeIfAbsent(userAcct, k -> new ConcurrentLinkedDeque<>());
        deque.addLast(now);
        // 清理1分钟前的记录
        while (!deque.isEmpty() && now - deque.peekFirst() > LOGIN_WINDOW_MILLIS) {
            deque.pollFirst();
        }
        if (deque.size() > LOGIN_WINDOW_LIMIT) {
            // 超过10次，输出warn日志
        	LogEngine.getLogger("mars-sso").warn("账号[" + userAcct + "] 1分钟内登录次数超过10次，当前次数：" + deque.size());
        }
        return true;
    }
} 