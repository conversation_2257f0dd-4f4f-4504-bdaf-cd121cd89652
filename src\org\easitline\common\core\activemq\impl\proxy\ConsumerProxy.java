package org.easitline.common.core.activemq.impl.proxy;

import javax.jms.JMSException;




import javax.jms.Message;
import javax.jms.MessageConsumer;
import javax.jms.MessageListener;
import org.easitline.common.core.activemq.BrokerContext;
import org.easitline.common.core.activemq.log.ActivieMQLogger;

public class ConsumerProxy implements MessageConsumer{
	
	long  msgCount = 0l;
	
	private BrokerContext  context;
	
	private int  exceptionTimes  = 0;
	
	private MessageConsumer consumer;
	
	public ConsumerProxy(BrokerContext  context,MessageConsumer consumer){
		this.consumer = consumer;
		this.context = context;
	}

	@Override
	public void close() throws JMSException {
	}
	
	public void setConsumer(MessageConsumer consumer){
		
		try {
			this.context.getConsumer().close();
		} catch (JMSException ex) {
			ActivieMQLogger.getLogger().error(ex,ex);
		}
		this.consumer = consumer;
	}

	@Override
	public MessageListener getMessageListener() throws JMSException {
		return this.context.getConsumer().getMessageListener();
	}

	@Override
	public String getMessageSelector() throws JMSException {
		return this.context.getConsumer().getMessageSelector();
	}

	@Override
	public synchronized Message receive() throws JMSException {
		return this.consumer.receive(5000);
	}
	
	@Override
	public synchronized Message receive(long timeout) throws JMSException {
		try {
			Message message =  this.consumer.receive(timeout);
			if(message == null) return null;
			this.msgCount++;
			if(this.msgCount%200==0){
				ActivieMQLogger.getLogger().info("当前队列["+context.getBrokerName()+"]已处理消息数："+msgCount);
			}
			exceptionTimes = 0 ;
			return message;
		} catch (JMSException ex) {
			ActivieMQLogger.getLogger().error("Consumer["+context.getBrokerName()+"] 接收消息失败，原因："+ex.getMessage());
			ActivieMQLogger.getLogger().error(ex,ex);
			try {
				Thread.sleep(3000);
			} catch (Exception e) {
				// TODO: handle exception
			}
			exceptionTimes++;
		}
		if(exceptionTimes > 2){
			exceptionTimes = 0;
			context.reload();
		}
		return  null;
	}

	@Override
	public Message receiveNoWait() throws JMSException {
		return this.receive(5000);
	}

	@Override
	public void setMessageListener(MessageListener arg0) throws JMSException {
		this.context.getConsumer().setMessageListener(arg0);
	}

}
