package org.easitline.common.core.log.impl;

import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;

/**
 * Easitline core 日志类
 *
 */
public class CoreLogger {
	
	public static Logger getLogger(){
		return LogEngine.getLogger("easitline-core");
	}
	
	public static Logger getPlatform(){
		return LogEngine.getLogger("Platform");
	}
	
	public static Logger getRedis(){
		return LogEngine.getLogger("easitline-redis");
	}
	
	public static Logger getMarsReq(){
		return LogEngine.getLogger("MarsReq");
	}
	
	public static Logger getCryptor(){
		return LogEngine.getLogger("easitline-cryptor");
	}
	
}
