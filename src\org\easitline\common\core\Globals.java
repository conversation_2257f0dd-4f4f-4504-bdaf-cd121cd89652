package org.easitline.common.core;

import java.io.File;

/**
 * Easitline  Server 全局变量
 */
public final class Globals {
	
    
	public static final String EASITLLINE_CORE = "easitline_core";
	
	/**
	 * MARS平台默认数据源
	 */
	public static final String DEFAULT_DS_NAME = "default-ds";
	
	/**
	 * 安装目录
	 */
	public static final String BASE_DIR   = System.getProperty("catalina.base",".");
	
	/**
	 *easyserver 目录 
	 */
	public static final String SERVER_DIR = System.getProperty("catalina.base",".")+File.separator+"easyserver";
	
	/**
	 * 临时目录
	 */
	public static final String TMP_DIR    = System.getProperty("catalina.base",".")+File.separator+"easyserver"+File.separator+"temp";
	
	/**
	 * 日志目录
	 */
	public static final String LOG_DIR    = System.getProperty("catalina.base",".")+File.separator+"easyserver"+File.separator+"logs";
	
	/**
	 * 内置Db数据目录
	 */
	public static final String DB_DIR = System.getProperty("catalina.base",".")+File.separator+"easyserver"+File.separator+"db";
	
	/**
	 * 配置文件目录
	 */
	public static final String CONF_DIR = System.getProperty("catalina.base",".")+File.separator+"easyserver"+File.separator+"conf";
	
	/**
	 * 应用部署目录
	 */
	public static final String WEBAPPS_DIR = System.getProperty("catalina.base",".")+File.separator+"webapps";
	
	/**
	 * 文件服务存储目录
	 */
	public static final String FILE_SERVER_DIR = System.getProperty("catalina.base",".")+File.separator+"easyserver"+File.separator+"efs";
	
	
}
