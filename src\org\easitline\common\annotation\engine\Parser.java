package org.easitline.common.annotation.engine;

import java.lang.annotation.Annotation;
import java.util.Iterator;
import java.util.Map;

import org.easitline.common.annotation.AnnotationUtil;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;


/**
 * 数据模型解析器
 * 
 * <AUTHOR>
 *
 */
public class Parser {
	


	
	/**
	 * 解析表
	 */
	public static TypeEntity  parse(Class<?> clazz){
		
		try {
			Annotation[] annotations =  clazz.getAnnotations();
			if(annotations == null){
				return null;
			}
			for(Annotation anno:annotations){
				//遍历，如果是Table注释的，才进行处理
				if(anno instanceof WebObject){
					WebObject  webObject = (WebObject)anno;	
					TypeEntity typeEntry = new TypeEntity();
					typeEntry.setClazz(clazz);
					typeEntry.setName(webObject.name());
					parseMethod(clazz,typeEntry);
					return typeEntry;
				}
			}
		} catch (Exception ex) {
			// TODO Auto-generated catch block
			ex.printStackTrace();
		}
		return null;
	}
	

	/**
	 * 解析字段
	 */
	private static  void  parseMethod(Class<?> clazz,TypeEntity typeEntry){
		
		Map<String,Annotation[]>  annons  = AnnotationUtil.parseMethod(clazz,WebControl.class);
		if(annons == null) return ;
		try {
			Iterator<String> iters = annons.keySet().iterator();
			while(iters.hasNext()){
				String methodName = iters.next();
				Annotation[] fieldAnnos = annons.get(methodName);
				for(Annotation anno:fieldAnnos){
					//遍历，如果是Table注释的，才进行处理
					if(anno instanceof WebControl){
						WebControl  webControl = (WebControl)anno;	
						MethodEntity methodEntry = new MethodEntity();
						methodEntry.setMothedName(methodName);
						methodEntry.setName(webControl.name());
						methodEntry.setType(webControl.type());
						typeEntry.putMethodEntity(methodEntry);
					}
				}
				
			}
			
		
		} catch (Exception ex) {
			// TODO Auto-generated catch block
			ex.printStackTrace();
		}
	}



}
