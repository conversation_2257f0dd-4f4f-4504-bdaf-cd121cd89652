package org.easitline.common.annotation;

import java.lang.annotation.Target;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * 元数据标签，用于描述数据库表
 * <AUTHOR>
 *
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Documented
public @interface WebObject {
	 /**
     * @return name of the DataObject
     */
    String name() default "";
}
