package org.easitline.common.core.activemq.impl.proxy;

import java.util.concurrent.BlockingQueue;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

import javax.jms.Destination;
import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageProducer;
import org.easitline.common.core.activemq.log.ActivieMQLogger;
import org.easitline.common.core.activemq.BrokerContext;


public class ProducerProxy implements MessageProducer{
	
	private  BlockingQueue<Message> messageQueue = new LinkedBlockingQueue<Message>();
	
	private MessageProducer producer;
	private BrokerContext  context;
	
	public ProducerProxy(BrokerContext  context,MessageProducer producer){
		this.producer = producer;
		this.context = context;
		Thread  t  = new Thread(new SendThread());
		t.start();
	}

	@Override
	public void close() throws JMSException {
	}
	
	public void setProducer(MessageProducer producer){
		try {
			this.context.getProducer().close();
		} catch (JMSException ex) {
			ActivieMQLogger.getLogger().error(ex,ex);
		}
		this.producer = producer;
	}

	@Override
	public int getDeliveryMode() throws JMSException {
		return this.context.getProducer().getDeliveryMode();
	}

	@Override
	public Destination getDestination() throws JMSException {
		return this.context.getProducer().getDestination();
	}

	@Override
	public boolean getDisableMessageID() throws JMSException {
		return this.context.getProducer().getDisableMessageID();
	}

	@Override
	public boolean getDisableMessageTimestamp() throws JMSException {
		return this.context.getProducer().getDisableMessageTimestamp();
	}

	@Override
	public int getPriority() throws JMSException {
		return this.context.getProducer().getPriority();
	}

	@Override
	public long getTimeToLive() throws JMSException {
		 return this.context.getProducer().getTimeToLive();
	}

	@Override
	public void send(Message message) throws JMSException {
		if(this.messageQueue.size()>5000){
			ActivieMQLogger.getLogger().warn("当前队列["+context.getBrokerName()+"]待发送消息数超过5000，当前发送信息丢弃...");
			return ;
		}
		messageQueue.add(message);
	}

	@Override
	public void send(Destination arg0, Message message) throws JMSException {
		this.send(message);
		
	}

	@Override
	public void send(Message message, int arg1, int arg2, long arg3) throws JMSException {
		this.send(message);
		
	}

	@Override
	public void send(Destination arg0, Message message, int arg2, int arg3, long arg4) throws JMSException {
		this.send(message);
	}

	@Override
	public void setDeliveryMode(int arg0) throws JMSException {
		
	}

	@Override
	public void setDisableMessageID(boolean arg0) throws JMSException {
		
	}

	@Override
	public void setDisableMessageTimestamp(boolean arg0) throws JMSException {
	}

	@Override
	public void setPriority(int arg0) throws JMSException {
	}

	@Override
	public void setTimeToLive(long arg0) throws JMSException {
	}
	
	private class SendThread implements Runnable{
		
		long msgCount = 0l ;

		@Override
		public void run() {
			while(true){
				try {
					Message message  =  messageQueue.poll(5, TimeUnit.MILLISECONDS);
					if(message == null) continue;
					for(int i = 0 ;i<20;i++){
						boolean bl = this.send(message);
						if(bl) break;
					}
					msgCount++;
					if(msgCount%200==0){
						ActivieMQLogger.getLogger().info("当前队列["+context.getBrokerName()+"]已发送消息数为："+msgCount+",待发送消息数："+messageQueue.size());
					}
				} catch (Exception ex) {
					ActivieMQLogger.getLogger().error(ex,ex);
				}
			}
		}
		
		public boolean send(Message message){
			try {
				MessageProxy proxy = (MessageProxy)message;
				ActivieMQLogger.getLogger().info("Producer["+context.getBrokerName()+","+context.getProducer()+"] >> "+ proxy.getMessage(context));
				producer.send(proxy.getMessage(context));
			} catch (Exception ex) {
				ActivieMQLogger.getLogger().error("Producer["+context.getBrokerName()+"] 发送消息失败，原因："+ex.getMessage(),ex);
				try {
					Thread.sleep(3000);
				} catch (Exception e2) {
					ActivieMQLogger.getLogger().error(ex,ex);
				}
				ActivieMQLogger.getLogger().info("当前队列["+context.getBrokerName()+"]状态： "+producer);
				context.reload();
				return false;
			}
			return true;
			
		}
		
	}

}
