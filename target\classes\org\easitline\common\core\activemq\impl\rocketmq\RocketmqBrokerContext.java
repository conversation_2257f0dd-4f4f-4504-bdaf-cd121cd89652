package org.easitline.common.core.activemq.impl.rocketmq;

import java.io.Serializable;





import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageConsumer;
import javax.jms.MessageProducer;

import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.acl.common.AclClientRPCHook;
import org.apache.rocketmq.acl.common.SessionCredentials;
import org.apache.rocketmq.client.consumer.DefaultLitePullConsumer;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.protocol.heartbeat.MessageModel;
import org.apache.rocketmq.remoting.RPCHook;
import org.easitline.common.core.activemq.Broker;
import org.easitline.common.core.activemq.BrokerContext;
import org.easitline.common.core.activemq.impl.ObjectMessageImpl;
import org.easitline.common.core.activemq.impl.proxy.ConsumerProxy;
import org.easitline.common.core.activemq.impl.proxy.ProducerProxy;
import org.easitline.common.core.activemq.log.ActivieMQLogger;


/**
 * 消息队列上下文信息
 * 
 * <AUTHOR>
 * 
 */
public class RocketmqBrokerContext extends BrokerContext{
	
	/**
	 * 消息的生产者
	 */
	private ProducerProxy producer;

	/**
	 * 消息的消费者
	 */
	private ConsumerProxy consumer;
	
	private DefaultLitePullConsumer defaultLitePullConsumer = null;
	
	private DefaultMQProducer defaultMQProducer = null;
	

	public RocketmqBrokerContext(Integer connectorType, String connector, String brokerName, String username,String password) {
		super(connectorType, connector, brokerName, username, password);
	}

	@Override
	public void init() {
		ActivieMQLogger.getLogger().info("RocketmqBrokerContext.init("+this.getBrokerName()+") start, connectorType -> "+ this.getConectorTypeName());
		try {
			if(this.getConnectorType() == Broker.CONNECTOR_TYPE_CONSUMER) {
				if(this.defaultLitePullConsumer == null) 	this.initQueueConsumer(MessageModel.CLUSTERING);
			}
			
			if(this.getConnectorType() == Broker.CONNECTOR_TYPE_PRODUCER) {
				if(this.defaultMQProducer == null) 	this.initQueueProducer();
			}
			
			if(this.getConnectorType() == Broker.CONNECTOR_TYPE_TOPIC_CONSUMER) {
				if(this.defaultLitePullConsumer == null) 	this.initQueueConsumer(MessageModel.BROADCASTING);
			}
			
			if(this.getConnectorType() == Broker.CONNECTOR_TYPE_TOPIC_PRODUCER) {
				if(this.defaultMQProducer == null)   this.initQueueProducer();
			}
			ActivieMQLogger.getLogger().info("RocketmqBrokerContext.init("+this.getBrokerName()+") success, connectorType -> "+ this.getConectorTypeName()+",consumer->"+consumer);
		} catch (Exception ex) {
			ActivieMQLogger.getLogger().error("RocketmqBrokerContext.init("+this.getBrokerName()+") fail,cause:"+ex.getMessage(),ex);
			try {
				Thread.sleep(3000);  //防止业务死循环
			} catch (Exception ex1) {
				ActivieMQLogger.getLogger().error(ex,ex);
			}
		}
	}
	
	private void initQueueConsumer(MessageModel messageModel) throws Exception{
		
		ActivieMQLogger.getLogger().info("initQueueConsumer(broker:"+this.brokerName+") ... ");
		try {
			if(defaultLitePullConsumer!=null){
				defaultLitePullConsumer.shutdown();
			}
		} catch (Exception ex) {
			ActivieMQLogger.getLogger().error(ex,ex);
		}
        
    	//设置账号密码
        if(StringUtils.isBlank(this.getUsername())){
        	defaultLitePullConsumer  = new DefaultLitePullConsumer(this.brokerName);
        }else{
        	defaultLitePullConsumer  = new DefaultLitePullConsumer(this.brokerName,this.getAclRPCHook(this.getUsername(), this.getPassword()));
        }
       
        // 2、设置namesrv地址
        defaultLitePullConsumer.setNamesrvAddr(this.connector);
        //设定订阅模式，设定是发布或订阅。
        defaultLitePullConsumer.setMessageModel(messageModel);
        // 3、订阅消费主题
//        if(StringUtils.isNotBlank(this.getUsername())) litePullConsumer.setAuthID(this.getUsername());
//        if(StringUtils.isNotBlank(this.getPassword())) litePullConsumer.setAuthPWD(this.getPassword());
        
        //订阅主题
        defaultLitePullConsumer.subscribe(this.brokerName, "*");
        defaultLitePullConsumer.setInstanceName(getConsumerInstanceName());
        
        //设置每次获取一条数据
        defaultLitePullConsumer.setPullBatchSize(1);
        
        // 4、启动消费对象
        defaultLitePullConsumer.start();
        
	    
        RocketmqMessageConsumer _consumer =  new RocketmqMessageConsumer(this,defaultLitePullConsumer);
        
        if(this.consumer == null){
        	ActivieMQLogger.getLogger().info("Consumer["+this.brokerName+"["+consumer+"] Started!");
        	this.consumer = new ConsumerProxy(this,_consumer);
        }else{
        	ActivieMQLogger.getLogger().info("Consumer "+this.brokerName+"["+consumer+"] Reconnected!");
        	this.consumer.setConsumer(_consumer);
        }
        
	}
	
    private RPCHook getAclRPCHook(String username,String password) {
        return new AclClientRPCHook(new SessionCredentials(username,password));
    }
	
	private void initQueueProducer()throws Exception{
		
		ActivieMQLogger.getLogger().info("initQueueProducer(broker:"+this.brokerName+") ... ");
		
		try {
			if(this.defaultMQProducer!=null){
				this.defaultMQProducer.shutdown();
			}
		} catch (Exception ex) {
			ActivieMQLogger.getLogger().error(ex,ex);
		}

		
		//设置账号密码
        if(StringUtils.isBlank(this.getUsername())){
        	defaultMQProducer  = new DefaultMQProducer(this.brokerName);
        }else{
        	defaultMQProducer  = new DefaultMQProducer(this.brokerName,this.getAclRPCHook(this.getUsername(), this.getPassword()));
        }
		// 设置NameServer地址
        defaultMQProducer.setNamesrvAddr(this.connector);
        defaultMQProducer.setInstanceName(getProducerInstanceName());
        defaultMQProducer.start();
		
		RocketmqMessageProducer _producer = new RocketmqMessageProducer(this,defaultMQProducer);
		
        if(this.producer == null){
        	ActivieMQLogger.getLogger().info("Producer["+this.brokerName+"] Started!");
        	this.producer = new ProducerProxy(this,_producer);
        }else{
        	ActivieMQLogger.getLogger().info("Producer["+this.brokerName+"]  Reconnected!");
        	this.producer.setProducer(_producer);
        }
	}
	


	@Override
	public void close() {
	}

	@Override
	public MessageProducer getProducer() {
		return this.producer;
	}

	@Override
	public MessageConsumer getConsumer() {
		return this.consumer;
	}


	@Override
	public String getConnector() {
		return this.connector;
	}

	@Override
	public String getBrokerName() {
		return this.brokerName;
	}

	@Override
	public Message createObjectMessage(Serializable messageObj) throws JMSException {
		return new ObjectMessageImpl(messageObj);
	}

	@Override
	public void reload() {
		ActivieMQLogger.getLogger().info("RocketmqBrokerContext.init("+this.getBrokerName()+") run reload, connectorType -> "+ this.getConectorTypeName());
		try {
			if (this.defaultLitePullConsumer != null){
				this.defaultLitePullConsumer.shutdown();
			}
		} catch (Exception ex) {
			ActivieMQLogger.getLogger().error(ex, ex);
		}
		try {
			if (this.defaultMQProducer != null)
				this.defaultMQProducer.shutdown();
		} catch (Exception ex) {
			ActivieMQLogger.getLogger().error(ex, ex);
		}
		this.defaultLitePullConsumer = null;
		this.defaultMQProducer = null;
		this.init();
	}
	
}
