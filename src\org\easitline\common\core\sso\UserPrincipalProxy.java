package org.easitline.common.core.sso;

import java.security.Principal;
import java.sql.SQLException;
import java.util.List;

import javax.security.auth.login.LoginException;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.sso.impl.DefaultLoginFactory;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.utils.calendar.EasyDate;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.sso.impl.YCUserPrincipal;

public class UserPrincipalProxy {
	
	private UserPrincipal userPrincipal ;
	private long timestamp =  System.currentTimeMillis();
	private Principal principal;
	
	public  UserPrincipalProxy(Principal principal){
		this.principal = principal;
		String marsNodeType = System.getProperty("MARS_NODE_TYPE",System.getProperty("mars.type", "mgr"));
		if("mgr".equalsIgnoreCase(marsNodeType)){
			LogEngine.getLogger("mars-sso").error("当前用户["+principal.getName()+"]登录mars节点类型为：mgr");
			DefaultLoginFactory  factory = new DefaultLoginFactory();
			try {
				this.userPrincipal  = factory.createUserPrincipal(principal.getName());
			} catch (LoginException ex) {
				LogEngine.getLogger("mars-sso").error(ex,ex);
			}
		}else{
			LogEngine.getLogger("mars-sso").error("当前用户["+principal.getName()+"]登录mars节点类型为：node");
			YCUserPrincipal _userPrincipal= createYCUserPrincipal(principal.getName());
			com.yunqu.yc.sso.impl.DefaultLoginFactory dlf = new com.yunqu.yc.sso.impl.DefaultLoginFactory();
			try {
				this.userPrincipal =  dlf.createUserPrincipal(_userPrincipal);
			} catch (LoginException ex) {
				LogEngine.getLogger("mars-sso").error(ex,ex);
			}
		}
	}
	
	
	private YCUserPrincipal createYCUserPrincipal(String username){
			YCUserPrincipal userPrincipal=new YCUserPrincipal();
			userPrincipal.setUserName(username);
			userPrincipal.setLoginAcct(username);
			EasyQuery query = ServerContext.getAdminQuery();
			try {
				EasyRow row = null;
				String sql="SELECT t1.USER_ID,t1.USER_PWD,t1.LOCK_STATE,t1.ACCT_STATE USER_STATE,t2.ENT_STATE,t2.ENT_TYPE,t1.AGENT_ID   FROM CC_EC_USER t1 , CC_ENT t2  WHERE  t1.ENT_ID = t2.ENT_ID and t2.ENT_TYPE <>2 and t1.USER_ACCT=? ";
				List<EasyRow> list = query.queryForList(sql, new Object[]{username});
				if(list == null||list.size()==0){
					sql="SELECT t1.USER_ID,t1.USER_PWD,t1.LOCK_STATE,t1.USER_STATE ,t2.ENT_STATE,t2.ENT_TYPE   FROM CC_USER t1 , CC_ENT t2  WHERE  t1.ENT_ID = t2.ENT_ID and t2.ENT_TYPE <>2 and t1.USER_ACCT=? ";
					list = query.queryForList(sql, new Object[]{username});
					if(list == null||list.size()==0){
						return  null;
					}else{
						row = list.get(0);
						userPrincipal.isEportalUser(false);
						userPrincipal.setUserId(row.getColumnValue("USER_ID"));
					}
				}else{
					row = list.get(0);
					userPrincipal.isEportalUser(true);
					userPrincipal.setUserId(row.getColumnValue("AGENT_ID"));
				}
				userPrincipal.setIsAgentUser(!userPrincipal.isEportalUser());
			} catch (SQLException ex) {
				LogEngine.getLogger("mars-sso").error(ex,ex);
			}
			return userPrincipal;
	}
	
	
	
	
	public boolean isTimeout(){
		return (System.currentTimeMillis() - timestamp) > 12*3600*1000;
	}
	
	public Principal  getPrincipal(){
		return principal;
	}
	
	public UserPrincipal getUserPrincipal() {
		this.timestamp = System.currentTimeMillis();
		return userPrincipal;
	}
	public long getTimestamp() {
		return timestamp;
	}
	
}
