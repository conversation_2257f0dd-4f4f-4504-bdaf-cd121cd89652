# EasitLine Core 文档中心

欢迎使用 EasitLine Core 开发框架！本文档中心为开发者提供了完整的使用指南和参考资料。

## 📚 文档目录

### 🚀 [快速入门指南](./快速入门指南.md)
- **适用人群**：新手开发者
- **内容概述**：5分钟快速上手，包含环境准备、基础示例、常见问题解决
- **推荐阅读时间**：15-30分钟

### 📖 [开发者使用手册](./开发者使用手册.md)
- **适用人群**：所有开发者
- **内容概述**：详细的类库介绍、使用示例、最佳实践
- **推荐阅读时间**：1-2小时

### 📋 [API参考文档](./API参考文档.md)
- **适用人群**：需要查阅具体API的开发者
- **内容概述**：完整的API列表、参数说明、返回值说明
- **使用方式**：作为开发时的参考手册

### 💡 [最佳实践指南](./最佳实践指南.md)
- **适用人群**：有一定经验的开发者
- **内容概述**：代码规范、性能优化、安全实践、架构设计
- **推荐阅读时间**：30-60分钟

### 🗃️ [数据库操作详解](./数据库操作详解.md)
- **适用人群**：需要深入了解数据库操作的开发者
- **内容概述**：EasyQuery、EasyRecord详细用法、事务管理、性能优化
- **推荐阅读时间**：45-90分钟

### 🚀 [缓存操作详解](./缓存操作详解.md)
- **适用人群**：需要使用缓存功能的开发者
- **内容概述**：Redis、Memcache使用、缓存策略、性能优化
- **推荐阅读时间**：30-60分钟

### 🌐 [Web开发详解](./Web开发详解.md)
- **适用人群**：Web应用开发者
- **内容概述**：Servlet开发、RESTful API、中间件、异常处理
- **推荐阅读时间**：45-90分钟

## 🎯 学习路径推荐

### 新手开发者
1. 📖 阅读 [快速入门指南](./快速入门指南.md)
2. 🛠️ 跟随示例创建第一个应用
3. 📚 深入学习 [开发者使用手册](./开发者使用手册.md)
4. 📋 收藏 [API参考文档](./API参考文档.md) 作为开发参考

### 有经验的开发者
1. 📖 快速浏览 [快速入门指南](./快速入门指南.md) 了解框架特点
2. 📚 重点阅读 [开发者使用手册](./开发者使用手册.md) 中的高级功能
3. 💡 学习 [最佳实践指南](./最佳实践指南.md) 提升代码质量
4. 📋 使用 [API参考文档](./API参考文档.md) 查阅具体接口

### 数据库开发者
1. 📖 阅读 [快速入门指南](./快速入门指南.md) 中的数据库部分
2. 🗃️ 深入学习 [数据库操作详解](./数据库操作详解.md)
3. 💡 参考 [最佳实践指南](./最佳实践指南.md) 中的数据库优化部分
4. 📋 使用 [API参考文档](./API参考文档.md) 查阅具体接口

### Web开发者
1. 📖 阅读 [快速入门指南](./快速入门指南.md) 中的Web部分
2. 🌐 深入学习 [Web开发详解](./Web开发详解.md)
3. 🚀 了解 [缓存操作详解](./缓存操作详解.md) 提升应用性能
4. 💡 参考 [最佳实践指南](./最佳实践指南.md) 中的Web优化部分

## 🔧 框架核心功能

### 数据库操作
- **EasyQuery**：统一的数据库查询接口
- **EasyRecord**：面向对象的数据记录操作
- **事务管理**：简单易用的事务控制

### 缓存管理
- **CacheManager**：统一的缓存管理器
- **多种实现**：支持Redis、Memcache等
- **自动选择**：根据配置自动选择缓存类型

### Web处理
- **EasyBaseServlet**：Web请求处理基类
- **EasyResult**：统一的响应格式
- **JSON支持**：内置JSON处理功能

### 工具类库
- **JsonKit**：JSON处理工具
- **StringUtils**：字符串处理工具
- **RandomKit**：随机数生成工具
- **日期时间工具**：EasyDate、EasyCalendar

### 安全功能
- **加密解密**：MD5、DES、SM4等加密算法
- **权限控制**：基于注解的权限管理
- **配置加密**：敏感配置自动加解密

## 📝 常用代码示例

### 数据库操作示例
```java
// 获取查询对象
EasyQuery query = getQuery();

// 查询数据
List<EasyRow> users = query.queryForList("SELECT * FROM users WHERE status = ?", 1);

// 保存数据
EasyRecord user = new EasyRecord("users", "id");
user.set("id", RandomKit.uuid());
user.set("name", "张三");
query.save(user);
```

### 缓存操作示例
```java
// 获取缓存
EasyCache cache = CacheManager.getCache();

// 存储数据
cache.put("user:123", userObject, CacheTime.HOUR);

// 获取数据
User user = cache.get("user:123");
```

### Web处理示例
```java
public class UserServlet extends EasyBaseServlet {
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) {
        JSONObject params = getJSONObject();
        // 处理业务逻辑...
        render(EasyResult.ok("操作成功"));
    }
}
```

## 🆘 获取帮助

### 常见问题
1. **数据库连接问题**：检查数据源配置和网络连接
2. **缓存连接问题**：确认Redis/Memcache服务状态
3. **JSON解析错误**：检查请求参数格式
4. **权限控制问题**：确认资源ID配置

### 调试技巧
1. **启用详细日志**：设置log4j日志级别为DEBUG
2. **检查配置文件**：确认所有必要配置项
3. **使用健康检查**：创建健康检查接口监控系统状态

### 技术支持
- 📧 邮件支持：[技术支持邮箱]
- 💬 在线咨询：[在线支持平台]
- 📞 电话支持：[技术支持电话]

## 🔄 版本信息

- **当前版本**：3.4
- **发布日期**：[发布日期]
- **兼容性**：Java 8+
- **依赖要求**：详见项目pom.xml

## 📄 许可证

本项目采用 [许可证类型] 许可证，详情请查看 [LICENSE](../LICENSE) 文件。

## 🤝 贡献指南

欢迎开发者为项目贡献代码和文档：

1. **代码贡献**：遵循项目代码规范，提交Pull Request
2. **文档改进**：发现文档问题或有改进建议，请提交Issue
3. **Bug报告**：发现Bug请详细描述复现步骤
4. **功能建议**：有新功能需求请先讨论可行性

## 📈 更新日志

### v3.4 (当前版本)
- 新增配置加密功能
- 优化缓存管理器
- 改进日志记录机制
- 修复已知问题

### v3.3
- 新增SM4加密支持
- 优化数据库连接池
- 改进异常处理机制

### v3.2
- 新增Redis集群支持
- 优化JSON处理性能
- 新增批量操作功能

---

## 🎉 开始使用

选择适合您的文档开始学习：

### 基础文档
- 🚀 **新手？** 从 [快速入门指南](./快速入门指南.md) 开始
- 📚 **需要详细了解？** 查看 [开发者使用手册](./开发者使用手册.md)
- 📋 **查找API？** 使用 [API参考文档](./API参考文档.md)
- 💡 **提升代码质量？** 学习 [最佳实践指南](./最佳实践指南.md)

### 专题文档
- 🗃️ **数据库开发？** 深入 [数据库操作详解](./数据库操作详解.md)
- 🚀 **缓存应用？** 学习 [缓存操作详解](./缓存操作详解.md)
- 🌐 **Web开发？** 参考 [Web开发详解](./Web开发详解.md)

祝您使用愉快！🎊
