# Web开发详解

## 概述

EasitLine Core 提供了完整的Web开发支持，包括Servlet基类、JSON处理、响应格式化等功能。本文档详细介绍Web相关类的使用方法和最佳实践。

## 核心类介绍

### EasyBaseServlet - Web基础Servlet

EasyBaseServlet 是所有Web控制器的基类，提供了丰富的Web开发功能。

#### 基础结构

```java
public class UserServlet extends EasyBaseServlet {
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) {
        // 处理GET请求
        handleGetRequest();
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) {
        // 处理POST请求
        handlePostRequest();
    }
    
    // 必须实现的抽象方法
    @Override
    protected String getAppName() {
        return "user-app"; // 应用名称
    }
    
    @Override
    protected String getLoggerName() {
        return "userServlet"; // 日志名称
    }
    
    @Override
    protected String getAppDatasourceName() {
        return "user-ds"; // 数据源名称
    }
    
    @Override
    protected String getResId() {
        return "user.manage"; // 资源ID（用于权限控制）
    }
}
```

#### 请求参数处理

```java
public class UserServlet extends EasyBaseServlet {
    
    // 处理JSON请求
    public void createUser() {
        try {
            // 获取JSON参数
            JSONObject params = getJSONObject();
            
            // 验证必要参数
            if (params == null) {
                render(EasyResult.error("请求参数不能为空"));
                return;
            }
            
            String userName = params.getString("userName");
            String email = params.getString("email");
            
            if (StringUtils.isBlank(userName)) {
                render(EasyResult.error("用户名不能为空"));
                return;
            }
            
            if (StringUtils.isBlank(email)) {
                render(EasyResult.error("邮箱不能为空"));
                return;
            }
            
            // 创建用户
            EasyRecord user = new EasyRecord("users", "user_id");
            user.set("user_id", RandomKit.uuid());
            user.set("user_name", userName);
            user.set("email", email);
            user.set("create_time", new Date());
            
            EasyQuery query = getQuery();
            query.save(user);
            
            // 记录操作日志
            OperatorLog log = new OperatorLog(1, "创建用户")
                .setUserId(getCurrentUserId())
                .setParams(user);
            log.save();
            
            render(EasyResult.ok("用户创建成功"));
            
        } catch (Exception e) {
            error("创建用户失败", e);
            render(EasyResult.error("创建用户失败"));
        }
    }
    
    // 处理带模型前缀的参数
    public void updateUser() {
        try {
            // 获取user模型的参数
            JSONObject userParams = getJSONObject("user");
            String userId = userParams.getString("userId");
            
            if (StringUtils.isBlank(userId)) {
                render(EasyResult.error("用户ID不能为空"));
                return;
            }
            
            // 更新用户信息
            EasyRecord user = new EasyRecord("users", "user_id");
            user.set("user_id", userId);
            user.setColumns(userParams);
            user.set("update_time", new Date());
            
            EasyQuery query = getQuery();
            query.update(user);
            
            render(EasyResult.ok("用户更新成功"));
            
        } catch (Exception e) {
            error("更新用户失败", e);
            render(EasyResult.error("更新用户失败"));
        }
    }
    
    // 处理数组参数
    public void batchCreateUsers() {
        try {
            JSONArray userArray = getJSONArray();
            
            if (userArray == null || userArray.isEmpty()) {
                render(EasyResult.error("用户数据不能为空"));
                return;
            }
            
            EasyQuery query = getQuery();
            query.begin();
            
            int successCount = 0;
            for (int i = 0; i < userArray.size(); i++) {
                try {
                    JSONObject userInfo = userArray.getJSONObject(i);
                    
                    EasyRecord user = new EasyRecord("users", "user_id");
                    user.set("user_id", RandomKit.uuid());
                    user.setColumns(userInfo);
                    user.set("create_time", new Date());
                    
                    query.save(user);
                    successCount++;
                    
                } catch (Exception e) {
                    error("创建用户失败: " + userArray.getJSONObject(i), e);
                }
            }
            
            query.commit();
            
            render(EasyResult.ok("批量创建完成，成功: " + successCount + 
                               ", 失败: " + (userArray.size() - successCount)));
            
        } catch (Exception e) {
            error("批量创建用户失败", e);
            render(EasyResult.error("批量创建失败"));
        }
    }
    
    // 处理URL参数
    public void getUserById() {
        try {
            String userId = getRequest().getParameter("userId");
            
            if (StringUtils.isBlank(userId)) {
                render(EasyResult.error("用户ID不能为空"));
                return;
            }
            
            EasyQuery query = getQuery();
            EasyRow user = query.queryForObject(
                "SELECT * FROM users WHERE user_id = ?", userId);
            
            if (user == null) {
                render(EasyResult.error("用户不存在"));
                return;
            }
            
            render(EasyResult.ok(user));
            
        } catch (Exception e) {
            error("查询用户失败", e);
            render(EasyResult.error("查询用户失败"));
        }
    }
}
```

#### 文件上传处理

```java
public class FileUploadServlet extends EasyBaseServlet {
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) {
        handleFileUpload();
    }
    
    public void handleFileUpload() {
        try {
            // 检查是否为multipart请求
            if (!ServletFileUpload.isMultipartContent(request)) {
                render(EasyResult.error("请求格式错误"));
                return;
            }
            
            // 配置文件上传
            DiskFileItemFactory factory = new DiskFileItemFactory();
            factory.setSizeThreshold(1024 * 1024); // 1MB
            factory.setRepository(new File(System.getProperty("java.io.tmpdir")));
            
            ServletFileUpload upload = new ServletFileUpload(factory);
            upload.setFileSizeMax(10 * 1024 * 1024); // 10MB
            upload.setSizeMax(50 * 1024 * 1024); // 50MB
            
            List<FileItem> items = upload.parseRequest(request);
            
            for (FileItem item : items) {
                if (!item.isFormField()) {
                    // 处理文件
                    String fileName = item.getName();
                    String fileExt = fileName.substring(fileName.lastIndexOf("."));
                    String newFileName = RandomKit.uuid() + fileExt;
                    
                    // 保存文件
                    String uploadDir = "/uploads/";
                    File uploadFile = new File(uploadDir + newFileName);
                    item.write(uploadFile);
                    
                    // 保存文件信息到数据库
                    EasyRecord fileRecord = new EasyRecord("files", "file_id");
                    fileRecord.set("file_id", RandomKit.uuid());
                    fileRecord.set("original_name", fileName);
                    fileRecord.set("file_name", newFileName);
                    fileRecord.set("file_path", uploadDir + newFileName);
                    fileRecord.set("file_size", item.getSize());
                    fileRecord.set("upload_time", new Date());
                    
                    EasyQuery query = getQuery();
                    query.save(fileRecord);
                    
                    render(EasyResult.ok(fileRecord, "文件上传成功"));
                    return;
                }
            }
            
            render(EasyResult.error("没有找到上传文件"));
            
        } catch (Exception e) {
            error("文件上传失败", e);
            render(EasyResult.error("文件上传失败"));
        }
    }
}
```

### EasyDaoBaseServlet - DAO基础Servlet

EasyDaoBaseServlet 提供了基于DAO模式的Web请求处理。

```java
public class UserDaoServlet extends EasyDaoBaseServlet {
    
    // 自动调用对应的DAO方法
    // URL: /user/getUserList
    // 会自动调用 UserDao.getUserList() 方法
    
    @Override
    protected String getAppName() {
        return "user-app";
    }
    
    @Override
    protected String getLoggerName() {
        return "userDaoServlet";
    }
    
    @Override
    protected String getAppDatasourceName() {
        return "user-ds";
    }
    
    @Override
    protected String getResId() {
        return "user.dao";
    }
}

// 对应的DAO类
public class UserDao extends DaoContext {
    
    public JSONObject getUserList() {
        JSONObject params = getParam(null);
        int pageNo = params.getIntValue("pageNo");
        int pageSize = params.getIntValue("pageSize");
        
        EasyQuery query = getQuery();
        
        int offset = (pageNo - 1) * pageSize;
        List<EasyRow> users = query.queryForList(
            "SELECT * FROM users ORDER BY create_time DESC LIMIT ?, ?",
            offset, pageSize);
        
        int total = query.queryForInt("SELECT COUNT(*) FROM users");
        
        JSONObject result = new JSONObject();
        result.put("data", users);
        result.put("total", total);
        result.put("pageNo", pageNo);
        result.put("pageSize", pageSize);
        
        return result;
    }
    
    public JSONObject createUser() {
        JSONObject userParams = getParam("user");
        
        EasyRecord user = new EasyRecord("users", "user_id");
        user.set("user_id", RandomKit.uuid());
        user.setColumns(userParams);
        user.set("create_time", new Date());
        
        EasyQuery query = getQuery();
        query.save(user);
        
        JSONObject result = new JSONObject();
        result.put("success", true);
        result.put("message", "用户创建成功");
        result.put("userId", user.get("user_id"));
        
        return result;
    }
    
    @Override
    protected String getAppName() {
        return "user-app";
    }
    
    @Override
    protected String getLoggerName() {
        return "userDao";
    }
    
    @Override
    protected String getAppDatasourceName() {
        return "user-ds";
    }
}
```

### EasyResult - 响应结果类

EasyResult 提供了统一的Web响应格式。

#### 基础使用

```java
// 成功响应
EasyResult success = EasyResult.ok();
EasyResult successWithData = EasyResult.ok(userData);
EasyResult successWithMessage = EasyResult.ok(userData, "操作成功");

// 错误响应
EasyResult error = EasyResult.error();
EasyResult customError = EasyResult.error(400, "参数错误");

// 检查响应状态
if (result.isOk()) {
    // 处理成功逻辑
    Object data = result.get("data");
} else {
    // 处理错误逻辑
    String errorMsg = result.getString("msg");
}
```

#### 高级使用

```java
public class ApiResponse {
    
    // 分页响应
    public static EasyResult page(List<?> data, int total, int pageNo, int pageSize) {
        JSONObject pageInfo = new JSONObject();
        pageInfo.put("data", data);
        pageInfo.put("total", total);
        pageInfo.put("pageNo", pageNo);
        pageInfo.put("pageSize", pageSize);
        pageInfo.put("totalPages", (total + pageSize - 1) / pageSize);
        
        return EasyResult.ok(pageInfo, "查询成功");
    }
    
    // 业务错误响应
    public static EasyResult businessError(String message) {
        return EasyResult.error(1001, message);
    }
    
    // 参数错误响应
    public static EasyResult paramError(String message) {
        return EasyResult.error(1002, message);
    }
    
    // 权限错误响应
    public static EasyResult permissionError() {
        return EasyResult.error(1003, "权限不足");
    }
    
    // 系统错误响应
    public static EasyResult systemError() {
        return EasyResult.error(1004, "系统异常，请联系管理员");
    }
}

// 使用示例
public void getUserList() {
    try {
        JSONObject params = getJSONObject();
        int pageNo = params.getIntValue("pageNo");
        int pageSize = params.getIntValue("pageSize");
        
        if (pageNo <= 0 || pageSize <= 0) {
            render(ApiResponse.paramError("分页参数错误"));
            return;
        }
        
        // 查询数据
        List<EasyRow> users = queryUsers(pageNo, pageSize);
        int total = getTotalUsers();
        
        render(ApiResponse.page(users, total, pageNo, pageSize));
        
    } catch (Exception e) {
        error("查询用户列表失败", e);
        render(ApiResponse.systemError());
    }
}
```

## RESTful API 设计

### 标准RESTful接口

```java
public class UserRestServlet extends EasyBaseServlet {
    
    @Override
    protected void service(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String method = request.getMethod();
        String pathInfo = request.getPathInfo();
        
        try {
            switch (method) {
                case "GET":
                    handleGet(pathInfo);
                    break;
                case "POST":
                    handlePost(pathInfo);
                    break;
                case "PUT":
                    handlePut(pathInfo);
                    break;
                case "DELETE":
                    handleDelete(pathInfo);
                    break;
                default:
                    render(EasyResult.error(405, "方法不支持"));
            }
        } catch (Exception e) {
            error("处理请求失败", e);
            render(EasyResult.error("请求处理失败"));
        }
    }
    
    // GET /users - 获取用户列表
    // GET /users/{id} - 获取单个用户
    private void handleGet(String pathInfo) {
        if (pathInfo == null || "/".equals(pathInfo)) {
            // 获取用户列表
            getUserList();
        } else {
            // 获取单个用户
            String userId = pathInfo.substring(1); // 去掉开头的 /
            getUserById(userId);
        }
    }
    
    // POST /users - 创建用户
    private void handlePost(String pathInfo) {
        if (pathInfo == null || "/".equals(pathInfo)) {
            createUser();
        } else {
            render(EasyResult.error(404, "资源不存在"));
        }
    }
    
    // PUT /users/{id} - 更新用户
    private void handlePut(String pathInfo) {
        if (pathInfo != null && !"/".equals(pathInfo)) {
            String userId = pathInfo.substring(1);
            updateUser(userId);
        } else {
            render(EasyResult.error(400, "缺少用户ID"));
        }
    }
    
    // DELETE /users/{id} - 删除用户
    private void handleDelete(String pathInfo) {
        if (pathInfo != null && !"/".equals(pathInfo)) {
            String userId = pathInfo.substring(1);
            deleteUser(userId);
        } else {
            render(EasyResult.error(400, "缺少用户ID"));
        }
    }
    
    private void getUserList() {
        // 实现获取用户列表逻辑
        JSONObject params = getJSONObject();
        int pageNo = params.getIntValue("pageNo");
        int pageSize = params.getIntValue("pageSize");
        
        // 查询逻辑...
        render(EasyResult.ok(users));
    }
    
    private void getUserById(String userId) {
        // 实现获取单个用户逻辑
        EasyQuery query = getQuery();
        EasyRow user = query.queryForObject(
            "SELECT * FROM users WHERE user_id = ?", userId);
        
        if (user == null) {
            render(EasyResult.error(404, "用户不存在"));
        } else {
            render(EasyResult.ok(user));
        }
    }
    
    private void createUser() {
        // 实现创建用户逻辑
        JSONObject userInfo = getJSONObject();
        
        // 验证和创建逻辑...
        render(EasyResult.ok("用户创建成功"));
    }
    
    private void updateUser(String userId) {
        // 实现更新用户逻辑
        JSONObject userInfo = getJSONObject();
        
        // 验证和更新逻辑...
        render(EasyResult.ok("用户更新成功"));
    }
    
    private void deleteUser(String userId) {
        // 实现删除用户逻辑
        EasyQuery query = getQuery();
        int affected = query.update(
            "DELETE FROM users WHERE user_id = ?", userId);
        
        if (affected > 0) {
            render(EasyResult.ok("用户删除成功"));
        } else {
            render(EasyResult.error("用户不存在或删除失败"));
        }
    }
}
```

### API版本控制

```java
public class ApiVersionServlet extends EasyBaseServlet {
    
    @Override
    protected void service(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String version = getApiVersion(request);
        String action = getAction(request);
        
        switch (version) {
            case "v1":
                handleV1(action);
                break;
            case "v2":
                handleV2(action);
                break;
            default:
                render(EasyResult.error(400, "不支持的API版本"));
        }
    }
    
    private String getApiVersion(HttpServletRequest request) {
        // 从URL路径获取版本号 /api/v1/users
        String pathInfo = request.getPathInfo();
        if (pathInfo != null && pathInfo.startsWith("/v")) {
            return pathInfo.split("/")[1];
        }
        
        // 从Header获取版本号
        String headerVersion = request.getHeader("API-Version");
        if (headerVersion != null) {
            return headerVersion;
        }
        
        // 默认版本
        return "v1";
    }
    
    private void handleV1(String action) {
        switch (action) {
            case "getUserList":
                getUserListV1();
                break;
            default:
                render(EasyResult.error(404, "接口不存在"));
        }
    }
    
    private void handleV2(String action) {
        switch (action) {
            case "getUserList":
                getUserListV2();
                break;
            default:
                render(EasyResult.error(404, "接口不存在"));
        }
    }
    
    private void getUserListV1() {
        // V1版本的用户列表接口
        JSONObject result = new JSONObject();
        result.put("users", getUserData());
        render(EasyResult.ok(result));
    }
    
    private void getUserListV2() {
        // V2版本的用户列表接口（增加了分页和过滤功能）
        JSONObject params = getJSONObject();
        int pageNo = params.getIntValue("pageNo");
        int pageSize = params.getIntValue("pageSize");
        String filter = params.getString("filter");
        
        // 查询逻辑...
        JSONObject result = new JSONObject();
        result.put("data", getUserData());
        result.put("pagination", getPaginationInfo(pageNo, pageSize));
        result.put("filter", filter);
        
        render(EasyResult.ok(result));
    }
}
```

## 中间件和过滤器

### 请求日志中间件

```java
public class RequestLogFilter implements Filter {
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, 
                        FilterChain chain) throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        long startTime = System.currentTimeMillis();
        String requestId = RandomKit.uuid();
        
        // 记录请求开始
        logRequestStart(httpRequest, requestId);
        
        try {
            // 继续处理请求
            chain.doFilter(request, response);
            
        } finally {
            // 记录请求结束
            long endTime = System.currentTimeMillis();
            logRequestEnd(httpRequest, httpResponse, requestId, endTime - startTime);
        }
    }
    
    private void logRequestStart(HttpServletRequest request, String requestId) {
        StringBuilder log = new StringBuilder();
        log.append("请求开始 [").append(requestId).append("] ");
        log.append(request.getMethod()).append(" ");
        log.append(request.getRequestURI());
        
        String queryString = request.getQueryString();
        if (queryString != null) {
            log.append("?").append(queryString);
        }
        
        log.append(" - IP: ").append(getClientIP(request));
        log.append(" - UserAgent: ").append(request.getHeader("User-Agent"));
        
        CoreLogger.getPlatform().info(log.toString());
    }
    
    private void logRequestEnd(HttpServletRequest request, HttpServletResponse response, 
                              String requestId, long duration) {
        StringBuilder log = new StringBuilder();
        log.append("请求结束 [").append(requestId).append("] ");
        log.append("状态: ").append(response.getStatus());
        log.append(" - 耗时: ").append(duration).append("ms");
        
        if (duration > 1000) {
            CoreLogger.getPlatform().warn(log.toString());
        } else {
            CoreLogger.getPlatform().info(log.toString());
        }
    }
    
    private String getClientIP(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
```

### 跨域处理中间件

```java
public class CorsFilter implements Filter {
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, 
                        FilterChain chain) throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        // 设置跨域头
        httpResponse.setHeader("Access-Control-Allow-Origin", "*");
        httpResponse.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        httpResponse.setHeader("Access-Control-Allow-Headers", 
            "Content-Type, Authorization, X-Requested-With");
        httpResponse.setHeader("Access-Control-Max-Age", "3600");
        
        // 处理预检请求
        if ("OPTIONS".equals(httpRequest.getMethod())) {
            httpResponse.setStatus(HttpServletResponse.SC_OK);
            return;
        }
        
        chain.doFilter(request, response);
    }
}
```

### 认证授权中间件

```java
public class AuthFilter implements Filter {
    
    private Set<String> publicPaths = new HashSet<>();
    
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // 配置公开路径（不需要认证）
        publicPaths.add("/api/login");
        publicPaths.add("/api/register");
        publicPaths.add("/api/health");
    }
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, 
                        FilterChain chain) throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        String path = httpRequest.getRequestURI();
        
        // 检查是否为公开路径
        if (isPublicPath(path)) {
            chain.doFilter(request, response);
            return;
        }
        
        // 检查认证信息
        String token = httpRequest.getHeader("Authorization");
        if (token == null || !token.startsWith("Bearer ")) {
            sendUnauthorizedResponse(httpResponse);
            return;
        }
        
        // 验证token
        String actualToken = token.substring(7); // 去掉 "Bearer " 前缀
        UserPrincipal user = validateToken(actualToken);
        
        if (user == null) {
            sendUnauthorizedResponse(httpResponse);
            return;
        }
        
        // 设置用户信息到请求中
        httpRequest.setAttribute("currentUser", user);
        
        chain.doFilter(request, response);
    }
    
    private boolean isPublicPath(String path) {
        return publicPaths.contains(path);
    }
    
    private UserPrincipal validateToken(String token) {
        try {
            // 从缓存或数据库验证token
            EasyCache cache = CacheManager.getCache();
            UserPrincipal user = cache.get("token:" + token);
            
            if (user != null) {
                // 延长token有效期
                cache.put("token:" + token, user, CacheTime.HOUR * 2);
            }
            
            return user;
            
        } catch (Exception e) {
            CoreLogger.getPlatform().error("Token验证失败", e);
            return null;
        }
    }
    
    private void sendUnauthorizedResponse(HttpServletResponse response) 
            throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        
        EasyResult result = EasyResult.error(401, "未授权访问");
        response.getWriter().write(result.toJSONString());
    }
}
```

## 异常处理和错误页面

### 全局异常处理

```java
public class GlobalExceptionHandler {
    
    public static EasyResult handleException(Exception e, String operation) {
        if (e instanceof IllegalArgumentException) {
            return EasyResult.error(400, "参数错误: " + e.getMessage());
        } else if (e instanceof SQLException) {
            CoreLogger.getPlatform().error("数据库操作失败: " + operation, e);
            return EasyResult.error(500, "数据操作失败");
        } else if (e instanceof SecurityException) {
            return EasyResult.error(403, "权限不足");
        } else {
            CoreLogger.getPlatform().error("系统异常: " + operation, e);
            return EasyResult.error(500, "系统异常，请联系管理员");
        }
    }
}

// 在Servlet中使用
public class UserServlet extends EasyBaseServlet {
    
    public void createUser() {
        try {
            // 业务逻辑
            doCreateUser();
            render(EasyResult.ok("用户创建成功"));
            
        } catch (Exception e) {
            render(GlobalExceptionHandler.handleException(e, "创建用户"));
        }
    }
}
```

### 自定义错误页面

```java
public class ErrorPageServlet extends EasyBaseServlet {
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) {
        Integer statusCode = (Integer) request.getAttribute("javax.servlet.error.status_code");
        String errorMessage = (String) request.getAttribute("javax.servlet.error.message");
        
        JSONObject errorInfo = new JSONObject();
        errorInfo.put("status", statusCode);
        errorInfo.put("message", getErrorMessage(statusCode));
        errorInfo.put("timestamp", new Date());
        errorInfo.put("path", request.getRequestURI());
        
        render(EasyResult.error(statusCode, errorInfo.getString("message")));
    }
    
    private String getErrorMessage(Integer statusCode) {
        if (statusCode == null) {
            return "未知错误";
        }
        
        switch (statusCode) {
            case 400:
                return "请求参数错误";
            case 401:
                return "未授权访问";
            case 403:
                return "权限不足";
            case 404:
                return "资源不存在";
            case 405:
                return "方法不支持";
            case 500:
                return "服务器内部错误";
            default:
                return "请求处理失败";
        }
    }
}
```

## 性能优化

### 响应压缩

```java
public class CompressionFilter implements Filter {
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, 
                        FilterChain chain) throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        String acceptEncoding = httpRequest.getHeader("Accept-Encoding");
        
        if (acceptEncoding != null && acceptEncoding.contains("gzip")) {
            // 使用Gzip压缩响应
            GzipResponseWrapper wrapper = new GzipResponseWrapper(httpResponse);
            chain.doFilter(request, wrapper);
            wrapper.finish();
        } else {
            chain.doFilter(request, response);
        }
    }
}
```

### 缓存控制

```java
public class CacheControlServlet extends EasyBaseServlet {
    
    public void getStaticData() {
        // 设置缓存头
        HttpServletResponse response = getResponse();
        
        // 设置缓存1小时
        response.setHeader("Cache-Control", "public, max-age=3600");
        response.setDateHeader("Expires", System.currentTimeMillis() + 3600000);
        
        // 设置ETag
        String etag = generateETag();
        response.setHeader("ETag", etag);
        
        // 检查客户端缓存
        String clientETag = getRequest().getHeader("If-None-Match");
        if (etag.equals(clientETag)) {
            response.setStatus(HttpServletResponse.SC_NOT_MODIFIED);
            return;
        }
        
        // 返回数据
        render(EasyResult.ok(getStaticData()));
    }
    
    private String generateETag() {
        // 根据数据生成ETag
        return "\"" + MD5Util.getHexMD5(getDataVersion()) + "\"";
    }
}
```
