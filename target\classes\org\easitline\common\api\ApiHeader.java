package org.easitline.common.api;

import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSONObject;

public class ApiHeader {
	public String serviceId;
	public String service;
	public String method;
	public String appKey;
	public String osType;
	public String timestamp;
	public String format;
	public String version;
	public String sign;
	public String accessToken;
	public boolean responseSign;
	public String curUserId;
	public String curUserName;
	public String clientId;

	public static ApiHeader prase(JSONObject jsonParam) {
		
		ApiHeader apiHeader = new ApiHeader();
		apiHeader.serviceId = jsonParam.getString("service_id");
		if(StringUtils.isNotEmpty(apiHeader.serviceId)){
			String[] serviceSplit = StringUtils.split(apiHeader.serviceId,".");
			if(serviceSplit.length>1){
				apiHeader.service = serviceSplit[0];
				apiHeader.method = serviceSplit[1];
			}else
				apiHeader.service = serviceSplit[0];
		}

		apiHeader.appKey = jsonParam.getString("app_key");
		apiHeader.osType = jsonParam.getString("os_type");
		apiHeader.timestamp = jsonParam.getString("timestamp");
		apiHeader.format = jsonParam.getString("format")==null?"json":jsonParam.getString("format");		
		apiHeader.version = jsonParam.getString("version");
		apiHeader.clientId = jsonParam.getString("client_id");
		apiHeader.sign = jsonParam.getString("sign");
		apiHeader.accessToken = jsonParam.getString("access_token");
		apiHeader.responseSign = jsonParam.getBooleanValue("response_sign");
		apiHeader.curUserId = jsonParam.getString("cur_user_id");
		apiHeader.curUserName = jsonParam.getString("cur_user_name");
		
		return apiHeader;
	}
}
