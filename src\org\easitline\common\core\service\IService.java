package org.easitline.common.core.service;

import com.alibaba.fastjson.JSONObject;

/**
 * ESB 服务接口定义
 * <AUTHOR>
 *
 */
public abstract class IService implements MarsService{
	
	/**
	 * 服务接口 
	 * @param jsonString  			服务参数：字符串对象为Json
	 * @return  					返回Json对象
	 * @throws ServiceException  	如果服务处理失败，则抛出，系统捕获  ServiceException，则代表本次SOA的服务调用失败，计数器加1
	 */
	public abstract JSONObject invoke(JSONObject jsonObject) throws ServiceException;
	
}
