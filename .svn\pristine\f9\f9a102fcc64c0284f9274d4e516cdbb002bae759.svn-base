package org.easitline.common.core.web.render;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;


public class Render {
	public static void render(HttpServletRequest request,HttpServletResponse response, String contentType,String content) {
		PrintWriter writer = null;
		try {
			response.setHeader("Pragma", "no-cache");	// HTTP/1.0 caches might not implement Cache-Control and might only implement Pragma: no-cache
			response.setHeader("Cache-Control", "no-cache");
			response.setDateHeader("Expires", 0);
			
			response.setContentType(contentType);
			response.setCharacterEncoding("UTF-8");	// 与 contentType 分开设置
			writer = response.getWriter();
			if(ServerContext.isDebug()){
				CoreLogger.getMarsReq().info(request.getRequestURI()+"->result:"+content);
			}
			writer.write(content);
			writer.flush();
		} catch (IOException e) {
			CoreLogger.getMarsReq().error(e);
		}
	}
	public static void renderText(HttpServletRequest request,HttpServletResponse response,String text){
		render(request,response, ContentType.TEXT.value(), text);
	}
	public static void renderHtml(HttpServletRequest request,HttpServletResponse response,String html){
		render(request,response, ContentType.HTML.value(), html);
	}
	public static void renderXml(HttpServletRequest request,HttpServletResponse response,String xml){
		render(request,response, ContentType.XML.value(), xml);
	}
	public static void renderJavascript(HttpServletRequest request,HttpServletResponse response,String js){
		Render.render(request,response, ContentType.JAVASCRIPT.value(), js);
	}
	/**
	 * 
	 * @param request
	 * @param response
	 * @param file 
	 * @param fileName 导出的文件名
	 * @param deleteFile 是否删除源文件
	 */
	public static void renderFile(HttpServletRequest request,HttpServletResponse response,File file,String fileName,boolean deleteFile){
		try {
			String contentType = request.getServletContext().getMimeType(file.getName());
			response.setCharacterEncoding("UTF-8");
			response.setContentType(contentType != null ? contentType : "application/octet-stream");
			String clientFileName = StringUtils.isBlank(fileName)?file.getName():fileName;
			response.setHeader("Content-disposition","attachment;"+encodeFileName(request,clientFileName));
			FileInputStream fis = new FileInputStream(file);
			OutputStream os = response.getOutputStream();
			byte[] b = new byte[1024];
			int i = 0;
			while ((i = fis.read(b)) > 0) {
				os.write(b, 0, i);
			} 
			fis.close();
			os.flush();
			os.close();
		} catch (Exception e) {
			CoreLogger.getPlatform().error(e.getMessage(), e);
		}finally{
			if(deleteFile){
				if(file.exists()){
					file.delete();
				}
			}
		}
	}
	public static void renderFile(HttpServletRequest request,HttpServletResponse response,File file,String fileName){
		renderFile(request,response,file,fileName,false);
	}
	public static void renderImages(HttpServletResponse response,File file){
		String fileName = file.getName();
		if (fileName.endsWith(".gif")) {
			response.setContentType("image/gif;charset=utf8");
		} else {
			response.setContentType("image/jpeg;charset=utf8");
		}
		try {
			OutputStream output = response.getOutputStream();// 得到输出流
			InputStream imageIn = new FileInputStream(file); // 文件流
			BufferedInputStream bis = new BufferedInputStream(imageIn);// 输入缓冲流
			BufferedOutputStream bos = new BufferedOutputStream(output);// 输出缓冲流
			byte data[] = new byte[4096];// 缓冲字节数
			int size = 0;
			size = bis.read(data);
			while (size != -1) {
				bos.write(data, 0, size);
				size = bis.read(data);
			}
			bis.close();
			bos.flush();// 清空输出缓冲流
			bos.close();
			output.close();
		} catch (FileNotFoundException e) {
			CoreLogger.getMarsReq().error(e);
		} catch (IOException e) {
			CoreLogger.getMarsReq().error(e);
		}
	}
	/**
	 * 依据浏览器判断编码规则
	 */
	public static String encodeFileName(HttpServletRequest request, String fileName) {
		String userAgent = request.getHeader("User-Agent");
		try {
			String encodedFileName = URLEncoder.encode(fileName, "UTF8");
			// 如果没有UA，则默认使用IE的方式进行编码
			if (userAgent == null) {
				return "filename=\"" + encodedFileName + "\"";
			}
			
			userAgent = userAgent.toLowerCase();
			// IE浏览器，只能采用URLEncoder编码
			if (userAgent.indexOf("msie") != -1) {
				return "filename=\"" + encodedFileName + "\"";
			}
			
			// Opera浏览器只能采用filename*
			if (userAgent.indexOf("opera") != -1) {
				return "filename*=UTF-8''" + encodedFileName;
			}
			
			// Safari浏览器，只能采用ISO编码的中文输出,Chrome浏览器，只能采用MimeUtility编码或ISO编码的中文输出
			if (userAgent.indexOf("safari") != -1 || userAgent.indexOf("applewebkit") != -1 || userAgent.indexOf("chrome") != -1) {
				return "filename=\"" + new String(fileName.getBytes("UTF-8"), "ISO8859-1") + "\"";
			}
			
			// FireFox浏览器，可以使用MimeUtility或filename*或ISO编码的中文输出
			if (userAgent.indexOf("mozilla") != -1) {
				return "filename*=UTF-8''" + encodedFileName;
			}
			
			return "filename=\"" + encodedFileName + "\"";
		} catch (UnsupportedEncodingException e) {
			CoreLogger.getMarsReq().error(e);
			throw new RuntimeException(e);
		}
	}
	
	public static void renderJsonp(HttpServletRequest request,HttpServletResponse response,Object obj){
		renderJson(request,response,obj);
	}
	public static void renderJson(HttpServletRequest request,HttpServletResponse response,Object obj){
		String callbackFunc = request.getParameter("callbackFunc");
		if(StringUtils.isBlank(callbackFunc)){
			callbackFunc =request.getParameter("busiCallbackFunc");
		}
		String content = null;
		if(obj!=null&&obj instanceof String){
			content=obj.toString();
		}else{
			content=JSON.toJSONString(obj,SerializerFeature.WriteNullListAsEmpty,SerializerFeature.IgnoreErrorGetter,SerializerFeature.IgnoreNonFieldGetter);
		}
		if(StringUtils.notBlank(callbackFunc)){
			content = callbackFunc + "("+content+");";
		}
		render(request,response, ContentType.JSON.value(),content);
	}


}
