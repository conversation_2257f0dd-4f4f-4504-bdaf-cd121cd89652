package org.easitline.common.core.cache.impl;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.net.URL;
import java.util.List;

import org.easitline.common.core.Globals;
import org.easitline.common.core.log.impl.CoreLogger;

import net.sf.ehcache.Cache;
import net.sf.ehcache.CacheManager;
import net.sf.ehcache.Element;
import net.sf.ehcache.Status;
import net.sf.ehcache.config.Configuration;

/**
 * EhcacheKit. Useful tool box for EhCache.
 */
public class EhcacheKit {
	
	private static CacheManager cacheManager;
	
	private String configurationFileName;
	private URL configurationFileURL;
	private InputStream inputStream;
	private Configuration configuration;
	
	private static Object locker = new Object();
	
	public void start() {
		createCacheManager();
		EhcacheKit.init(cacheManager);
	}
	
	static void init(CacheManager cacheManager) {
		EhcacheKit.cacheManager = cacheManager;
	}
	
	private static void defaultInit(){
		String url=Globals.CONF_DIR+File.separator+"ehcache.xml";
		try {
			File file=new File(url);
			if(file.exists()){
				InputStream is = new FileInputStream(url);
				cacheManager= CacheManager.create(is);
				EhcacheKit.init(cacheManager);
			}else{
				CoreLogger.getPlatform().warn(url+"不存在.");
			}
		} catch (FileNotFoundException e) {
			CoreLogger.getPlatform().error(url+"不存在.");
		}
	}
	public static CacheManager getCacheManager() {
		return cacheManager;
	}
	public EhcacheKit(){
		defaultInit();
	}
	public EhcacheKit(CacheManager cacheManager) {
		EhcacheKit.cacheManager = cacheManager;
	}
	
	public EhcacheKit(String configurationFileName) {
		this.configurationFileName = configurationFileName; 
	}
	
	public EhcacheKit(URL configurationFileURL) {
		this.configurationFileURL = configurationFileURL;
	}
	
	public EhcacheKit(InputStream inputStream) {
		this.inputStream = inputStream;
	}
	
	public EhcacheKit(Configuration configuration) {
		this.configuration = configuration;
	}
	
	static Cache getOrAddCache(String cacheName) {
		if(cacheManager==null)defaultInit();
		Cache cache = cacheManager.getCache(cacheName);
		if (cache == null) {
			synchronized(locker) {
				cache = cacheManager.getCache(cacheName);
				if (cache == null) {
					CoreLogger.getPlatform().warn("Could not find cache config [" + cacheName + "], using default.");
					cacheManager.addCacheIfAbsent(cacheName);
					cache = cacheManager.getCache(cacheName);
					CoreLogger.getPlatform().debug("Cache [" + cacheName + "] started.");
				}
			}
		}
		return cache;
	}
	
	public static void put(String cacheName, Object key, Object value) {
		if(cacheManager==null||cacheManager.getStatus()==Status.STATUS_SHUTDOWN)defaultInit();
		if(cacheManager==null){
			CoreLogger.getPlatform().error("cacheManager is not create.");
			return;
		}
		getOrAddCache(cacheName).put(new Element(key, value));
	}
	
	@SuppressWarnings("unchecked")
	public static <T> T get(String cacheName, Object key) {
		if(cacheManager==null||cacheManager.getStatus()==Status.STATUS_SHUTDOWN)defaultInit();
		if(cacheManager==null){
			CoreLogger.getPlatform().error("cacheManager is not create.");
			return null;
		}
		Element element = getOrAddCache(cacheName).get(key);
		return element != null ? (T)element.getObjectValue() : null;
	}

	@SuppressWarnings("rawtypes")
	public static List getKeys(String cacheName) {
		return getOrAddCache(cacheName).getKeys();
	}
	
	public static void remove(String cacheName, Object key) {
		getOrAddCache(cacheName).remove(key);
	}
	
	public static void removeAll(String cacheName) {
		getOrAddCache(cacheName).removeAll();
	}
	
	@SuppressWarnings("unchecked")
	public static <T> T get(String cacheName, Object key, Object data) {
		if(cacheManager==null||cacheManager.getStatus()==Status.STATUS_SHUTDOWN)defaultInit();
		if(cacheManager==null){
			CoreLogger.getPlatform().error("cacheManager is not create.");
			return null;
		}
		Object result = get(cacheName, key);
		if (result == null) {
			result=data;
			put(cacheName, key, data);
		}
		return (T)result;
	}
	
	private void createCacheManager() {
		if (cacheManager != null)
			return ;
		
		if (configurationFileName != null) {
			cacheManager = CacheManager.create(configurationFileName);
			return ;
		}
		
		if (configurationFileURL != null) {
			cacheManager = CacheManager.create(configurationFileURL);
			return ;
		}
		
		if (inputStream != null) {
			cacheManager = CacheManager.create(inputStream);
			return ;
		}
		
		if (configuration != null) {
			cacheManager = CacheManager.create(configuration);
			return ;
		}
		cacheManager = CacheManager.create();
	}
	public void stop() {
		cacheManager.shutdown();
	}
	
}


