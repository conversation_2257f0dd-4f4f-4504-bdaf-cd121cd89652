package org.easitline.common.annotation;

import java.lang.annotation.ElementType;



import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;



/**
 * <AUTHOR>
 * 对数据对象的方式进行 
 */

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME) 
public @interface WebControl {
	
	/**
	 * 控件类型
	 * @return
	 */
	public Types  type() ;
	
	/**
	 * 控件名称
	 * @return
	 */
	public String name();
}
