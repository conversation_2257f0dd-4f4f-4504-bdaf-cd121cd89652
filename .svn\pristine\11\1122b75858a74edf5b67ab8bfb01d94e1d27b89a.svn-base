package org.easitline.common.core.context;

import java.io.File;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.log4j.Level;
import org.easitline.common.core.EasyPool;
import org.easitline.common.core.Globals;
import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;

/**
 * ServerContext
 */
public class ServerContext {
	
	private static boolean init = false;
	
	private static String containerType = null;
	
	private static ConcurrentHashMap<String,String> config = new ConcurrentHashMap<String,String>();
	
	private static String G_DEFAULT_DS = "default-ds";
	
	static{
		init();
	}
	
	
	/**
	 * 获得系统数据连接
	 * @param sysDatasourceName
	 * @return
	 */
	public static  Connection getConnection(String sysDatasourceName) throws SQLException{
		if(G_DEFAULT_DS.equals(sysDatasourceName)){
			return  EasyPool.getInstance().getConnection(getSysDsName());
		}
		return  EasyPool.getInstance().getConnection(sysDatasourceName);
	}
	
	
	/**
	 * 获得系统数据源
	 * @return
	 */
	private static String getSysDsName(){
		return ServerContext.getProperties("G_SYS_DS", G_DEFAULT_DS);
	}
	
	/**
	 * 获得系统的查询对象
	 * @param sysDatasourceName
	 * @return
	 */
	public static EasyQuery  getAdminQuery(){
		 return EasyQuery.getQuery(getSysDsName());
	}
	
	public static EasyQuery getSqliteQuery() {
		return getSqliteQuery(null);
	}
	/**
	 * 获取console连接信息
	 * @return
	 */
	public static EasyQuery getSqliteQuery(String dbName) {
		if(StringUtils.isBlank(dbName)) {
			 String consoleDsName = "easitline-console";
			 if(EasyPool.datasourceNames.contains(consoleDsName)) {
				 return EasyQuery.getQuery(consoleDsName);
			 }
		}
		String driverName = "org.sqlite.JDBC";
		try {
			Class.forName(driverName);
		} catch (ClassNotFoundException ex) {
			CoreLogger.getPlatform().info("驱动包sqlite-jdbc.jar不存在>>>"+ex.getMessage());
			return getConsoleQuery();
		}
		if(StringUtils.isBlank(dbName)){
			dbName="mars.db";
		}else{
			dbName=dbName+".db";
		}
		String dbpath = Globals.DB_DIR+File.separator+dbName;
		String url    = "jdbc:sqlite:" + dbpath;
		return EasyQuery.getQuery(driverName, url, "", "");
		
	}
	/**
	 * 获取console连接信息
	 * getSqliteQuery 去掉 derby
	 * @return
	 */
	@Deprecated
	public static EasyQuery getConsoleQuery() {
		String driverName = "org.apache.derby.jdbc.EmbeddedDriver";
		try {
			Class.forName(driverName);
		} catch (ClassNotFoundException ex) {
			ex.printStackTrace();
		}
		String dbpath = Globals.DB_DIR;
		String url    = "jdbc:derby:" + dbpath + ";create=true";
		return EasyQuery.getQuery(driverName, url, "", "");
		
	}
	/**
	  * 添加应用服务日志
	 * @param appId
	 * @param msg
	 * @param params ['OPERATE_TYPE','DATE_ID','OPERATE_TIME','MSG']
	 */
	public static void addAppLog(String appId,String msg,JSONObject params) {
		EasyRecord record = new EasyRecord("EASI_APP_LOG","LOG_ID");
		record.set("APP_ID", appId);
		record.set("MSG", msg);
		record.set("DATE_ID", EasyCalendar.newInstance().getDateInt());
		record.set("OPERATE_TIME", EasyDate.getCurrentDateString());
		if(params!=null) {
			record.setColumns(params);
		}
		record.setPrimaryValues(RandomKit.uuid());
		try {
			 getSqliteQuery().save(record);
		} catch (SQLException e) {
			 CoreLogger.getPlatform().error(null,e);;
		}
	}
	/**
	  * 添加应用服务日志
	 * @param appId
	 * @param msg
	 */
	public static void addAppLog(String appId,String msg) {
		addAppLog(appId,msg,null);
	}
	
	public static boolean isDebug(){
		String value = getProperties("G_DEBUG","false");
		if(value.equalsIgnoreCase("false")){
			return false;
		}
		return true;
	}
	
	public static String getContainerType() {
		if(containerType==null) {
			setContainerType(null);
		}
		return containerType;
	}
	
	public static boolean isTomcat() {
		if("tomcat".equals(getContainerType())) {
			return true;
		}else {
			return false;
		}
	}
	

	public static void setContainerType(String type) {
		if(StringUtils.isBlank(type)) {
			 String _containerType = "tomcat";
			 String tongweb = System.getProperty("tongweb.base");
			 if(StringUtils.isNotBlank(tongweb)){
				 _containerType = "tongweb";
			}
			String apusic = System.getProperty("com.apusic.aas.instanceRoot");
			if(StringUtils.notBlank(apusic)){
				_containerType = "apusic";
			}
			String bes = System.getProperty("bes.base");
			if(StringUtils.notBlank(bes)){
				_containerType = "bes";
			}
			ServerContext.containerType = _containerType;
		}else {
			ServerContext.containerType = type;
		}
		CoreLogger.getPlatform().info("containerType->"+getContainerType());
	}


	/**
	 * 获取当前节点安全KEY
	 * @return
	 */
	public static String getSecurityKey(){
		return getProperties("SECURITY_KEY","");
	}
	
	/**
	 * 获取系统名称
	 * @return
	 */
	public static String getServerName(){
		return getProperties("G_SYS_NAME","Mars System");
	}
	
	/**
	 * 获得Mars节点名称
	 * @return
	 */
	public static String getNodeName(){
		return getProperties("G_NODE_NAME","Mars-Node-1");
	}
	
	
	/**
	 * 获得Memcache服务器路径
	 * @return
	 */
	public static String getCacheAddr(){
		return  getProperties("G_MEMCACHE_ADDR","");
	}
	/**
	 * redis memcache
	 * @return
	 */
	public static String getCacheType(){
		return  getProperties("CACHE_TYPE","memcache");
	}
	
	public static String getMQType(){
		return  getProperties("MQ_TYPE","activemq");
	}
	
	public static String getMQPrefix(){
		return  getProperties("MQ_PREFIX","");
	}
	
	public static String getMQAddr(){
	     return ServerContext.getProperties("MQ_ADDR","");
	}
	
	public static String getMQUser(){
		 return ServerContext.getProperties("MQ_USERNAME", "");
	}
	
	public static String getMQPassword(){
		 return ServerContext.getProperties("MQ_PASSWORD", "");
	}
	
	public static String getMQClusterName(){
		 return ServerContext.getProperties("MQ_CLUSTER_NAME", "");
	}
	
	public static String getMQTenantId(){
		 return ServerContext.getProperties("MQ_TENANTID", "");
	}
	
	
	public static String getMQConsumerGroup(String defaultGroup){
		 return ServerContext.getProperties("MQ_CONSUMER_GROUP", defaultGroup);
	}
	
	/**
	 * 获得Mars的文件服务的存储目录
	 * @return
	 */
	public static String getEfsDir(){
		return  getProperties("G_EFS_DIR", Globals.FILE_SERVER_DIR);
	}
	
	/**
	 * 初始化
	 */
	public static void init(){
		if(init) return ;
		init = true ;
		reload();
	}
	
	public static void reload(){
		setContainerType(null);
		config.clear();
		CoreLogger.getPlatform().info("Engine->重新加载Mars系统配置!");
		String sql = "select * from  EASI_CONF ";
		try {
			List<EasyRow> list = getSqliteQuery().queryForList(sql);
			for(EasyRow row :list){
				String configValue = row.getColumnValue("CONF_VALUE");
				configValue = ConfigContext.desDecryptStr(configValue);
				String value = ConfigContext.decryptionStrWithPrefix(configValue,"GV");
				setProterties(row.getColumnValue("CONF_KEY"), value);
				CoreLogger.getPlatform().info("EasyEngine->配置项： "+row.getColumnValue("CONF_KEY")+"="+value);
			}
		} catch (SQLException ex) {
			CoreLogger.getPlatform().info("EasyEngine->始加载Mars全局配置失败，原因："+ex.getMessage());
		}
		
	}
	
	/**
	 * 是否做登录验证码的验证
	 * @return
	 */
	public static boolean isLoginAuth(){
		String osName = System.getProperty("os.name");
        return osName.indexOf("Windows") == -1;
	}
	public static boolean isLinux(){
		String osName = System.getProperty("os.name");
		return osName.indexOf("Windows") == -1;
	}
	
	/**
	 * 获得日志备份数
	 * @return
	 */
	public static int getLogBakCount(){
		return Integer.parseInt(getProperties("G_LOG_BAK_INDEX","8"));
	}
	public static int getLogBakSize(){
		int val=1024*1024*150;
		return Integer.parseInt(getProperties("G_LOG_BAK_SIZE",String.valueOf(val)));
	}
	
	public static String getLogLevelStr() {
		return getProperties("G_LOG_LEVEL","DEBUG");
	}
	
	/**
	 * 日志级别
	 * @return
	 */
	public static Level getLogLevel(){
		String str = getLogLevelStr();
		Level level=Level.ALL;
		switch (str) {
		case "INFO":
			level=Level.INFO;
			break;
		case "ERROR":
			level=Level.ERROR;
			break;
		case "WARN":
			level=Level.WARN;
			break;
		default:
			level=Level.DEBUG;
			break;
		}
		return level;
	}
	
	public static boolean isLog4j2() {
		return false;
	}
	
	
	/**
	 * 获得Mars的全局配置信息
	 * @param key
	 * @param defaultValue
	 * @return
	 */
	public static String  getProperties(String key,String defaultValue){
		if(!config.containsKey(key)){
			//System.out.println("EasyEngine->找不到["+key+"]对应的配置项!");
			return defaultValue;
		}
		String value = config.get(key);
		if(StringUtils.isBlank(value)) {
			return defaultValue;
		}else {
			return StringUtils.trim(value);
		}
	}
	
	/**
	 * 设置mars的全局配置信息
	 * @param key
	 * @param value
	 */
	public static void setProterties(String key,String value){
		if(key == null) return ;
		config.put(key, value);
	}
	public static ConcurrentHashMap<String,String> getConfig(){
		return config;
	}
	
	
	
	
	

}
 