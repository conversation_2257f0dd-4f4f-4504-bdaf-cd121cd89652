package org.easitline.common.core.service;

import java.util.List;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;

import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.common.core.resource.ServiceResource;


/**
 * 服务监听器，应用于WAR，加载和销毁时进行服务的注册和取消注册。
 * <AUTHOR>
 *
 */
public abstract class ServiceContextListener implements ServletContextListener{

	@Override
	public void contextDestroyed(ServletContextEvent event) {
		List<ServiceResource> serviceList = serviceResourceCatalog();
		if(null != serviceList){
			for(int i = 0; i < serviceList.size(); i++){
				ServiceResource service = serviceList.get(i);
				StringBuilder sb = new StringBuilder();
				sb.append("\n");
				sb.append("==========================================="+"\n");
				sb.append("开始卸载Easitline服务\n");
				sb.append(service);
				sb.append("==========================================="+"\n");
				CoreLogger.getPlatform().info(sb.toString());
				ServiceRegistor.getRegistor().unregister(service);
				
				if(service.remoteService) {
					
				}
			}
		}
	}

	@Override
	public void contextInitialized(ServletContextEvent event) {
		List<ServiceResource> serviceList = serviceResourceCatalog();
		if(null != serviceList){
			for(int i = 0; i < serviceList.size(); i++){
				ServiceResource service = serviceList.get(i);
				StringBuilder sb = new StringBuilder();
				sb.append("\n");
				sb.append("==========================================="+"\n");
				sb.append("开始加载Easitline服务\n");
				sb.append(service);
				sb.append("==========================================="+"\n");
			    CoreLogger.getPlatform().info(sb.toString());
				ServiceRegistor.getRegistor().register(service);
				
				if(service.remoteService) {
					
				}
				
			}
		}
	}
	
	/**
	 * 获得服务资源列表
	 * @return
	 */
	protected abstract List<ServiceResource> serviceResourceCatalog();
	
}
