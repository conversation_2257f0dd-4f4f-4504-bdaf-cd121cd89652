package org.easitline.common.core.log.impl;

import java.io.File;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.log4j.AsyncAppender;
import org.apache.log4j.DailyRollingFileAppender;
import org.apache.log4j.Layout;
import org.apache.log4j.Logger;
import org.apache.log4j.RollingFileAppender;
import org.apache.log4j.spi.LoggingEvent;
import org.easitline.common.core.Globals;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.utils.string.StringUtils;

/**
 * MARS日志打印引擎
 * 
 * 例子： 
 *  org.apache.log4j.Logger logger = LogEngine.getLogger("mylog");
 *  org.apache.log4j.Logger logger = LogEngine.getLogger("testapp","mylog");
 *
 */
public class Log4jImpl {

	private static Map<String, Logger> logs = new ConcurrentHashMap<String, Logger>();
	
	// 使用ThreadLocal保证高并发下SimpleDateFormat线程安全
	private static final ThreadLocal<DateFormat> dateFormatThreadLocal = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
	
	private Log4jImpl(){
		
	}
	
	public static void remove(String logName){
		logs.remove(logName);
	}
	public static void removeAll(){
		logs.clear();
	}
	
	/**
	 * 获得Log4J对象
	 * @param logName 根据日志名称，日志名称不需要加.log后缀
	 * @return
	 */
	public static Logger  getLogger(String logName){
		return getLogger("undefine",logName);
	}
	
	/**
	 * 获得应用的日志名称
	 * @param appName  应用名称
	 * @param logName  日志名
	 * @return
	 */
	public static Logger  getLogger(String appName,String logName){
		if(logs.containsKey(logName)){
			return logs.get(logName); // 如果日志已经存在，直接返回。
		}  
		Log4jImpl impl = new Log4jImpl();
		Logger logger = impl.newInstance(appName, logName);
		logs.put(logName, logger);
		return logger;
	}

	/**
	 * 创建新的日志实例
	 * @appName  应用名称，可为空
	 * @logName  日志名称，如果logName为空，则统一输出到out.log中
	 * @return Logger
	 */
	private Logger newInstance(String appName,String logName) {
		//如果logName为空，则统一输出到out.log中
		System.out.println("create log instance > "+logName+",logbakCount > "+ ServerContext.getLogBakCount()+",logBackSize > "+ServerContext.getLogBakSize());
		if(StringUtils.isBlank(logName)) logName = "easitline-out"; 
		AsyncAppender asyncAppender = new AsyncAppender();
		try {
			String rollStrategy = ServerContext.getProperties("rollStrategy", "SIZE");
			if("DATE".equals(rollStrategy)) {
				DailyRollingFileAppender dailyAppender = new DailyRollingFileAppender(new FileLayout(appName, logName),getloggerPath(logName),"'.'yyyy-MM-dd");
				dailyAppender.setEncoding("UTF-8");
				dailyAppender.activateOptions();
				asyncAppender.addAppender(dailyAppender);
			}else {
				RollingFileAppender rollingFileAppender = new RollingFileAppender(new FileLayout(appName,logName), getloggerPath(logName), true);
				rollingFileAppender.setMaxBackupIndex(ServerContext.getLogBakCount());
				rollingFileAppender.setMaximumFileSize(ServerContext.getLogBakSize());
				rollingFileAppender.setEncoding("UTF-8");
				rollingFileAppender.activateOptions();
				asyncAppender.addAppender(rollingFileAppender);
			}
	        
		    // 设置 AsyncAppender 的其他属性
		    asyncAppender.setBufferSize(5000);
		    asyncAppender.setLocationInfo(true);
		    asyncAppender.setBlocking(false);
		    asyncAppender.activateOptions();
		} catch (Exception e) {
			e.printStackTrace();
		}
        // 创建 AsyncAppender，并设置子 Appender
		Logger logger = Logger.getLogger(logName);
		logger.setLevel(ServerContext.getLogLevel());
		logger.removeAllAppenders();
		logger.addAppender(asyncAppender);
		return logger;
	}

	/**
	 * 获得日志的路径
	 * @param logName 日志名称
	 * @return
	 */
	private static String getloggerPath(String logName) {
		String path =  appLogDir() + File.separator + logName + ".log";
		return path;
	}

	private static String appLogDir() {
		return System.getProperty("appLogDir", Globals.LOG_DIR);
	}
	

	 /**
	  * 日志格式化输出
	  * <AUTHOR>
	  *
	  */
	private class FileLayout extends Layout {
		private String logName;
		private String appName;

	    protected FileLayout(String appName,String logName) {
	    	this.logName  = logName;
	    	this.appName  = appName;
		}

		public void activateOptions() {
		}

		@Override
		public String format(LoggingEvent event) {
			Date date = new Date(event.timeStamp);
			String msg = event.getRenderedMessage();
			if (msg == null) msg = "";
			StringBuilder buf = new StringBuilder(msg.length() + 100);
			buf.append(dateFormatThreadLocal.get().format(date)).append("\t");
			buf.append(event.getLevel()).append("\t");
			buf.append("[").append(this.appName).append(":").append(this.logName).append("]\t");
			buf.append(msg);
			if (event.getThrowableStrRep() != null) {
			    buf.append("\r\n");
				String[] rows = event.getThrowableStrRep();
				for (String row : rows) {
					buf.append(row).append("\r\n");
				}
			}else{
				// 修改格式：显示更简洁的位置信息
				buf.append(" - ");
				buf.append(event.getLocationInformation().getClassName()).append(".");
				buf.append(event.getLocationInformation().getMethodName()).append("(");
				buf.append(event.getLocationInformation().getFileName()).append(":");
				buf.append(event.getLocationInformation().getLineNumber()).append(")");
				buf.append("\r\n");
			}
			return buf.toString();
		}

		@Override
		public boolean ignoresThrowable() {
			return false;
		}

	}
		
	
}
