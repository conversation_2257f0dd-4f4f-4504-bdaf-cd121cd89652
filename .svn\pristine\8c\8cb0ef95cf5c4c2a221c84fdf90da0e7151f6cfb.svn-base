package org.easitline.common.core.web;

import java.io.File;


import java.io.IOException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.Part;

import org.apache.log4j.Logger;
import org.easitline.common.annotation.PreAuthorize;
import org.easitline.common.annotation.PreAuthorize.ResIdCheckType;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.framework.vo.Department;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.common.core.sso.PrincipalConvert;
import org.easitline.common.core.sso.UserPrincipal;
import org.easitline.common.core.vo.OperatorLog;
import org.easitline.common.core.web.render.Render;
import org.easitline.common.db.EasyModel;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.utils.kit.JsonKit;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
/**
 * 
 Mars 平台的 servlet 基础类。
   一、 处理以下几种请求：
 1: action   --适用于增加、修改的操作 
 2: query    --应用于查询的操作
 3: method   --适用于之定义方法
 4: login    --适用于处理登录
 二、 /servletPath/method/param1/param2.html
 */
public abstract class EasyBaseServlet extends HttpServlet {
	
	private static final long serialVersionUID = 1609930206407099944L;

	private Logger logger = LogEngine.getLogger(this.getAppName(),this.getLoggerName());

	private ThreadLocal<LocalContext> local = new ThreadLocal<LocalContext>();
	
	/**
	 * 保存应用的上下文
	 */
	protected AppContext appContext = AppContext.getContext(this.getAppName());
			
	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp)throws ServletException, IOException {
		this.service(req, resp,"get");  
		
	}

	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp)throws ServletException, IOException {
		this.service(req, resp,"post");
	}
	
	protected HttpServletRequest getRequest(){
		return this.local.get().getRequest();
	}
	
	protected HttpServletResponse getResponse(){
		return this.local.get().getResponse();
	}
	
	protected String getMothed(){
		return this.local.get().getMethod();
	}
	
	/***
     * 判断一个请求是否为AJAX请求,是则返回true
     */
	protected boolean isAjaxRequest() {
    	String requestType = getRequest().getHeader("X-Requested-With"); 
    	if(requestType == null){
    		return false;
    	}
		return true;
 
	}
	/**
	 * BaseServlet默认实现类service
	 */
	protected void service(HttpServletRequest request,HttpServletResponse response,String requestType) throws ServletException, IOException {
		LocalContext  context = new LocalContext(request,response,requestType);
		this.local.set(context);
		/**
		 * 判断当前用户是否有权限
		 */
		if(!this.isPermission(this.getResId())){
			this.debug("ErrorCode:435,该资源[user:"+this.getRemoteUser()+",resId:"+this.getResId()+"]未对当前用户授权，禁止访问!", null);
			response.sendError(435, "该资源未对当前用户授权，禁止访问!");
			return;
		}
		try {
			if(ServerContext.isDebug()){
				this.printParamInfo();
			}
			Object returnObj = handleService();
			if(returnObj == null) return;
			if(returnObj instanceof EasyResult){
				EasyResult result = (EasyResult)returnObj;
				this.renderJson(result.toJsonString());
			}else if(returnObj instanceof String){
				this.forward((String)returnObj);
			}else if(returnObj instanceof JSONObject){
				JSONObject  jsonObject = (JSONObject)returnObj;
				this.renderJson(JSON.toJSONString(jsonObject,SerializerFeature.WriteNullListAsEmpty,SerializerFeature.IgnoreErrorGetter,SerializerFeature.IgnoreNonFieldGetter));
			}else{
				this.renderJson(JSONObject.toJSONString(returnObj));
			}
			return;
			
		} catch (Exception ex) {
			response.sendError(500, "页面请求异常，原因："+ex.getMessage());
			this.getLogger().error(ex, ex);
		}
	}
	
	private void   printParamInfo(){
		String param = getRequest().getQueryString();
		if(null == param ) {
			this.debug("请求参数为空", null);
			return;
		}
		this.debug("请求参数：urlparams>"+param,null);
	}
	
	/**
	 * 根据Ajax请求的data内容，转成:JSONObject.
	 * JSONObject jsonObject =  this.getJSONObject();
	 * String username = jsonObject.getString("username");
	 * @return
	 */
	protected JSONObject getJSONObject(){
		return  JsonKit.getJSONObject(this.getRequest(), null);
	}
	protected JSONObject getJSONObject(String modelName){
		return  JsonKit.getJSONObject(this.getRequest(), modelName);
	}
	@SuppressWarnings("unchecked")
	protected <T> T getModel(Class<T> modelClass,String modelName) {
		EasyModel bean = null;
		try {
			bean = (EasyModel)modelClass.newInstance();
		} catch (InstantiationException | IllegalAccessException e) {
			this.error(e.getMessage(), e);
		}
		bean.setColumns(this.getJSONObject(modelName));
		return (T)bean;
	}
	protected JSONArray getJSONArray(){
		return JsonKit.getJSONArray(this.getRequest());
	}
	
	protected void keepPara() {
		Map<String, String[]> map = this.getRequest().getParameterMap();
		for (Entry<String, String[]> e: map.entrySet()) {
			String[] values = e.getValue();
			if (values.length == 1)
				this.getRequest().setAttribute(e.getKey(), values[0]);
			else
				this.getRequest().setAttribute(e.getKey(), values);
		}
	}
	
	protected String getPara(int index, String defaultValue) {
		String result = getPara(index);
		return result != null && !"".equals(result) ? result : defaultValue;
	}
	
	protected String getPara(int index) {
		String servletPath= getRequest().getServletPath();
		String url = getRequest().getRequestURI();
		String param = url.substring(url.indexOf(servletPath));
		if(servletPath.equals(param)){
			return "";
		}
		param=param.substring(servletPath.length()+1);
		String[] arrays = param.split("/");
		if(index >= arrays.length-1){
			return "";
		}
		return arrays[index+1];
	}
	
	protected String getPara(String name) {
		return  this.getRequest().getParameter(name);
	}
	protected String getJsonPara(String name) {
		return  getJSONObject().getString(name);
	}
	protected String getPara(String name, String defaultValue) {
		String result = this.getRequest().getParameter(name);
		return StringUtils.notBlank(result) ? result : defaultValue;
	}
	protected void setAttr(String name, Object value) {
		this.getRequest().setAttribute(name, value);
	}
	protected boolean isParaBlank(String paraName) {
		return StringUtils.isBlank(this.getRequest().getParameter(paraName));
	}
	protected boolean isParaExists(String paraName) {
		return this.getRequest().getParameterMap().containsKey(paraName);
	}
	/**
	 * 判断是否是超管
	 * @return
	 */
	protected boolean isSuperUser(){
		return this.getUserPrincipal().isRole("SYS_SUPER_USER");
	}
	
	/**
	 * 获得当前登录用户的UserPrincipal
	 * @return
	 */
	protected UserPrincipal  getUserPrincipal(){
		return PrincipalConvert.getUserPrincipal(getRequest());
	}
	
	/**
	 * 获得当前登陆用户ID
	 * @return
	 */
	protected String getRemoteUser(){
		UserPrincipal userPrincipal=this.getUserPrincipal();
		return userPrincipal!=null?userPrincipal.getName():"";
	}
	/**
	 * 可以根据需求自定义重写获类方法
	 * @return
	 */
	protected String requestMethod(){
		String requestMethod=null;
		String[] strs={"action","query","method","login"};
		for(String str:strs){
			requestMethod = StringUtils.trimToEmpty(this.getRequest().getParameter(str));
			if(StringUtils.notBlank(requestMethod)){
				requestMethod = str+"For"+StringUtils.firstCharToUpperCase(requestMethod); 
				break;
			}
		}
		if(StringUtils.isBlank(requestMethod)){
			String servletPath= getRequest().getServletPath();
			String url = getRequest().getRequestURI();
			String param = url.substring(url.indexOf(servletPath));
			if(servletPath.equals(param)){
				return "actionForIndex";
			}
			param=param.substring(servletPath.length()+1);
			if(StringUtils.isBlank(param)){
				return "actionForIndex";
			}
			String[] arrays = param.split("/");
			String method  =arrays[0];
			method = method.replace(".html", "");
			return method;
		}
		return requestMethod;
		
	}
	/**
	 * 处理客户端请求，这里处理客户端的action & query请求
	 * @return
	 */
	private Object handleService() throws Exception {
		 this.getRequest().setCharacterEncoding("UTF-8");
		 String requestMethod = requestMethod(); 
		 return  this.handleRequest(requestMethod);
	}
	
	protected boolean verifyAuth(PreAuthorize annotation) {
		String[] resIds = annotation.resId();
		String[] roleIds = annotation.roleId();
		ResIdCheckType checkType = annotation.checkType();
		boolean hasAuth = false;
		
		if(roleIds.length>0) {
			for(String roleId:roleIds) {
				boolean hasResAuth = getUserPrincipal().isRole(roleId);
				if(checkType==ResIdCheckType.AND) {
					if(!hasResAuth) {
						hasAuth =  false;
						break;
					}
				}else {
					if(hasResAuth) {
						hasAuth =  true;
						break;
					}
				}
			}
			return hasAuth;
		}
		
		for(String resId:resIds) {
			boolean hasResAuth = getUserPrincipal().isResource(resId);
			if(checkType==ResIdCheckType.AND) {
				if(!hasResAuth) {
					hasAuth =  false;
					break;
				}
			}else {
				if(hasResAuth) {
					hasAuth =  true;
					break;
				}
			}
		}
		return hasAuth;
	}
	
	private Object notAuth(String msg){
		CoreLogger.getMarsReq().warn(msg);
		if(isAjaxRequest()) {
			EasyResult result = new EasyResult();
			result.setErrorCode(403);
			result.addFail(msg);
			return result;
		}else {
			renderHtml(msg);
			return null;
		}
	}
		
	/**
	 * 执行动态方法调用
	 * @param methodName 要执行的方法名
	 * @return 返回结果
	 * @throws Exception
	 */
	private Object invokeMethod(String methodName) throws Exception{
		Method method = null;
		try{
			method = this.getClass().getDeclaredMethod(methodName);
			PreAuthorize classAuth = this.getClass().getAnnotation(PreAuthorize.class);
			PreAuthorize annotation = method.getAnnotation(PreAuthorize.class);
			if(classAuth!=null) {
			   boolean bl  = verifyAuth(classAuth);
			   if(!bl) {
				  return notAuth(classAuth.msg());
			   }
			   if(annotation!=null) {
				   bl  = verifyAuth(annotation);
			   }
			   if(!bl) {
					return notAuth(annotation.msg());
				}
			}else if(annotation!=null) {
				boolean bl  = verifyAuth(annotation);
				if(!bl) {
					return notAuth(annotation.msg());
				}
			}
		}catch(Exception ex){
			this.error("EasyBaseservlet.invokeMehtod()->方法("+methodName+")不存在！", ex);
			if(isAjaxRequest()){
				return EasyResult.fail("ajax request not exsit.");
			}else{
				renderHtml("Request action not exsit.");
				return null;
			}
		}
		try {
			Object retObj =  method.invoke(this); 
			return retObj;
		} catch (Exception ex) {
			this.error("EasyBaseservlet.invokeMehtod()->执行方法["+methodName+"]error,cause:"+ex.getMessage(), ex);
			throw ex;
		}
	}
	
	
	/**
	 * 处理请求,系统自动返回该对象的Json内容
	 * @param methodName
	 * @return
	 * @throws Exception
	 */
	protected Object  handleRequest(String methodName)  throws Exception {
		return  this.invokeMethod(methodName);
	}
	protected void renderJsp(String url){
		this.forward(url);
	}
	protected void renderText(String text){
		Render.renderText(this.getRequest(),this.getResponse(),text);
	}
	protected void renderHtml(String html){
		Render.renderHtml(this.getRequest(),this.getResponse(), html);
	}
	protected void renderXml(String xml){
		Render.renderXml(this.getRequest(),getResponse(),xml);
	}
	protected void renderJson(Object obj){
		Render.renderJson(this.getRequest(),getResponse(),obj);
	}
	protected void renderFile(File file,String downloadFileName,boolean delFile){
		Render.renderFile(this.getRequest(),this.getResponse(),file,downloadFileName,delFile);
	}
	protected void renderFile(File file,String downloadFileName){
		Render.renderFile(this.getRequest(),this.getResponse(),file,downloadFileName,true);
	}
	protected void renderFile(File file){
		Render.renderFile(this.getRequest(),this.getResponse(),file,null);
	}
	/**
	 * 上传文件 注：必须 servlet加上注解@MultipartConfig
	 * @param fieldName 页面文件字段name
	 * @return Part对象 ; getInputStream(),submittedFileName(),size()
	 */
	protected Part getFile(String fieldName) throws IOException, ServletException{
		return getRequest().getPart(fieldName);
	}
	/**
	 * 获取批量上传的所有文件
	 * 注：必须 servlet加上注解@MultipartConfig
	 */
	protected List<Part> getFiles() throws IOException, ServletException{
			Collection<Part> fileList=getRequest().getParts();
			if(fileList!=null){
				List<Part> files=new ArrayList<Part>();
				for (Part part:fileList) {
					files.add(part);
				}
				return files;
			}
			return null;
	}

	/**
	 * 转发请求
	 * @param url	转发url
	 */
	protected void forward(String url){
		try {
			this.getRequest().getRequestDispatcher(url).forward(this.getRequest(), this.getResponse());
		} catch (Exception ex) {
			this.error("request.forward("+url+") error ,casue:"+ex.getMessage(),null);
		}
		
	}
	
	/**
	 * 重定向
	 * @param url 重定向的url
	 * @param response
	 * @throws IOException
	 */
	protected void redirect(String url) {
		try {
			this.getResponse().sendRedirect(url);
		} catch (Exception ex) {
			this.error("response.sendRedirect("+url+") error ,casue:"+ex.getMessage(),null);
		}
		
	}
	
	
	/**
	 * 判断是否有资源访问权限
	 * 可以根据需求@Override此方法
	 * @param resCode	资源编码
	 * @throws IOException
	 */
	protected boolean  isPermission(String resId){
		if(this.getResId() == null) return true;
		return getUserPrincipal().isResource(this.getResId());
	}
	
	
	/**
	 * 获得当前用户所在的单位的部门ID，如果是系统管理员，则返回：0
	 * @return
	 */
	protected String getOwnerUnit(){
		if(this.isSuperUser()) 		return  "0";
	    Department department =  this.getUserPrincipal().getOwnerUnit();
	    if(department==null){return null;}
	    return StringUtils.trimToEmpty(department.getDeptId());
	}


	protected Logger getLogger() {
		if(this.logger == null){
			 String appName= StringUtils.defaultString(this.getAppName(),"easitline-app");
			 String logName=StringUtils.defaultString(this.getLoggerName(),"easitline-app");
			 this.logger = LogEngine.getLogger(appName,logName);
		}
		return this.logger;
	}
	
	private String getRequestContextInfo(){
			return this.getRequest().getRequestURI() +","+this.getRequest().getRemoteUser();
	}
	
	/**
	 *  打印debug日志，当ServerContext.isDebug()为true时，采用info级别进行打印。
	 * @param message
	 * @param e
	 */
	protected void debug(Object message,Throwable e){
		if(ServerContext.isDebug()){
			if(e == null){
				this.getLogger().debug("[DEBUG:"+getRequestContextInfo()+"]:"+this.getLoggerMessage(message));
			}else{
				this.getLogger().debug("[DEBUG:"+getRequestContextInfo()+"]:"+this.getLoggerMessage(message),e);
			}
		}
	}
	
	/**
	 * 打印info日志
	 * @param message
	 * @param e
	 */
	protected void info(Object message,Throwable e){
		if(e == null){
			this.getLogger().info("[INFO:"+getRequestContextInfo()+"]:"+this.getLoggerMessage(message));
		}else{
			this.getLogger().info("[INFO:"+getRequestContextInfo()+"]:"+this.getLoggerMessage(message),e);
		}
		
	}
	
	/**
	 * 打印warn日志
	 * @param message
	 * @param e
	 */
	protected void warn(Object message,Throwable e){
		if(e == null){
			this.getLogger().warn("[WARN:"+getRequestContextInfo()+"]:"+this.getLoggerMessage(message));
		}else{
			this.getLogger().warn("[WARN:"+getRequestContextInfo()+"]:"+this.getLoggerMessage(message),e);
		}
	}
	
	/**
	 * 打印error日志
	 * @param message
	 * @param e
	 */
	protected void error(Object message,Throwable e){
		if(e == null){
			this.getLogger().error("**[ERROR:"+getRequestContextInfo()+"]:"+this.getLoggerMessage(message));
		}else{
			this.getLogger().error("**[ERROR:"+getRequestContextInfo()+"]:"+this.getLoggerMessage(message),e);
		}
	}
	/**
	 * 添加操作日志
	 * @param operatorLog
	 */
	protected void addOperatorLog(OperatorLog operatorLog){
		if(operatorLog==null)operatorLog=new OperatorLog();
		operatorLog.setUrl(this.getRequest().getRequestURL().toString()).setSessionId(this.getRequest().getSession().getId());
		operatorLog.setUserId(getUserPrincipal().getUserId()).setUserAcct(getUserPrincipal().getLoginAcct());
		operatorLog.setIpAddr(WebKit.getIP(getRequest())).setModule(this.getAppName());
		operatorLog.save();
	}
	
	private Object getLoggerMessage(Object message){
		return message;
	}
	
	/**
	 * 获得根据当前的应用名和数据源名获得查询对象
	 * @return
	 */
	protected  EasyQuery getQuery(){
		if("default-ds".equals(getAppDatasourceName())){
			return EasyQuery.getQuery(this.getAppDatasourceName());
		}else{
			return EasyQuery.getQuery(this.getAppName(),this.getAppDatasourceName());
		}
	}
	/**
	 * 获得根据当前的应用名和数据源名获得DB对象
	 * @return
	 */
	protected  EasyQuery getDb(){
		return getQuery();
	}
	/**
	 * 获得当前应用名称
	 * @return
	 */
	protected abstract String getAppName();
	
	/**
	 * 获得当前应用的日志名称
	 * @return
	 */
	protected abstract String getLoggerName();
	
	/**
	 * 获得当前应用数据源名称
	 * @return
	 */
	protected abstract String getAppDatasourceName();
	
	/**
	 * 获得当前servlet对应的资源ID，通过 canAccess方法来判断当前用户是否有权访问该servlet
	 * @return
	 */
	protected abstract String getResId();
	
	

}
