# EasitLine Core 开发者使用手册

## 项目概述

EasitLine Core 是一个基于Java的企业级开发框架，提供了数据库操作、缓存管理、Web请求处理、配置管理等核心功能。本手册将帮助开发者（特别是新人）快速上手使用框架中的常用类。

## 目录

1. [上下文管理类](#上下文管理类)
2. [数据库操作类](#数据库操作类)
3. [缓存管理类](#缓存管理类)
4. [Web处理类](#web处理类)
5. [工具类](#工具类)
6. [配置管理类](#配置管理类)
7. [日志管理类](#日志管理类)
8. [常用操作示例](#常用操作示例)

---

## 上下文管理类

### ServerContext - 服务器上下文

**功能**: 管理全局服务器配置和数据源连接

**主要方法**:
- `getAdminQuery()` - 获取管理员查询对象
- `getSqliteQuery(String dbName)` - 获取SQLite查询对象
- `getConsoleQuery()` - 获取控制台查询对象
- `addAppLog(String appId, String msg)` - 添加应用日志

**使用示例**:
```java
// 获取管理员查询对象
EasyQuery adminQuery = ServerContext.getAdminQuery();

// 添加应用日志
ServerContext.addAppLog("myApp", "应用启动成功");

// 获取SQLite查询对象
EasyQuery sqliteQuery = ServerContext.getSqliteQuery("mars");
```

### AppContext - 应用上下文

**功能**: 管理应用级别的配置和资源

**主要方法**:
- `getContext(String appName)` - 获取指定应用的上下文

**使用示例**:
```java
AppContext appContext = AppContext.getContext("myApp");
```

### ConfigContext - 配置上下文

**功能**: 支持远程/本地加解密、批量加解密、属性获取等功能

**主要方法**:
- `getInstance()` - 获取单例实例
- `decryptionStr(String value, String source)` - 解密字符串
- `decryptionStrWithPrefix(String prefix, String value, String source)` - 带前缀解密

**使用示例**:
```java
ConfigContext config = ConfigContext.getInstance();
String decryptedValue = ConfigContext.decryptionStr("encryptedValue", "DS");
```

---

## 数据库操作类

### EasyQuery - 数据库查询核心类

**功能**: 提供数据库查询、更新、删除等操作

**主要方法**:
- `getQuery(String datasourceName)` - 获取查询对象
- `save(EasyRecord record)` - 保存记录
- `begin()` - 开始事务
- `commit()` - 提交事务
- `rollback()` - 回滚事务

**使用示例**:
```java
// 获取查询对象
EasyQuery query = EasyQuery.getQuery("default-ds");

// 事务操作
query.begin();
try {
    query.save(record);
    query.commit();
} catch (Exception e) {
    query.rollback();
}
```

### EasyRecord - 数据记录类

**功能**: 表示数据库中的一条记录

**主要方法**:
- `EasyRecord(String tableName, String primaryKey)` - 构造函数
- `set(String column, Object value)` - 设置字段值
- `get(String column)` - 获取字段值
- `setColumns(JSONObject json)` - 批量设置字段
- `setPrimaryValues(Object value)` - 设置主键值

**使用示例**:
```java
// 创建记录
EasyRecord record = new EasyRecord("EASI_USER", "USER_ID");
record.set("USER_ID", "123");
record.set("USER_NAME", "张三");
record.setPrimaryValues("123");

// 保存记录
EasyQuery query = ServerContext.getAdminQuery();
query.save(record);
```

### EasyModel - 数据模型基类

**功能**: 提供数据模型的基础功能

**使用示例**:
```java
// 在Servlet中获取模型
MyModel model = getModel(MyModel.class, "user");
```

---

## 缓存管理类

### CacheManager - 缓存管理器

**功能**: 统一管理各种缓存实现（Redis、Memcache等）

**主要方法**:
- `getCache()` - 根据配置自动选择缓存对象
- `getRedis()` - 获取Redis缓存
- `getMemcache()` - 获取Memcache缓存
- `getDefaultCache()` - 获取默认缓存

**使用示例**:
```java
// 获取缓存对象
EasyCache cache = CacheManager.getCache();

// 存储数据
cache.put("userInfo:123", userObject, CacheTime.HOUR);

// 获取数据
User user = cache.get("userInfo:123");

// 删除缓存
cache.delete("userInfo:123");
```

### EasyCache - 缓存接口

**功能**: 定义缓存操作的标准接口

**主要方法**:
- `put(String key, Object value)` - 存储对象（默认3小时）
- `put(String key, Object value, int seconds)` - 存储对象（指定时间）
- `get(String key)` - 获取对象
- `delete(String key)` - 删除对象
- `flushAll()` - 清空所有缓存

### CacheTime - 缓存时间常量

**常用常量**:
- `CacheTime.HOUR` - 1小时
- `CacheTime.DAY` - 1天

---

## Web处理类

### EasyBaseServlet - Web基础Servlet

**功能**: 提供Web请求处理的基础功能

**主要方法**:
- `getJSONObject()` - 获取请求的JSON对象
- `getJSONObject(String modelName)` - 获取指定模型的JSON对象
- `getModel(Class<T> modelClass, String modelName)` - 获取模型对象
- `getQuery()` - 获取数据库查询对象
- `render(Object data)` - 渲染响应数据

**使用示例**:
```java
public class UserServlet extends EasyBaseServlet {
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) {
        // 获取JSON参数
        JSONObject params = getJSONObject();
        String userName = params.getString("userName");
        
        // 获取数据库查询对象
        EasyQuery query = getQuery();
        
        // 处理业务逻辑...
        
        // 返回结果
        render(EasyResult.ok("操作成功"));
    }
    
    @Override
    protected String getAppName() { return "myApp"; }
    
    @Override
    protected String getLoggerName() { return "userServlet"; }
    
    @Override
    protected String getAppDatasourceName() { return "default-ds"; }
    
    @Override
    protected String getResId() { return "user.manage"; }
}
```

### EasyDaoBaseServlet - DAO基础Servlet

**功能**: 提供基于DAO模式的Web请求处理

**使用示例**:
```java
public class UserDaoServlet extends EasyDaoBaseServlet {
    // 类似EasyBaseServlet的使用方式
}
```

### EasyResult - 响应结果类

**功能**: 统一Web响应格式

**主要方法**:
- `ok()` - 成功响应
- `ok(Object data)` - 带数据的成功响应
- `error()` - 错误响应
- `error(int state, String msg)` - 自定义错误响应

**使用示例**:
```java
// 成功响应
return EasyResult.ok(userList, "查询成功");

// 错误响应
return EasyResult.error(500, "用户不存在");

// 检查响应状态
if (result.isOk()) {
    // 处理成功逻辑
}
```

---

## 工具类

### JsonKit - JSON工具类

**功能**: 处理JSON数据转换

**主要方法**:
- `getJSONObject(HttpServletRequest request, String modelName)` - 从请求获取JSON对象
- `getJSONArray(HttpServletRequest request)` - 从请求获取JSON数组

**使用示例**:
```java
// 在Servlet中使用
JSONObject userJson = JsonKit.getJSONObject(request, "user");
JSONArray dataArray = JsonKit.getJSONArray(request);
```

### StringUtils - 字符串工具类

**功能**: 字符串处理工具

**主要方法**:
- `isBlank(String str)` - 判断字符串是否为空
- `notBlank(String str)` - 判断字符串是否不为空

### RandomKit - 随机数工具类

**功能**: 生成随机数和UUID

**主要方法**:
- `uuid()` - 生成UUID
- `randomStr()` - 生成随机字符串

**使用示例**:
```java
String id = RandomKit.uuid();
String randomStr = RandomKit.randomStr();
```

---

## 配置管理类

### DaoContext - DAO上下文基类

**功能**: 提供DAO层的基础功能

**主要方法**:
- `getQuery()` - 获取查询对象
- `getParam(String modelName)` - 获取参数
- `getMethodParam(int index)` - 获取方法参数

**使用示例**:
```java
public class UserDao extends DaoContext {
    
    public JSONObject getUserList() {
        EasyQuery query = getQuery();
        // 查询逻辑...
        return result;
    }
    
    @Override
    protected String getAppName() { return "myApp"; }
    
    @Override
    protected String getLoggerName() { return "userDao"; }
    
    @Override
    protected String getAppDatasourceName() { return "default-ds"; }
}
```

### BaseService - 服务基类

**功能**: 提供服务层的基础功能

**使用示例**:
```java
public class UserService extends BaseService {
    
    public EasyResult createUser(JSONObject userInfo) {
        EasyQuery query = getQuery();
        // 业务逻辑...
        return EasyResult.ok();
    }
    
    @Override
    protected String getAppName() { return "myApp"; }
    
    @Override
    protected String getLoggerName() { return "userService"; }
    
    @Override
    protected String getAppDatasourceName() { return "default-ds"; }
}
```

---

## 日志管理类

### OperatorLog - 操作日志类

**功能**: 记录用户操作日志

**主要方法**:
- `setUserId(String userId)` - 设置用户ID
- `setUserAcct(String userAcct)` - 设置用户账号
- `setModule(String module)` - 设置操作模块
- `save()` - 保存日志

**使用示例**:
```java
OperatorLog log = new OperatorLog()
    .setUserId("123")
    .setUserAcct("admin")
    .setModule("用户管理")
    .setParams(userRecord);
log.save();
```

### CoreLogger - 核心日志类

**功能**: 提供平台级日志记录

**使用示例**:
```java
CoreLogger.getPlatform().info("系统启动成功");
CoreLogger.getPlatform().error("系统异常", exception);
```

---

## 常用操作示例

### 1. 用户管理完整示例

```java
public class UserManageServlet extends EasyBaseServlet {
    
    // 创建用户
    public void createUser() {
        JSONObject params = getJSONObject("user");
        String userId = RandomKit.uuid();
        
        EasyQuery query = getQuery();
        query.begin();
        
        try {
            // 创建用户记录
            EasyRecord userRecord = new EasyRecord("EASI_USER", "USER_ID");
            userRecord.set("USER_ID", userId);
            userRecord.set("USER_NAME", params.getString("userName"));
            userRecord.set("EMAIL", params.getString("email"));
            query.save(userRecord);
            
            // 记录操作日志
            OperatorLog log = new OperatorLog(1, "创建用户")
                .setUserId(getCurrentUserId())
                .setParams(userRecord);
            log.save();
            
            query.commit();
            render(EasyResult.ok("用户创建成功"));
            
        } catch (Exception e) {
            query.rollback();
            error("创建用户失败", e);
            render(EasyResult.error("创建用户失败"));
        }
    }
    
    // 查询用户（带缓存）
    public void getUserInfo() {
        String userId = getJSONObject().getString("userId");
        
        // 先从缓存获取
        EasyCache cache = CacheManager.getCache();
        JSONObject userInfo = cache.get("user:" + userId);
        
        if (userInfo == null) {
            // 缓存不存在，从数据库查询
            EasyQuery query = getQuery();
            // 查询逻辑...
            
            // 存入缓存
            cache.put("user:" + userId, userInfo, CacheTime.HOUR);
        }
        
        render(EasyResult.ok(userInfo));
    }
}
```

### 2. 配置管理示例

```java
// 解密配置
String dbPassword = ConfigContext.decryptionStr("S01_encryptedPassword", "DS");

// 获取应用配置
AppContext appContext = AppContext.getContext("myApp");
```

### 3. 缓存使用示例

```java
// 获取缓存管理器
EasyCache cache = CacheManager.getCache();

// 存储用户信息（1小时过期）
cache.put("user:123", userObject, CacheTime.HOUR);

// 获取用户信息
User user = cache.get("user:123");

// 删除缓存
cache.delete("user:123");

// 清空所有缓存
cache.flushAll();
```

## 最佳实践

1. **数据库操作**: 始终使用事务处理重要操作
2. **缓存使用**: 合理设置缓存过期时间，避免内存泄漏
3. **日志记录**: 重要操作必须记录操作日志
4. **异常处理**: 妥善处理异常，提供友好的错误信息
5. **资源管理**: 及时释放数据库连接等资源

## 注意事项

1. 继承基类时必须实现抽象方法
2. 数据库操作要注意SQL注入防护
3. 缓存key要有明确的命名规范
4. 敏感信息要使用加密存储
5. 定期清理无用的缓存数据

---

## 附录：详细API参考

### EasyQuery 详细API

#### 查询方法
```java
// 执行SQL查询
List<EasyRow> queryForList(String sql, Object... params);

// 查询单个对象
EasyRow queryForObject(String sql, Object... params);

// 分页查询
PageResult queryForPage(String sql, int pageNo, int pageSize, Object... params);

// 统计查询
int queryForInt(String sql, Object... params);
```

#### 更新方法
```java
// 执行更新SQL
int update(String sql, Object... params);

// 保存记录
void save(EasyRecord record) throws SQLException;

// 更新记录
void update(EasyRecord record) throws SQLException;

// 删除记录
void delete(EasyRecord record) throws SQLException;
```

#### 事务方法
```java
// 开始事务
void begin() throws SQLException;

// 提交事务
void commit() throws SQLException;

// 回滚事务
void rollback() throws SQLException;

// 设置自动提交
void setAutoCommit(boolean autoCommit) throws SQLException;
```

### EasyRecord 详细API

#### 基础方法
```java
// 构造函数
EasyRecord(String tableName, String primaryKey);

// 设置表信息
void setTableInfo(String tableName, String primaryKey);

// 获取表名
String getTableName();

// 获取主键名
String getPrimaryKey();
```

#### 数据操作方法
```java
// 设置字段值
EasyRecord set(String column, Object value);

// 获取字段值
<T> T get(String column);

// 批量设置字段
void setColumns(JSONObject json);

// 设置主键值
void setPrimaryValues(Object... values);

// 获取所有字段
Map<String, Object> getColumns();

// 清空所有字段
void clear();
```

### CacheManager 详细配置

#### Redis配置示例
```properties
# Redis单机配置
cache.type=redis
redis.host=127.0.0.1
redis.port=6379
redis.password=
redis.database=0
redis.timeout=2000

# Redis集群配置
redis.cluster.nodes=127.0.0.1:7000,127.0.0.1:7001,127.0.0.1:7002
```

#### Memcache配置示例
```properties
# Memcache配置
cache.type=memcache
memcache.servers=127.0.0.1:11211
memcache.weights=1
```

### 缓存实现类详细说明

#### RedisImpl - Redis缓存实现
```java
// 获取Redis实例
RedisImpl redis = RedisImpl.getInstance();

// 字符串操作
redis.put("key", "value", 3600); // 存储1小时
String value = redis.get("key");

// 哈希操作
redis.hset("user:123", "name", "张三");
String name = redis.hget("user:123", "name");

// 列表操作
redis.lpush("list:key", "value1", "value2");
List<String> list = redis.lrange("list:key", 0, -1);

// 集合操作
redis.sadd("set:key", "member1", "member2");
Set<String> members = redis.smembers("set:key");
```

#### MemcacheImpl - Memcache缓存实现
```java
// 获取Memcache实例
MemcacheImpl memcache = MemcacheImpl.getInstance();

// 基础操作
memcache.put("key", object, CacheTime.HOUR);
Object value = memcache.get("key");
memcache.delete("key");
```

### 日期时间工具类

#### EasyDate - 日期工具类
```java
// 获取当前日期字符串
String dateStr = EasyDate.getCurrentDateString(); // yyyy-MM-dd HH:mm:ss

// 格式化日期
String formatted = EasyDate.format(new Date(), "yyyy-MM-dd");

// 解析日期
Date date = EasyDate.parse("2023-12-01", "yyyy-MM-dd");
```

#### EasyCalendar - 日历工具类
```java
// 创建日历实例
EasyCalendar calendar = EasyCalendar.newInstance();

// 获取日期整数（yyyyMMdd格式）
int dateInt = calendar.getDateInt();

// 日期计算
calendar.addDays(7); // 加7天
calendar.addMonths(1); // 加1个月
```

### 加密解密工具类

#### MD5Util - MD5工具类
```java
// MD5加密
String md5 = MD5Util.getHexMD5("password");

// 验证MD5
boolean isValid = MD5Util.verify("password", md5);
```

#### DESUtil - DES加密工具类
```java
// DES加密
String encrypted = DESUtil.encrypt("plaintext", "secretkey");

// DES解密
String decrypted = DESUtil.decrypt(encrypted, "secretkey");
```

#### SM4Util - SM4加密工具类
```java
// SM4加密
String encrypted = SM4Util.encrypt("plaintext", "secretkey");

// SM4解密
String decrypted = SM4Util.decrypt(encrypted, "secretkey");
```

### 数据库连接池配置

#### EasyPool - 连接池管理
```java
// 获取连接池实例
EasyPool pool = EasyPool.getInstance();

// 数据源配置示例
DSResource ds = new DSResource();
ds.setDatasourceName("myapp-ds");
ds.setDriverClassName("com.mysql.cj.jdbc.Driver");
ds.setUrl("********************************");
ds.setUsername("root");
ds.setPassword("password");
ds.setMaxActive(20);
ds.setMaxIdle(10);
ds.setMinIdle(5);
```

### 权限控制注解

#### @PreAuthorize - 权限控制注解
```java
// 方法级权限控制
@PreAuthorize(resId = "user.create", checkType = ResIdCheckType.METHOD)
public void createUser() {
    // 创建用户逻辑
}

// 类级权限控制
@PreAuthorize(resId = "user.manage")
public class UserManageServlet extends EasyBaseServlet {
    // Servlet逻辑
}
```

### 类型注解

#### @Types - 类型注解
```java
// 指定处理类型
@Types(Types.JSON)
public void handleJsonRequest() {
    // 处理JSON请求
}

@Types(Types.XML)
public void handleXmlRequest() {
    // 处理XML请求
}
```

## 错误处理和调试

### 常见错误及解决方案

#### 1. 数据库连接错误
```java
// 检查数据源配置
EasyQuery query = null;
try {
    query = EasyQuery.getQuery("myapp-ds");
} catch (Exception e) {
    CoreLogger.getPlatform().error("数据源连接失败", e);
    // 检查数据源名称是否正确
    // 检查数据库连接参数
}
```

#### 2. 缓存连接错误
```java
// 检查缓存配置
EasyCache cache = null;
try {
    cache = CacheManager.getCache();
    cache.put("test", "value");
} catch (Exception e) {
    CoreLogger.getPlatform().error("缓存连接失败", e);
    // 检查Redis/Memcache服务是否启动
    // 检查网络连接
}
```

#### 3. JSON解析错误
```java
// 安全的JSON解析
JSONObject params = null;
try {
    params = getJSONObject();
    if (params == null) {
        render(EasyResult.error("请求参数不能为空"));
        return;
    }
} catch (Exception e) {
    error("JSON解析失败", e);
    render(EasyResult.error("请求参数格式错误"));
    return;
}
```

### 调试技巧

#### 1. 启用详细日志
```properties
# log4j.properties
log4j.logger.org.easitline=DEBUG
log4j.logger.org.easitline.common.db=DEBUG
```

#### 2. 数据库SQL调试
```java
// 在查询前后添加日志
debug("执行SQL: " + sql);
List<EasyRow> result = query.queryForList(sql, params);
debug("查询结果数量: " + result.size());
```

#### 3. 缓存调试
```java
// 检查缓存命中情况
Object cached = cache.get(key);
if (cached != null) {
    debug("缓存命中: " + key);
} else {
    debug("缓存未命中: " + key);
}
```

## 性能优化建议

### 1. 数据库优化
- 使用连接池管理数据库连接
- 合理使用事务，避免长事务
- 使用预编译语句防止SQL注入
- 适当使用批量操作

### 2. 缓存优化
- 设置合理的缓存过期时间
- 使用缓存预热策略
- 避免缓存雪崩和穿透
- 定期清理无效缓存

### 3. 内存优化
- 及时释放大对象引用
- 使用对象池复用对象
- 避免内存泄漏

### 4. 并发优化
- 使用线程安全的类
- 合理使用同步机制
- 避免死锁

## 部署和运维

### 1. 配置文件管理
```properties
# 应用配置文件 app.properties
app.name=myapp
app.version=1.0.0
datasource.name=myapp-ds

# 数据库配置
db.driver=com.mysql.cj.jdbc.Driver
db.url=********************************
db.username=root
db.password=S01_encryptedPassword

# 缓存配置
cache.type=redis
redis.host=127.0.0.1
redis.port=6379
```

### 2. 监控和告警
```java
// 添加监控日志
ServerContext.addAppLog("myapp", "用户登录",
    new JSONObject().fluentPut("userId", userId)
                   .fluentPut("ip", request.getRemoteAddr()));

// 性能监控
long startTime = System.currentTimeMillis();
// 业务逻辑...
long endTime = System.currentTimeMillis();
if (endTime - startTime > 1000) {
    CoreLogger.getPlatform().warn("操作耗时过长: " + (endTime - startTime) + "ms");
}
```

### 3. 健康检查
```java
public class HealthCheckServlet extends EasyBaseServlet {

    public void checkHealth() {
        JSONObject health = new JSONObject();

        // 检查数据库连接
        try {
            EasyQuery query = getQuery();
            query.queryForInt("SELECT 1");
            health.put("database", "OK");
        } catch (Exception e) {
            health.put("database", "ERROR: " + e.getMessage());
        }

        // 检查缓存连接
        try {
            EasyCache cache = CacheManager.getCache();
            cache.put("health_check", "OK", 60);
            health.put("cache", "OK");
        } catch (Exception e) {
            health.put("cache", "ERROR: " + e.getMessage());
        }

        render(EasyResult.ok(health));
    }
}
