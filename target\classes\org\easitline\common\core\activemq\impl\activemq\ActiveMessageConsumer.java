package org.easitline.common.core.activemq.impl.activemq;

import javax.jms.JMSException;

import javax.jms.Message;
import javax.jms.MessageConsumer;
import javax.jms.MessageListener;

import org.easitline.common.core.activemq.BrokerContext;
import org.easitline.common.core.activemq.log.ActivieMQLogger;


public class ActiveMessageConsumer implements MessageConsumer  {
	
	private MessageConsumer consumer;
	
	private BrokerContext context;
	
	public static final int CONSUMER_TYPE_QUEUE = 1;
	
	public static final int CONSUMER_TYPE_TOPIC = 2;
	
	
	
	public ActiveMessageConsumer(BrokerContext context,MessageConsumer consumer) {
		this.context = context;
		this.consumer = consumer;
	}
	
	@Override
	public void close() throws JMSException {
	}

	@Override
	public MessageListener getMessageListener() throws JMSException {
		return null;
	}

	@Override
	public String getMessageSelector() throws JMSException {
		return null;
	}

	@Override
	public Message receive() throws JMSException {
		return this.receive(10*1000);
	}

	@Override
	public Message receive(long timeout) throws JMSException {
		try {
			return consumer.receive(timeout);
		} catch (JMSException ex) {
			ActivieMQLogger.getLogger().error(ex,ex);
			try {
				Thread.sleep(3000);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			throw ex;
		}
		
	}

	@Override
	public Message receiveNoWait() throws JMSException {
		return this.receive(5000);
	}

	@Override
	public void setMessageListener(MessageListener arg0) throws JMSException {
	}
	
	@Override
	public String toString() {
		return "ActiveMessageConsumer("+context.getBrokerName()+","+context.getConnector()+"),Consumer对象名："+this.consumer;
	}



}
