package org.easitline.common.core.framework.vo.model;


/**
 * 部门信息模型
 * <AUTHOR>
 *
 */
public class DepartmentModel {

	private String deptId;
	private String deptCode;
	private String deptName;
	private String pdeptCode;
	private String deptType;
	private String linkMan;
	private String linkMobile;
	private String qrcodeUrl;
	private String reserve1;
	private String reserve2;
	private String reserve3;
	private String reserve4;
	private String reserve5;
	private String reserve6;
	private String reserve7;
	private String reserve8;
	private String reserve9;
	private String reserve10;
	private int nodeType = 3;


    /**
     * 获得部门信息
     * @param deptId
     * @param userId
     */
    public DepartmentModel() {
    }
    
    
    
    public int getNodeType() {
		return nodeType;
	}



	public void setNodeType(int nodeType) {
		this.nodeType = nodeType;
	}



    

    /**
     * 获得当前部门所在的层次。
     * @return int 层次，部门代码长度为3的是第一层，长度为6是第二层.....
     */
    public int getLevel() {
        return  this.getDeptCode().length()/3;
    }



    /**
     * 获得部门ID
     * @return
     */
	public String getDeptId() {
		return deptId;
	}

	/**
     * 获得部门代码，格式：001001...
     * @return
     */
	public void setDeptId(String deptId) {
		this.deptId = deptId;
	}


	/**
     * 获得部门代码，格式：001001...
     * @return
     */
	public String getDeptCode() {
		return deptCode;
	}


	public void setDeptCode(String deptCode) {
		this.deptCode = deptCode;
	}


	/**
     * 获得部门名称
     * @return
     */
	public String getDeptName() {
		return deptName;
	}


	public void setDeptName(String deptName) {
		this.deptName = deptName;
	}

	/**
     * 获得父部门代码
     * @return
     */
	public String getPdeptCode() {
		return pdeptCode;
	}


	public void setPdeptCode(String pdeptCode) {
		this.pdeptCode = pdeptCode;
	}

	/**
     * 获得部门类型
     * @return
     */
	public String getDeptType() {
		return deptType;
	}


	public void setDeptType(String deptType) {
		this.deptType = deptType;
	}

	/**
     * 获得部门联系人
     * @return
     */
	public String getLinkMan() {
		return linkMan;
	}


	public void setLinkMan(String linkMan) {
		this.linkMan = linkMan;
	}
	
	/**
	 * 获得部门联系电话
	 * @return
	 */
	public String getLinkMobile() {
		return linkMobile;
	}


	public void setLinkMobile(String linkMobile) {
		this.linkMobile = linkMobile;
	}

	/**
	 * 获得部门二维码链接
	 * @return
	 */
	public String getQrcodeUrl() {
		return qrcodeUrl;
	}


	public void setQrcodeUrl(String qrcodeUrl) {
		this.qrcodeUrl = qrcodeUrl;
	}

	/**
	 * 获得部门保留字段1
	 * @return
	 */
	public String getReserve1() {
		return reserve1;
	}


	public void setReserve1(String reserve1) {
		this.reserve1 = reserve1;
	}

	/**
	 * 获得部门保留字段2
	 * @return
	 */
	public String getReserve2() {
		return reserve2;
	}


	public void setReserve2(String reserve2) {
		this.reserve2 = reserve2;
	}

	/**
	 * 获得部门保留字段3
	 * @return
	 */
	public String getReserve3() {
		return reserve3;
	}


	public void setReserve3(String reserve3) {
		this.reserve3 = reserve3;
	}

	/**
	 * 获得部门保留字段4
	 * @return
	 */
	public String getReserve4() {
		return reserve4;
	}


	public void setReserve4(String reserve4) {
		this.reserve4 = reserve4;
	}

	/**
	 * 获得部门保留字段5
	 * @return
	 */
	public String getReserve5() {
		return reserve5;
	}


	public void setReserve5(String reserve5) {
		this.reserve5 = reserve5;
	}

	/**
	 * 获得部门保留字段6
	 * @return
	 */
	public String getReserve6() {
		return reserve6;
	}


	public void setReserve6(String reserve6) {
		this.reserve6 = reserve6;
	}

	/**
	 * 获得部门保留字段7
	 * @return
	 */
	public String getReserve7() {
		return reserve7;
	}


	public void setReserve7(String reserve7) {
		this.reserve7 = reserve7;
	}

	/**
	 * 获得部门保留字段8
	 * @return
	 */
	public String getReserve8() {
		return reserve8;
	}


	public void setReserve8(String reserve8) {
		this.reserve8 = reserve8;
	}

	/**
	 * 获得部门保留字段9
	 * @return
	 */
	public String getReserve9() {
		return reserve9;
	}


	public void setReserve9(String reserve9) {
		this.reserve9 = reserve9;
	}

	/**
	 * 获得部门保留字段10
	 * @return
	 */
	public String getReserve10() {
		return reserve10;
	}


	public void setReserve10(String reserve10) {
		this.reserve10 = reserve10;
	}
    /**
     * 是否单位
     * @return
     */
	public boolean isUnit(){
		if(this.nodeType == 2) return true;
		return false;
	}
	/**
	 * 是否节点
	 * @return
	 */
	public boolean isNode(){
		if(this.nodeType == 1) return true;
		return false;
	}
	/**
	 * 是否部门
	 * @return
	 */
	public boolean isDepart(){
		if(this.nodeType == 3) return true;
		return false;
	}
    
    
}
