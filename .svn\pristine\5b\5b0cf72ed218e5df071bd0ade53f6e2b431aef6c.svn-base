package org.easitline.common.core.dao.impl;

import java.io.File;

import javax.servlet.ServletContextEvent;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.annotation.engine.Parser;
import org.easitline.common.annotation.engine.TypeEntity;
import org.easitline.common.core.dao.DaoContext;
import org.easitline.common.core.dao.DaoFactory;
import org.easitline.common.core.log.impl.CoreLogger;

public class ApusicDaoContext {
	
	public static void  loadDao(String appDeployDir,ServletContextEvent event)  { 
		
		ClassLoader classLoader = event.getServletContext().getClassLoader();
    	String contextPath = event.getServletContext().getContextPath();
    	String daoPath=event.getServletContext().getInitParameter("daoPath");
    	CoreLogger.getPlatform().info("Load ["+contextPath+","+daoPath+"] @WebObject ..." );
    	long begin=System.currentTimeMillis();
		try {
			File  appDir = new File(appDeployDir+contextPath+"/WEB-INF");
			//dirs = uLoader.getResources("/"); //从应用的根目录中获取资源信息
			File[] files = appDir.listFiles();
			if(files == null){
				CoreLogger.getPlatform().error("Load ["+contextPath+"] @WebObject error,cause: resource[/] is null!" );
				return ;
			}
			for(File dir:files) {
				
	    		//URL url = dirs.nextElement();
	    		CoreLogger.getPlatform().info("Load ["+contextPath+"] @WebObject from -> "+dir.getPath());
	    		//File dir = new File(url.getPath());
	    		if(dir != null) {
	    			if(dir.listFiles() == null) {
	    				CoreLogger.getPlatform().error("Load ["+dir.getPath()+"] @WebObject error,cause: dir.listFiles() is null!" );
	    				continue ;
	    			}
	    		}
	    		loadDirClass(classLoader,dir,"",contextPath,daoPath);
	    	}
			long end=System.currentTimeMillis();
			CoreLogger.getPlatform().info("Load nend time is "+(end-begin)+"ms!");
		} catch (Exception ex) { 
			CoreLogger.getPlatform().error("Load ["+contextPath+"] @WebObject error,cause: "+ex.getMessage() );
			CoreLogger.getPlatform().error(ex.getMessage(),ex);
		}
    }
    /**
     * 从目录中加载类
     * @param dir
     * @param pkg
     */
   private  static  void loadDirClass(ClassLoader classLoader,File dir,String pkg,String contextPath,String daoPath){
	   CoreLogger.getPlatform().info("Load ["+contextPath+"] @WebObject from -> "+dir.getPath());
    	if(dir != null){
    		File[] files = dir.listFiles();
    		if(files == null) return;
    	
    		for(File file:files){
    			if(file.isDirectory()){
    				String nextPackage = "";
    				if(StringUtils.isBlank(pkg)){
    					nextPackage = file.getName();
    				}else{
    					nextPackage = pkg + "." + file.getName();
    				}
    				loadDirClass(classLoader,file,nextPackage,contextPath,daoPath);
    			}else{
    				String className = file.getName();
	    			if(className.endsWith(".class")){
	    				className = className.substring(0, className.length()-6);
	    				loadTypeEntity(classLoader,pkg+"."+className,contextPath,daoPath);
	    			}
    			}
    		}
    	}
    }
    
    private static  void loadTypeEntity(ClassLoader classLoader,String className,String contextPath,String daoPath){
    	try {
    		CoreLogger.getPlatform().info("Load ["+className+"] ...",null);
    		Class<?> clazz = Thread.currentThread().getContextClassLoader().loadClass(className);
    		if(!DaoContext.class.isAssignableFrom(clazz)) return ;
    		TypeEntity typeEntity =  Parser.parse(classLoader.loadClass(className));
    		if(typeEntity == null) return;
    		CoreLogger.getPlatform().info("Load "+typeEntity,null);
    		DaoFactory.putObject(contextPath,daoPath,typeEntity.getName(), typeEntity);
		} catch (Exception ex) {
			CoreLogger.getPlatform().error("Parse  @WebObject ["+contextPath+","+className+"]   error,cause:"+ex.getMessage(),ex);
		} catch(Error er){
			CoreLogger.getPlatform().error("Parse  @WebObject ["+contextPath+","+className+"]   error,cause:"+er.getMessage(),er);
		}
    }
}
