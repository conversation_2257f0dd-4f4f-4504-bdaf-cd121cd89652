package org.easitline.common.core.dao;


import java.sql.SQLException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.sso.PrincipalConvert;
import org.easitline.common.core.sso.UserPrincipal;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.kit.JsonKit;
import org.easitline.common.utils.lang.DictUtil;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;


/**
 * 数据对象工厂
 *
 */
public abstract class DaoContext {

	/**
	 * HttpServletRequest 请求对象
	 */
	protected HttpServletRequest request;
	
	protected HttpServletResponse response;
	
	protected Types type;
	/**
	 * 请求参数
	 */
	protected JSONObject param ; 
	
	private EasyQuery query; 
	
	protected AppContext appContext = AppContext.getContext(this.getAppName());

	/**
	 * 日志
	 */
	private Logger logger;
	
	public DaoContext(){
		
	}
	
	public void  init(HttpServletRequest request,HttpServletResponse response,Types type,JSONObject param){
		this.request = request;
		this.response=response;
		this.param = param;
		this.type= type;
    }
	
	protected String getRemoteUser(){
		return request.getRemoteUser();
	}
	
	private  boolean isLogin(){
		if(getRemoteUser()!=null) return true;
		return false;
	}
	protected void setQuery(EasyQuery query){
		this.query=query;
	}
	/**
	 * method(x,y,z)
	 * @param index 默认从0开始
	 * @return 获取方法参数值
	 */
	protected Object getMethodParam(int index){
		Object value=this.param.get("param["+index+"]");
		return value;
	}
	
	/**
	 * @param modelName 前缀  model.column
	 * @return JSONObject
	 */
	protected JSONObject getParam(String modelName){
		return JsonKit.getJSONObject(param, modelName);
	}
	
	/**
	 * 将数组转换成Map
	 * @param arr
	 * @return
	 */
	protected JSONObject getDictByArray(String... arr){
		Map<String,String>  dict = new LinkedHashMap<String,String>();
		for(int i = 0 ;i<arr.length ;i=i+2){
			dict.put(arr[i],arr[i+1]);
		}
		JSONObject resultJson = new JSONObject();
		resultJson.put("total", dict.size());
		resultJson.put("data", dict);
		return resultJson;
		
	}
	/**
	 * 通过查询返回数字字典
	 * @param sql  sql， 格式: select id as key ,name as value from table1  其中:列表key 和 value是固定命名
	 * @param params  查询参数
	 * @return  JSONObject
	 */
	protected JSONObject getDictByQuery(String sql, Object... params){
		JSONObject resultJson = new JSONObject();
		Map<String, String> dict = new LinkedHashMap<String, String>();
		try {
			List<EasyRow> list =  this.getQuery().queryForList(sql, params);
			for(EasyRow row:list){
				 dict.put(row.getColumnValue(1),row.getColumnValue(2));
			 }
		} catch (Exception ex) {
			resultJson.put("state", 0);
			this.error("queryForMap()执行异常，原因："+ex.getMessage(), ex);
		}
		resultJson.put("total", dict.size());
		resultJson.put("data", dict);
		return  resultJson;
	}
	protected JSONObject emptyPage(){
		return queryForPageList(null,null,null,true);
	}
	protected JSONObject getJsonResult(Object data){
		if(type==Types.LIST){
			return getJsonResult(data,new ArrayList<>());
		}else if(type==Types.RECORD){
			return getJsonResult(data, new JSONObject());
		}else if(type==Types.TEXT){
			return getJsonResult(data,"");
		}else{
			return getJsonResult(data,new JSONObject());
		}
	}
	protected JSONObject getJsonResult(Object data,Object defaultVal){
		JSONObject resultJson = new JSONObject();
		if(data==null){
			resultJson.put("data",defaultVal);
		}else{
			resultJson.put("data",data);
		}
		if(!resultJson.containsKey("state")){
			resultJson.put("state",1);
		}
		if (loginCheck()&&!isLogin()) {
			resultJson.put("msg", "会话不存在或失效!");
			resultJson.put("data", "nosession");
			resultJson.put("state", 0);
		}
		return resultJson;
	}
	/**
	 * 通过字典ID返回数字字典
	 * @param dictTypeId 字典ID
	 * @return  返回查询
	 */
	protected JSONObject getDictById(String dictTypeId){
		 List<Map<String, String>> list = DictUtil.getDict(dictTypeId);
		 Map<String, String> dict = new LinkedHashMap<String, String>(); 
		 for(Map<String, String> item:list){
			 dict.put(item.get("DICT_ID"),item.get("DICT_NAME"));
		 }
		 JSONObject resultJson = new JSONObject();
		 resultJson.put("total", dict.size());
		 resultJson.put("data", dict);
		 return resultJson;
	}
	
	/**
	 * 获得Text内容,主要用于填充input对象
	 * @param object
	 * @return
	 */
	protected JSONObject getText(String textString){
		return getJsonResult(textString,"");
	}
	
	/**
	 * 获得模板数据,主要用于渲染模板数据
	 * @param objectData
	 * @return
	 */
	protected JSONObject getTemplate(Object objectData){
		return getJsonResult(objectData);
	}
	
	/**
	 * 获得Text内容,主要用于填充input对象
	 * @param object
	 * @return
	 */
	protected JSONObject getTree(Object treeObject){
		return getJsonResult(treeObject);
	}
	
	/**
	 * 获得HTML内容，用于在页面填充html内容
	 * @param htmlString
	 * @return
	 */
	protected JSONObject getHtml(String  htmlString){
		return getJsonResult(htmlString,"");
	}
	
	protected JSONObject queryForRecord(EasyRecord record) {
		try {
			if(record!=null&&record.getPrimaryValue()!=null&&StringUtils.isNotBlank(record.getPrimaryValue().toString())){
				return getJsonResult(this.getQuery().findById(record),"{}");
			}else{
				return getJsonResult(null, "{}");
			}
		} catch (SQLException e) {
			this.error("queryForRecord()执行异常，原因："+e.getMessage(), e);
			return getJsonResult(null, "{}");
		}
		
	}
	/**
	 * 可使用queryForRecord 替代此方法
	 * @param obj
	 * @return
	 */
	@Deprecated
	protected JSONObject getRecord(Object obj){
		if(obj instanceof EasyRecord){
			try {
				if(((EasyRecord) obj).getPrimaryValue()!=null&&StringUtils.isNotBlank(((EasyRecord) obj).getPrimaryValue().toString())){
					return getJsonResult(this.getQuery().findById((EasyRecord)obj));
				}else{
					 return getJsonResult(null,"{}");
				}
			} catch (SQLException ex) {
				this.error("getRecord()执行异常，原因："+ex.getMessage(), ex);
				return getJsonResult(null,"{}");
			}
		}else{
			 return getJsonResult(obj);
		}
	}
	
	/**
	 * 查询
	 * @param sql  sql语句
	 * @param params  查询参数
	 * @return  返回查询
	 */
	@SuppressWarnings("unchecked")
	protected JSONObject queryForList(String sql,Object[] params,EasyRowMapper rowMapper){
		List<?>    list = null;
		if(rowMapper == null)  rowMapper = new  JSONMapperImpl();
		try {
			list =  this.getQuery().queryForList(sql, params,rowMapper);
		} catch (Exception ex) {
			list = new ArrayList<Map<String,Object>>();
			this.error("queryForMap()执行异常，原因："+ex.getMessage(), ex);
		}
		JSONObject resultJson=getJsonResult(list);
		resultJson.put("total", list.size());
		return  resultJson;
	}
	
	
	/**
	 * 2.5.1 support
	 * @return List<?>
	 */
	protected List<JSONObject> findList(String sql,Object... params){
		List<JSONObject>    list = null;
		try {
			list =  this.getQuery().queryForList(sql, params,new  JSONMapperImpl());
		} catch (Exception ex) {
			this.error("queryForMap()执行异常，原因："+ex.getMessage(), ex);
		}
		return list;
	}
	
	
	protected JSONObject queryForList(String sql,Object[] params){
		return  queryForList(sql,params,null);
	}
	/**
	 * version above 2.5.1 support
	 * @param sql
	 * @param params
	 * @return
	 */
	@SuppressWarnings({ "unchecked" })
	protected List<JSONObject> findPageList(String sql, Object... params) {
		JSONObject result=queryForPageList(sql,params,new JSONMapperImpl(),false);
		return (List<JSONObject>)result.get("data");
	}
	
	private JSONObject queryForPageList(String sql, Object[] params, EasyRowMapper<?> rowMapper,boolean emptyPage) {
		if (rowMapper == null) 	rowMapper = new JSONMapperImpl();
		int pageIndex = -1;
		int pageSize  = 10;
		int total = -1;
		int pageType=1;
		try {
			pageType  = this.param.getIntValue("pageType");
			pageType  = pageType == 0 ? 1: pageType;
			pageIndex = this.param.getIntValue("pageIndex");
			pageSize  = this.param.getIntValue("pageSize");
			pageSize  = pageSize == 0 ? 1: pageSize;
		} catch (Exception ex) {
			this.warn("无法获取到当前分页的数据，对于Dao对象查询来说，[pageIndex,pageSize]这两个参数是必须的!", ex);
		}
		JSONObject resultJson = new JSONObject();
		List<?>    list = new ArrayList<>();
		try {
			if(emptyPage){
				total=0;
			}else{
				if((pageIndex < 0 && pageType ==1)||pageType>2){
					String   countSql = "select count(1) from ("+getCountSQL(sql)+") temp"; // 转成查记录数量的sql
					total = this.getQuery().queryForInt(countSql, params); // 查询记录总数
				}
				list = this.getQuery().queryForList(sql, params, pageIndex, pageSize, rowMapper);
			}

		} catch (SQLException ex) {
			this.error("DaoContext.queryForPageList()->处理查询结果错误，原因：" + ex.getMessage(), ex);
			resultJson.put("msg", "请求失败(500)");
			resultJson.put("state",0);
			return resultJson;
		}
		if(pageType!=2){
			resultJson.put("totalRow", total);
			resultJson.put("totalPage", (total+(pageSize-1))/pageSize);
		}
		resultJson.put("msg", "请求成功!");
		resultJson.put("state", 1);
		resultJson.put("pageSize", pageSize);
		resultJson.put("pageNumber", pageIndex);
		resultJson.put("data", list);
		resultJson.put("pageType", pageType);
		return resultJson;
	}
	/**
	 * 返回查询结果
	 * @param sql 查询语句
	 * @param params  参数
	 * @param rowMapper  EasyRowMapper的实现，为空时候，缺省为：JSONMapperImpl
	 * @return
	 */
	protected JSONObject queryForPageList(String sql, Object[] params, EasyRowMapper<?> rowMapper)  {
		return queryForPageList(sql,params,rowMapper,false);
	}
	protected JSONObject queryForPageList(String sql, Object[] params){
		return queryForPageList(sql,params,new JSONMapperImpl());
	}
	
	protected JSONObject queryForRecord(String sql,Object... params){
		return queryForRecord(sql, params, new JSONMapperImpl());
	}
	
	/**
	 * 查询
	 * @param sql  sql语句
	 * @param params  查询参数
	 * @return  返回查询
	 */
	protected JSONObject queryForRecord(String sql,Object[] params, EasyRowMapper<?> rowMapper){
		if (rowMapper == null) 	rowMapper = new JSONMapperImpl();
		JSONObject resultJson = new JSONObject();
		Object  value;
		try {
			value = this.getQuery().queryForRow(sql, params,rowMapper);
		} catch (Exception ex) {
			value = new JSONObject();
			this.error("queryForRecord()执行异常，原因："+ex.getMessage(), ex);
		}
		resultJson.put("data", value==null?new JSONObject():value);
		return resultJson;
	}
	
	/**
	 * @param sql
	 * @param params
	 * @param rowMapper
	 * @return
	 * version above 2.5.1 support 
	 */
	protected JSONObject findRecord(String sql,Object... params){
		JSONObject  value;
		try {
			value = this.getQuery().queryForRow(sql, params,new JSONMapperImpl());
		} catch (Exception ex) {
			value = new JSONObject();
			this.error("queryForRecord()执行异常，原因："+ex.getMessage(), ex);
		}
		return value==null?new JSONObject():value;
	}
	
	
	/**
	 * 获得当前登录用户的UserPrincipal
	 * @return
	 */
	protected UserPrincipal  getUserPrincipal(){
		return PrincipalConvert.getUserPrincipal(request);
	}

	
	/**
	 * 判断是否是超级用户
	 * @return
	 */
	protected boolean isSuperUser(){
		return this.getUserPrincipal().isRole("SYS_SUPER_USER");
	}
	
	/**
	 * 获得当前的日志
	 * @return
	 */
	protected Logger getLogger() {
		if(this.logger == null){
			String appName = this.getAppName();
			if(StringUtils.isBlank(appName)) appName = "easitline-app";
			String logName = this.getLoggerName();
			if(StringUtils.isBlank(logName)) logName = "easitline-app";
			this.logger = LogEngine.getLogger(appName,logName);
		}
		return this.logger;
	}
	
	/**
	 * 获得SQL对象
	 * @param sql
	 * @return
	 */
	protected  EasySQL  getEasySQL(String sql){
		 return new EasySQL(sql);
	}
	
	/**
	 *  打印debug日志，当ServerContext.isDebug()为true时，采用info级别进行打印。
	 * @param message
	 * @param e
	 */
	protected void debug(Object message,Throwable e){
		if(ServerContext.isDebug()){
			if(e == null){
				this.getLogger().debug(this.getMessage(message));
			}else{
				this.getLogger().debug(this.getMessage(message),e);
			}
		}
	}
	
	/**
	 * 打印info日志
	 * @param message
	 * @param e
	 */
	protected void info(Object message,Throwable e){
		if(e == null){
			this.getLogger().info(this.getMessage(message));
		}else{
			this.getLogger().info(this.getMessage(message),e);
		}
		
	}
	
	/**
	 * 打印warn日志
	 * @param message
	 * @param e
	 */
	protected void warn(Object message,Throwable e){
		if(e == null){
			this.getLogger().warn(this.getMessage(message));
		}else{
			this.getLogger().warn(this.getMessage(message),e);
		}
	}
	
	/**
	 * 打印error日志
	 * @param message
	 * @param e
	 */
	protected void error(Object message,Throwable e){
		if(e == null){
			this.getLogger().error(this.getMessage(message));
		}else{
			this.getLogger().error(this.getMessage(message),e);
		}
	}
	
	
	/**
	 * 获得根据当前的应用名和数据源名获得查询对象
	 * @return
	 */
	protected  EasyQuery getQuery(){
		if(this.query!=null){
			return this.query;
		}else if("default-ds".equals(getAppDatasourceName())){
			return EasyQuery.getQuery(this.getAppDatasourceName());
		}else{
			return EasyQuery.getQuery(this.getAppName(),this.getAppDatasourceName());
		}
	}
	
	private String getMessage(Object message){
		try {
			StringBuffer buf = new StringBuffer();
			buf.append(this.request.getRequestURI()).append("(").append(this.getUserPrincipal().getUserName()).append(")->").append(message);
			return buf.toString();
		} catch (Exception e) {
			return "";
		}
	}
	
	/**
	 * 获得当前用用ID
	 * @return
	 */
	protected abstract String getAppName();
	
	/**
	 * 获得当前用户的日志名
	 * @return
	 */
	protected abstract String getLoggerName();
	
	/**
	 * 获得当前应用数据源名称
	 * @return
	 */
	protected abstract String getAppDatasourceName();
	
	
	protected abstract boolean  loginCheck();
	
	/**
	  * 在计算SQL执行的行总数时候，先剔除末尾的order by部分的SQL语句，为了兼容sqlserver场景和增加count的执行性能。
	  * @param sql
	  * @return
	  */
	 private static  String getCountSQL(String sql){
		 String[]  sqlpart         = sql.split(" ");
		 boolean   byFlag          = false;
		 int 	   orderTokenIdx = -1;
		 for(int idx = sqlpart.length-1 ;idx>=0 ;idx--){
			 String token = sqlpart[idx];
			 if(token.equals("")){
				 continue;
			 }
			 //如果遇到子查询则不作处理
			 if(token.indexOf(")")>-1) break; 
			 if(token.equalsIgnoreCase("by")){
				 byFlag = true;
				 continue;
			 }
			 if(byFlag){
				 if(token.equalsIgnoreCase("order")){
					 orderTokenIdx = idx ;
					 break;
				 }
				 byFlag = false;
			 }
		 }
		 
		 if(orderTokenIdx == -1) return sql;
		 
		 StringBuffer buf = new StringBuffer();
		 for(int i = 0 ;i<sqlpart.length;i++){
			 if(i == orderTokenIdx) break;
			 buf.append(sqlpart[i]).append(" ");
		 }
		 return buf.toString();
	 }

}
