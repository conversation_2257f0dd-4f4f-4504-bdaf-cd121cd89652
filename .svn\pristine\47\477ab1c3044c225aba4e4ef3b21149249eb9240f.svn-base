package org.easitline.common.core.activemq;

import java.io.Serializable;
import java.util.HashMap;
import java.util.concurrent.BlockingQueue;

import org.easitline.common.core.activemq.log.ActivieMQLogger;

/**
 * MQ消息生成者
 * 
 * <AUTHOR>
 * 
 */
@SuppressWarnings( { "unused", "unchecked" })
public class MQConsumer {

	private BlockingQueue<Serializable> blockingQueue;

	private String connector;

	private String brokerName;

	private boolean threadRunStatus = true;

	private int consumerCount;
	
	private Object syncObj = new Object();

	private static String INNER_BROKER_URL = "vm://broker";

	private Class listenerClass;

	private BrokerHelper[] brokerHelpers ;
	//private Thread[] threads;

	private static HashMap<String, MQConsumer> consumerHandles = new HashMap<String, MQConsumer>();

	public MQConsumer(String connector, String brokerName, Class listenerClass,int consumerCount) {
		
		this.connector = connector;
		this.brokerName = brokerName;
		this.consumerCount = consumerCount;
		this.listenerClass = listenerClass;
		brokerHelpers = new BrokerHelper[this.consumerCount];
		//threads = new Thread[this.consumerCount];
		init();
	}

	private void init() {
		for (int i = 0; i < this.consumerCount; i++) {
			brokerHelpers[i] = new BrokerHelper(i);
			try {
				brokerHelpers[i].register();
			} catch (Exception e) {
				ActivieMQLogger.getLogger().error("Register MQConsumer("+brokerName+") error!");
			}
			// //创建新的线程消费者
			// threads[i] = new Thread(new Consumer(brokerHelpers[i]));
			// threads[i].start();
		}
		new Thread(new ConsumerGuard()).start();
	}

	/**
	 * 创建消息队列Producer
	 * 
	 * @param brokerName
	 * @return
	 */
	public static MQConsumer getInstance(String brokerName,Class listenerClass, int consumerCount) {
		return MQConsumer.getInstance(INNER_BROKER_URL, brokerName,listenerClass, consumerCount);
	}

	/**
	 * 创建消息队列Producer
	 * 
	 * @param brokerUrl
	 *            连接URL，brokerName队列名称
	 * @return
	 */
	public static synchronized MQConsumer getInstance(String brokerUrl,String brokerName, Class listenerClass, int consumerCount) {
		MQConsumer consumer = consumerHandles.get(brokerName);
		if (consumer != null)
			return consumer;
		
		ActivieMQLogger.getLogger().debug("Create  new MQConsumer("+brokerName+")..!");
		consumer = new MQConsumer(brokerUrl, brokerName, listenerClass,consumerCount);
		consumerHandles.put(brokerName, consumer);
		return consumer;
	}

	public void close() {
		// 设置线程状态
		this.threadRunStatus = false;
		for (int i = 0; i < brokerHelpers.length; i++) {
			//threads[i].interrupt();
			brokerHelpers[i].close(); // 关闭broker
		}
		consumerHandles.remove(this.brokerName);
		ActivieMQLogger.getLogger().info("ConsumerBroker(" + brokerName+ ")-> Destory MQProducer complete!");
	}

	/**
	 * 消费者守护线程，保护消费者阻塞，假死情况.
	 * 
	 * <AUTHOR>
	 * 
	 */
	private class ConsumerGuard implements Runnable {
		public void run() {
			ActivieMQLogger.getLogger().info("ConsumerGuard.run("+brokerName+")->thread start;"); 
			while (true) {
				// 每隔10秒检查一次
				if (!threadRunStatus)
					return;
				try {
					Thread.sleep(30 * 1000);
				} catch (Exception ex) {
					//ex.printStackTrace();
					return;
				}
				for (int i = 0; i < brokerHelpers.length; i++) {
					if (brokerHelpers[i] != null) {
						brokerHelpers[i].check(120 * 1000); // 如果发送时间超过5秒，则直接关闭producer重新连接MQ
					}
				}
			}
		}
	}

	/**
	 * MQ消息队列
	 * 
	 * <AUTHOR>
	 * 
	 */
	private class BrokerHelper {
		BrokerMessageListener messageListener = null;
		Long lastRecTime = System.currentTimeMillis(); // 最后接收状态时间

		Broker broker = null;
		int brokerOrder;

		BrokerHelper(int brokerOrder) {
			this.brokerOrder = brokerOrder;

		}

		/**
		 * 检查producer是否阻塞
		 */
		void check(int maxKeepAlive) {
			
			if (messageListener instanceof MessageMonitor) {
				MessageMonitor listener = (MessageMonitor) messageListener;
				ActivieMQLogger.getLogger().debug("ConsumerBroker("+ brokerName+ ":"+ this.brokerOrder+ ") run check-> timediff:"+ (System.currentTimeMillis() - listener.getLastHandleTime())+ ",successcount:" + listener.getSuccessCount()+ ",errorcount:" + listener.getErrorCount());
			}else{
				ActivieMQLogger.getLogger().debug(" ConsumerBroker.healthCheck(" + brokerName + ":"+ this.brokerOrder + ") Run check -> MessageListener not implements  MessageMonitor interface!");
			}
		}

		/**
		 * 重新连接Broker
		 */
		void connectBroker() {
			if (broker != null) {
				broker.close();
			}
			broker = BrokerFactory.getConsumerQueueBroker(connector,brokerName, "", "");
		}

		/**
		 * 关闭队列
		 */
		void close() {
			ActivieMQLogger.getLogger().info("ConsumerBroker(" + brokerName + ":" + this.brokerOrder+ ")-> Start destory MQProducer!");
			try {
				if (broker != null)
					broker.close();
			} catch (Exception ex) {
				ActivieMQLogger.getLogger().error("ConsumerBroker(" + brokerName + ":" + this.brokerOrder+ ")-> Close Broker error,cause:"+ ex.getMessage());
			}
			ActivieMQLogger.getLogger().debug("ConsumerBroker(" + brokerName + ":" + this.brokerOrder+ ")-> Destory MQProducer complete!");
		}

		/**
		 * 发送消息com.suntek.vms.app.mq.broker.MessageRecvListener
		 * 
		 * @param obj
		 */
		void register() {
			ActivieMQLogger.getLogger().debug("ConsumerBroker(" + brokerName + ":" + this.brokerOrder+ ")-> Start register MQConsumer start...!");
			try {
				this.messageListener = (BrokerMessageListener) listenerClass.newInstance();
				if (broker == null) connectBroker();
				this.broker.registMessageListener(this.messageListener);
				ActivieMQLogger.getLogger().debug("ConsumerBroker(" + brokerName + ":" + this.brokerOrder+ ")-> Register MQConsumer complete...!");
			} catch (Exception ex) {
				ActivieMQLogger.getLogger().error("ConsumerBroker(" + brokerName + ":" + this.brokerOrder+ ")-> Register MQConsumer error,cause:"+ ex.getMessage(),ex);
			}
		}
	}

}
