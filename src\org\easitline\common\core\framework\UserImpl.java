package org.easitline.common.core.framework;

import java.sql.SQLException;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.vo.OperatorLog;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.crypt.MD5Util;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;

public class UserImpl {

	/***
	 * 添加用户
	 * @param params("USERNAME","OWNER_DEPT_ID","CREATE_TIME","CREATE_MAN","USER_ACCT","USER_PWD")
	 * @return
	 * @throws SQLException
	 */
	public static EasyResult addUser(JSONObject params) throws SQLException{
		 String[] vs=new String[]{"USERNAME","OWNER_DEPT_ID","CREATE_TIME","CREATE_MAN","USER_ACCT","USER_PWD"};
	        for(String k:vs){
	        	if(StringUtils.isBlank(params.getString(k))){
	        		return EasyResult.fail("字段"+k+"不能为空!");
	        	}
	     }
    	String userName=params.getString("USERNAME");
    	String mobile=params.getString("MOBILE");
    	String loginAcct=params.getString("USER_ACCT");//登录账号
    	String userPwd=params.getString("USER_PWD");
    	params.remove("USER_ACCT");params.remove("USER_PWD");
    	
    	String userId=RandomKit.randomStr();
    	EasyQuery  easyQuery = ServerContext.getAdminQuery();
        	String sql = "SELECT COUNT(*) AS TCOUNT FROM EASI_USER WHERE USERNAME = ? AND STATE <> 9 ";
			if(easyQuery.queryForExist(sql, new Object[]{userName})){
				return EasyResult.fail("输入的用户姓名已被使用，请重新输入！");
			}
		   sql = "SELECT COUNT(*) AS TCOUNT FROM EASI_USER_LOGIN WHERE USER_ACCT = ?  AND LOGIN_TYPE = 1";
	        if(!loginAcct.equals("")){
		        if(easyQuery.queryForExist(sql, new Object[]{loginAcct})){
					return EasyResult.fail("输入的登录账号已被使用，请重新输入！");
		        }
	        }
	        sql = "SELECT COUNT(*) AS TCOUNT FROM EASI_USER WHERE MOBILE = ?";
	        if(!mobile.equals("")){
		        if(easyQuery.queryForExist(sql, new Object[]{mobile})){
					return EasyResult.fail("输入的手机号码已被使用，请重新输入！");
		        }
	        }
	        EasyRecord userRecord=new EasyRecord("EASI_USER","USER_ID");
	        userRecord.setColumns(params);
	        userRecord.set("USER_ID", userId);
	        EasyRecord loginRecord=new EasyRecord("EASI_USER_LOGIN","USER_ID");
	        loginRecord.set("USER_ID",userId);
	        loginRecord.set("USER_ACCT",loginAcct );
	        
	        loginRecord.set("USER_PWD",MD5Util.getHexMD5(userPwd));
	        loginRecord.set("LOGIN_TYPE",1);
	        loginRecord.set("ACCT_STATE",0);
	        loginRecord.set("LOCK_STATE",0);
	        easyQuery.begin();
	        easyQuery.save(userRecord);
	        easyQuery.save(loginRecord);
	        
	        OperatorLog operatorLog=new OperatorLog(1, "新增账号").setParams(userRecord);
	        operatorLog.save();
	        easyQuery.commit();
	        return EasyResult.ok();
	}
}
