package org.easitline.common.core.activemq;

/**
 * 消息监听类
 * 
 * <AUTHOR>
 * @date 2011-9-22 
 * @version V1.0
 */
public interface MessageMonitor {
	
	/**
	 * 获得处理成功数
	 * @return
	 */
	public long  getSuccessCount();
	
	/**
	 * 最后处理时间
	 * @return
	 */
	public long  getLastHandleTime();
	
	/**
	 * 获得处理失败数
	 * @return
	 */
	
	public long getErrorCount();

	/**
	 * 清除统计信息
	 */
	public void  clear();
	
}
