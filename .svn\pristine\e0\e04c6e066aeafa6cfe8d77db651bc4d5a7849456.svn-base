package org.easitline.common.core.web;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class LocalContext {
	/**
	 * HttpServletRequest 请求对象
	 */
	private HttpServletRequest request;
	
	/**
	 * HttpServletResponse 请求对象
	 */
	private HttpServletResponse response; 
	
	/**
	 * 保存当前的请求方式，通常为： post 和  get的方法
	 */
	private String method;
	
	public LocalContext(HttpServletRequest request,HttpServletResponse response,String method){
		this.request  = request;
		this.response = response;
		this.method = method;
	}

	public HttpServletRequest getRequest() {
		return request;
	}

	public HttpServletResponse getResponse() {
		return response;
	}

	public String getMethod() {
		return method;
	}
	
	
	
}
