package org.easitline.common.core.vo;

public class ApiLogWithBLOBs extends ApiLog {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column rt_api_log.request_content
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	private String requestContent;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column rt_api_log.response_content
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	private String responseContent;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column rt_api_log.request_content
	 * @return  the value of rt_api_log.request_content
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	public String getRequestContent() {
		return requestContent;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column rt_api_log.request_content
	 * @param requestContent  the value for rt_api_log.request_content
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	public void setRequestContent(String requestContent) {
		this.requestContent = requestContent == null ? null : requestContent
				.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column rt_api_log.response_content
	 * @return  the value of rt_api_log.response_content
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	public String getResponseContent() {
		return responseContent;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column rt_api_log.response_content
	 * @param responseContent  the value for rt_api_log.response_content
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	public void setResponseContent(String responseContent) {
		this.responseContent = responseContent == null ? null : responseContent
				.trim();
	}
}