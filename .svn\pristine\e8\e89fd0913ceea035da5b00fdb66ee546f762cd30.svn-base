package org.easitline.common.core.activemq;

import java.lang.reflect.Constructor;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.activemq.impl.BrokerImpl;
import org.easitline.common.core.activemq.log.ActivieMQLogger;
import org.easitline.common.core.context.ServerContext;

/**
 * 消息代理工厂，获取消息队列的消息队列代理，进行消息的发送和监听
 * 
 * <AUTHOR> 
 * 
 */
public class BrokerFactory {

	private static  Map<String,Broker> consumerBrokers = new ConcurrentHashMap<String,Broker>();
	private static  Map<String,Broker> producerBrokers = new ConcurrentHashMap<String,Broker>();
	
	/**
	 * 获得真实的MQ名称，需要加上console配置的前缀
	 * @param brokerName
	 * @return
	 */
	private static String getRealBrokerName(String brokerName){
		String prefix = ServerContext.getMQPrefix();
		ActivieMQLogger.getLogger().info("MQPrefix->"+prefix);
		if(StringUtils.isNotBlank(prefix)){
			ActivieMQLogger.getLogger().info("RealBrokerName["+brokerName+"]->"+prefix+brokerName);
			return prefix+brokerName;
		}
		ActivieMQLogger.getLogger().info("RealBrokerName["+brokerName+"]->"+brokerName);
		return brokerName;
	}

	/**
	 * 通过JNDI方式，获取生产者消息队列代理
	 * 
	 * @param brokerName
	 *            消息队列名称，具体可以参考配置文件broker-config.xml
	 * @return
	 */
	public synchronized static Broker getProducerQueueBroker(String brokerName) {
		brokerName = getRealBrokerName(brokerName);
		if(producerBrokers.containsKey(brokerName)) return producerBrokers.get(brokerName);
		ActivieMQLogger.getLogger().info("BrokerFactory.getProducerQueueBroker("+brokerName+") 创建新的队列 - "+getStackTrace());
		BrokerContext context = getBrokerConext(Broker.CONNECTOR_TYPE_PRODUCER, "",brokerName,"","");
		Broker broker =  new BrokerImpl(context);
		producerBrokers.put(brokerName, broker);
		ActivieMQLogger.getLogger().info("["+producerBrokers.size()+"]getProducerQueueBroker("+brokerName+") by "+getStackTrace());
		ActivieMQLogger.getLogger().info("producerBrokers->"+producerBrokers);
		return broker;
	}
	
	

	/**
	 * 通过JNDI方式，获取消费者消息队列代理
	 * 
	 * @param brokerName
	 *            消息队列名称，具体可以参考配置文件broker-config.xml
	 * @return
	 */
	public synchronized  static Broker getConsumerQueueBroker(String brokerName) {
		brokerName = getRealBrokerName(brokerName);
		if(consumerBrokers.containsKey(brokerName)) return consumerBrokers.get(brokerName);
		ActivieMQLogger.getLogger().info("BrokerFactory.getConsumerQueueBroker("+brokerName+")  创建新的队列 - "+getStackTrace());
		BrokerContext context = getBrokerConext(Broker.CONNECTOR_TYPE_CONSUMER,"", brokerName, "","");
		Broker broker =  new BrokerImpl(context);
		consumerBrokers.put(brokerName, broker);
		ActivieMQLogger.getLogger().info("["+consumerBrokers.size()+"]getConsumerQueueBroker("+brokerName+") - "+getStackTrace());
		ActivieMQLogger.getLogger().info("consumerBrokers -> "+consumerBrokers);
		return broker;
	}

	/**
	 * 通过URL方式，获取生产者消息队列代理
	 * 
	 * @param connector
	 *            连接器，消息队列支持多种的链接方式，<br>
	 *            基于远程的通常采用TCP的链接方式，格式如：tcp://172.16.54.188:61616
	 * @param brokerName
	 *            队列名称
	 * @param username
	 *            访问账号
	 * @param password
	 *            访问密码
	 * @return Broker 消息代理对象
	 */
	public synchronized static Broker getProducerQueueBroker(String connector,String brokerName, String username, String password) {
		brokerName = getRealBrokerName(brokerName);
		if(producerBrokers.containsKey(brokerName)) return producerBrokers.get(brokerName);
		ActivieMQLogger.getLogger().info("BrokerFactory.getProducerQueueBroker("+brokerName+") 创建新的队列 - "+getStackTrace());
		BrokerContext context = getBrokerConext(Broker.CONNECTOR_TYPE_PRODUCER, connector, brokerName,username, password);
		Broker broker =  new BrokerImpl(context);
		producerBrokers.put(brokerName, broker);
		ActivieMQLogger.getLogger().info("["+producerBrokers.size()+"]getProducerQueueBroker("+brokerName+") by "+getStackTrace());
		ActivieMQLogger.getLogger().info("producerBrokers -> "+producerBrokers);
		return broker;
	}
	
	/**
	 * 获得消息发布者
	 * @param connector
	 * @param brokerName
	 * @param username
	 * @param password
	 * @return
	 */
	public synchronized  static Broker getProducerTopicBroker(String connector,String brokerName, String username, String password) {
		brokerName = getRealBrokerName(brokerName);
		if(producerBrokers.containsKey(brokerName)) return producerBrokers.get(brokerName);
		BrokerContext context = getBrokerConext(Broker.CONNECTOR_TYPE_TOPIC_PRODUCER, connector, brokerName,username, password);
		Broker broker =  new BrokerImpl(context);
		producerBrokers.put(brokerName, broker);
		ActivieMQLogger.getLogger().info("["+producerBrokers.size()+"]getProducerTopicBroker("+brokerName+") by "+getStackTrace());
		ActivieMQLogger.getLogger().info("producerBrokers -> "+producerBrokers);
		return broker;
	}

	/**
	 * 通过URL方式，获取消费者消息队列代理
	 * 
	 * @param connector
	 *            连接器，消息队列支持多种的链接方式，<br>
	 *            基于远程的通常采用TCP的链接方式，格式如：tcp://172.16.54.188:61616
	 * @param brokerName
	 *            队列名称
	 * @param username
	 *            访问账号
	 * @param password
	 *            访问密码
	 * @return Broker 消息代理对象
	 */
	public synchronized static Broker getConsumerQueueBroker(String connector,String brokerName, String username, String password) {
		brokerName = getRealBrokerName(brokerName);
		if(consumerBrokers.containsKey(brokerName)) return consumerBrokers.get(brokerName);
		ActivieMQLogger.getLogger().info("BrokerFactory.getConsumerQueueBroker("+brokerName+") 创建新的队列 - "+getStackTrace());
		BrokerContext context = getBrokerConext(Broker.CONNECTOR_TYPE_CONSUMER, connector, brokerName,username, password);
		Broker broker =  new BrokerImpl(context);
		consumerBrokers.put(brokerName, broker);
		ActivieMQLogger.getLogger().info("["+consumerBrokers.size()+"]getConsumerQueueBroker("+brokerName+") - "+getStackTrace());
		ActivieMQLogger.getLogger().info("consumerBrokers -> "+consumerBrokers);
		return broker;
	}
	
	/**
	 * 获得消息订阅者
	 * @param connector
	 * @param brokerName
	 * @param username
	 * @param password
	 * @return
	 */
	public  synchronized static Broker getConsumerTopicBroker(String connector,String brokerName, String username, String password) {
		brokerName = getRealBrokerName(brokerName);
		if(consumerBrokers.containsKey(brokerName)) return consumerBrokers.get(brokerName);
		ActivieMQLogger.getLogger().info("BrokerFactory.getConsumerTopicBroker("+brokerName+") 缓存对象不存在，创新新的broker...");
		BrokerContext context = getBrokerConext(Broker.CONNECTOR_TYPE_TOPIC_CONSUMER, connector, brokerName,username, password);
		Broker broker =  new BrokerImpl(context);
		consumerBrokers.put(brokerName, broker);
		ActivieMQLogger.getLogger().info("["+consumerBrokers.size()+"]getConsumerTopicBroker("+brokerName+") by "+getStackTrace());
		ActivieMQLogger.getLogger().info("consumerBrokers -> "+consumerBrokers);
		return broker;
	}
	
	public synchronized static  BrokerContext getBrokerConext(Integer type,String connector,String brokerName, String username, String password){
		String contextClass = System.getProperty("mars.mq.conext");
		String mqtype = ServerContext.getMQType();
		if(StringUtils.isBlank(contextClass)){
			if("rocketmq".equalsIgnoreCase(mqtype)) {
				contextClass = "org.easitline.common.core.activemq.impl.rocketmq.RocketmqBrokerContext";
			}else if("kafka".equalsIgnoreCase(mqtype)){
				contextClass = "org.easitline.common.core.activemq.impl.kafka.KafkaBrokerContext";
			}else if("ctgmq".equalsIgnoreCase(mqtype)){
				contextClass = "org.easitline.common.core.activemq.impl.ctgmq.CtgmqBrokerContext";
			}else if("tonglinkq".equalsIgnoreCase(mqtype)){
				contextClass = "org.easitline.common.core.activemq.impl.tonglinkq.TLQBrokerContext";
			}else{
				contextClass = "org.easitline.common.core.activemq.impl.activemq.ActivemqBrokerContext";
			}
		}
		ActivieMQLogger.getLogger().info("init MQ["+mqtype+"] extends class["+contextClass+"]");
		BrokerContext context = null;
		try {
			Class[] paraTypes = {Integer.class,String.class,String.class,String.class,String.class};
			Object[] paras = {type, connector, brokerName, username, password};
			Class<?> clazz = Class.forName(contextClass);
			Constructor<?> cons = clazz.getConstructor(paraTypes);
			context = (BrokerContext)cons.newInstance(paras);
			context.init();
		} catch (Exception ex) {
			ActivieMQLogger.getLogger().error(ex,ex);
		}
		return context;
	}
	
	public static Throwable getThrowable(String reason){
		Throwable t = new Throwable(reason);
		t.setStackTrace(Thread.currentThread().getStackTrace());
		return t;
	}
	
	public static StackTraceElement getStackTrace(){
		StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
		StackTraceElement e = null;
		int length = stackTrace.length;
		boolean isEasyQueryImpl = false;
		for(int i=0;i<length;i++){
			e = stackTrace[i];
			String className = e.getClassName();
			if(className.startsWith("org.easitline")){
				isEasyQueryImpl = true;
				continue;
			}
			if(isEasyQueryImpl){
				return e;
			}
		}
		return e;
	}
}
