# EasitLine Core API 参考文档

## 核心数据库类

### EasyQuery

数据库查询核心类，提供所有数据库操作功能。

#### 静态方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `getQuery(String datasourceName)` | 数据源名称 | EasyQuery | 获取查询对象 |
| `getQuery(String appName, String datasourceName)` | 应用名, 数据源名 | EasyQuery | 获取指定应用的查询对象 |

#### 查询方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `queryForList(String sql, Object... params)` | SQL语句, 参数 | List<EasyRow> | 查询多条记录 |
| `queryForObject(String sql, Object... params)` | SQL语句, 参数 | EasyRow | 查询单条记录 |
| `queryForInt(String sql, Object... params)` | SQL语句, 参数 | int | 查询整数结果 |
| `queryForString(String sql, Object... params)` | SQL语句, 参数 | String | 查询字符串结果 |

#### 更新方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `update(String sql, Object... params)` | SQL语句, 参数 | int | 执行更新操作 |
| `save(EasyRecord record)` | 记录对象 | void | 保存记录 |
| `update(EasyRecord record)` | 记录对象 | void | 更新记录 |
| `delete(EasyRecord record)` | 记录对象 | void | 删除记录 |

#### 事务方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `begin()` | 无 | void | 开始事务 |
| `commit()` | 无 | void | 提交事务 |
| `rollback()` | 无 | void | 回滚事务 |
| `setAutoCommit(boolean autoCommit)` | 自动提交标志 | void | 设置自动提交 |

### EasyRecord

数据记录类，表示数据库中的一条记录。

#### 构造方法

| 构造方法 | 参数 | 说明 |
|----------|------|------|
| `EasyRecord()` | 无 | 默认构造 |
| `EasyRecord(String tableName, String primaryKey)` | 表名, 主键名 | 指定表和主键 |

#### 基础方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `setTableInfo(String tableName, String primaryKey)` | 表名, 主键名 | void | 设置表信息 |
| `getTableName()` | 无 | String | 获取表名 |
| `getPrimaryKey()` | 无 | String | 获取主键名 |

#### 数据操作方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `set(String column, Object value)` | 字段名, 值 | EasyRecord | 设置字段值 |
| `get(String column)` | 字段名 | Object | 获取字段值 |
| `setColumns(JSONObject json)` | JSON对象 | void | 批量设置字段 |
| `setPrimaryValues(Object... values)` | 主键值 | void | 设置主键值 |
| `getColumns()` | 无 | Map<String, Object> | 获取所有字段 |
| `clear()` | 无 | void | 清空所有字段 |

### EasyRow

查询结果行类，表示查询结果中的一行数据。

#### 主要方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `getColumnValue(String columnName)` | 字段名 | String | 获取字段值（字符串） |
| `getColumnValue(String columnName, Class<T> type)` | 字段名, 类型 | T | 获取指定类型的字段值 |
| `getColumnNames()` | 无 | List<String> | 获取所有字段名 |

## 缓存管理类

### CacheManager

缓存管理器，提供统一的缓存访问接口。

#### 静态方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `getCache()` | 无 | EasyCache | 根据配置自动选择缓存 |
| `getRedis()` | 无 | EasyCache | 获取Redis缓存 |
| `getMemcache()` | 无 | EasyCache | 获取Memcache缓存 |
| `getDefaultCache()` | 无 | EasyCache | 获取默认缓存 |

### EasyCache

缓存操作接口，定义标准的缓存操作。

#### 接口方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `put(String key, Object value)` | 键, 值 | void | 存储对象（默认3小时） |
| `put(String key, Object value, int seconds)` | 键, 值, 秒数 | void | 存储对象（指定时间） |
| `get(String key)` | 键 | T | 获取对象 |
| `delete(String key)` | 键 | void | 删除对象 |
| `flushAll()` | 无 | void | 清空所有缓存 |
| `init()` | 无 | void | 初始化缓存 |
| `stop()` | 无 | void | 停止缓存 |
| `reload()` | 无 | void | 重新加载缓存 |

### CacheTime

缓存时间常量类。

#### 常量定义

| 常量 | 值 | 说明 |
|------|-----|------|
| `CacheTime.MINUTE` | 60 | 1分钟 |
| `CacheTime.HOUR` | 3600 | 1小时 |
| `CacheTime.DAY` | 86400 | 1天 |
| `CacheTime.WEEK` | 604800 | 1周 |
| `CacheTime.MONTH` | 2592000 | 1月 |

## Web处理类

### EasyBaseServlet

Web请求处理基类。

#### 抽象方法（必须实现）

| 方法 | 返回值 | 说明 |
|------|--------|------|
| `getAppName()` | String | 获取应用名称 |
| `getLoggerName()` | String | 获取日志名称 |
| `getAppDatasourceName()` | String | 获取数据源名称 |
| `getResId()` | String | 获取资源ID |

#### 常用方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `getJSONObject()` | 无 | JSONObject | 获取请求JSON对象 |
| `getJSONObject(String modelName)` | 模型名 | JSONObject | 获取指定模型JSON对象 |
| `getJSONArray()` | 无 | JSONArray | 获取请求JSON数组 |
| `getModel(Class<T> modelClass, String modelName)` | 模型类, 模型名 | T | 获取模型对象 |
| `getQuery()` | 无 | EasyQuery | 获取查询对象 |
| `render(Object data)` | 数据对象 | void | 渲染响应数据 |

#### 日志方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `debug(String message)` | 消息 | void | 记录调试日志 |
| `info(String message)` | 消息 | void | 记录信息日志 |
| `warn(String message)` | 消息 | void | 记录警告日志 |
| `error(String message, Throwable e)` | 消息, 异常 | void | 记录错误日志 |

### EasyResult

Web响应结果类。

#### 静态方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `ok()` | 无 | EasyResult | 成功响应 |
| `ok(Object data)` | 数据 | EasyResult | 带数据的成功响应 |
| `ok(Object data, String msg)` | 数据, 消息 | EasyResult | 带数据和消息的成功响应 |
| `error()` | 无 | EasyResult | 默认错误响应 |
| `error(int state, String msg)` | 状态码, 消息 | EasyResult | 自定义错误响应 |

#### 实例方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `isOk()` | 无 | boolean | 判断是否成功 |
| `setData(Object data)` | 数据 | void | 设置数据 |
| `setMsg(String msg)` | 消息 | void | 设置消息 |
| `addError(int state, String msg)` | 状态码, 消息 | void | 添加错误信息 |

#### 常量定义

| 常量 | 值 | 说明 |
|------|-----|------|
| `MSG_SUCCESS_DELETE` | "删除成功!" | 删除成功消息 |
| `MSG_FAIL_DELETE` | "删除失败!" | 删除失败消息 |
| `MSG_SUCCESS_UPDATE` | "更新成功!" | 更新成功消息 |
| `MSG_FAIL_UPDATE` | "更新失败!" | 更新失败消息 |
| `MSG_SUCCESS_ADD` | "添加成功!" | 添加成功消息 |
| `MSG_FAIL_ADD` | "添加失败!" | 添加失败消息 |

## 上下文管理类

### ServerContext

服务器上下文管理类。

#### 静态方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `getAdminQuery()` | 无 | EasyQuery | 获取管理员查询对象 |
| `getConsoleQuery()` | 无 | EasyQuery | 获取控制台查询对象 |
| `getSqliteQuery(String dbName)` | 数据库名 | EasyQuery | 获取SQLite查询对象 |
| `addAppLog(String appId, String msg)` | 应用ID, 消息 | void | 添加应用日志 |
| `addAppLog(String appId, String msg, JSONObject params)` | 应用ID, 消息, 参数 | void | 添加应用日志（带参数） |
| `init()` | 无 | void | 初始化服务器上下文 |

### AppContext

应用上下文管理类。

#### 静态方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `getContext(String appName)` | 应用名 | AppContext | 获取应用上下文 |

### ConfigContext

配置上下文管理类。

#### 静态方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `getInstance()` | 无 | ConfigContext | 获取单例实例 |
| `decryptionStr(String value, String source)` | 密文, 来源 | String | 解密字符串 |
| `decryptionStrWithPrefix(String prefix, String value, String source)` | 前缀, 密文, 来源 | String | 带前缀解密 |

## 工具类

### JsonKit

JSON处理工具类。

#### 静态方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `getJSONObject(HttpServletRequest request, String modelName)` | 请求对象, 模型名 | JSONObject | 从请求获取JSON对象 |
| `getJSONArray(HttpServletRequest request)` | 请求对象 | JSONArray | 从请求获取JSON数组 |

### StringUtils

字符串工具类。

#### 静态方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `isBlank(String str)` | 字符串 | boolean | 判断是否为空 |
| `notBlank(String str)` | 字符串 | boolean | 判断是否不为空 |
| `isEmpty(String str)` | 字符串 | boolean | 判断是否为空字符串 |
| `isNotEmpty(String str)` | 字符串 | boolean | 判断是否不为空字符串 |

### RandomKit

随机数工具类。

#### 静态方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `uuid()` | 无 | String | 生成UUID |
| `randomStr()` | 无 | String | 生成随机字符串 |
| `randomStr(int length)` | 长度 | String | 生成指定长度随机字符串 |
| `randomInt(int min, int max)` | 最小值, 最大值 | int | 生成随机整数 |

### EasyDate

日期工具类。

#### 静态方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `getCurrentDateString()` | 无 | String | 获取当前日期字符串 |
| `getCurrentDateString(String pattern)` | 格式 | String | 获取指定格式当前日期 |
| `format(Date date, String pattern)` | 日期, 格式 | String | 格式化日期 |
| `parse(String dateStr, String pattern)` | 日期字符串, 格式 | Date | 解析日期 |

### EasyCalendar

日历工具类。

#### 静态方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `newInstance()` | 无 | EasyCalendar | 创建日历实例 |

#### 实例方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `getDateInt()` | 无 | int | 获取日期整数（yyyyMMdd） |
| `addDays(int days)` | 天数 | EasyCalendar | 增加天数 |
| `addMonths(int months)` | 月数 | EasyCalendar | 增加月数 |
| `addYears(int years)` | 年数 | EasyCalendar | 增加年数 |

## 日志管理类

### OperatorLog

操作日志类。

#### 构造方法

| 构造方法 | 参数 | 说明 |
|----------|------|------|
| `OperatorLog()` | 无 | 默认构造 |
| `OperatorLog(int type, String operation)` | 类型, 操作 | 指定类型和操作 |

#### 链式方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `setUserId(String userId)` | 用户ID | OperatorLog | 设置用户ID |
| `setUserAcct(String userAcct)` | 用户账号 | OperatorLog | 设置用户账号 |
| `setIpAddr(String ipAddr)` | IP地址 | OperatorLog | 设置IP地址 |
| `setModule(String module)` | 模块 | OperatorLog | 设置操作模块 |
| `setParams(Object params)` | 参数 | OperatorLog | 设置参数 |

#### 其他方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `save()` | 无 | void | 保存日志 |

### CoreLogger

核心日志类。

#### 静态方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `getPlatform()` | 无 | Logger | 获取平台日志器 |

## 加密工具类

### MD5Util

MD5加密工具类。

#### 静态方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `getHexMD5(String input)` | 输入字符串 | String | 获取MD5哈希值 |
| `verify(String input, String hash)` | 输入, 哈希值 | boolean | 验证MD5 |

### DESUtil

DES加密工具类。

#### 静态方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `encrypt(String plaintext, String key)` | 明文, 密钥 | String | DES加密 |
| `decrypt(String ciphertext, String key)` | 密文, 密钥 | String | DES解密 |

### SM4Util

SM4加密工具类。

#### 静态方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `encrypt(String plaintext, String key)` | 明文, 密钥 | String | SM4加密 |
| `decrypt(String ciphertext, String key)` | 密文, 密钥 | String | SM4解密 |

## 注解

### @PreAuthorize

权限控制注解。

#### 属性

| 属性 | 类型 | 说明 |
|------|------|------|
| `resId` | String | 资源ID |
| `checkType` | ResIdCheckType | 检查类型 |

#### 使用示例

```java
@PreAuthorize(resId = "user.create", checkType = ResIdCheckType.METHOD)
public void createUser() {
    // 方法实现
}
```

### @Types

类型注解。

#### 属性

| 属性 | 类型 | 说明 |
|------|------|------|
| `value` | String | 处理类型 |

#### 使用示例

```java
@Types(Types.JSON)
public void handleJsonRequest() {
    // 方法实现
}
```
