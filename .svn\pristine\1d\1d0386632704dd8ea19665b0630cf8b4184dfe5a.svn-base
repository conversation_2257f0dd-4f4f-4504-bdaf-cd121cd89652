package org.easitline.common.core.log;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.impl.Log4jImpl;

/**
 * MARS日志打印引擎
 * 
 * 例子： 
 *  org.apache.log4j.Logger logger = LogEngine.getLogger("mylog");
 *  org.apache.log4j.Logger logger = LogEngine.getLogger("testapp","mylog");
 *
 */
public class LogEngine {
	
	private static boolean isLog4j2 = ServerContext.isLog4j2();
	
	public static Logger  getLogger(String logName){
		if(isLog4j2) {
//			return Log4j2Impl.getLogger(logName);
			return Log4jImpl.getLogger(logName);
		}else {
			return Log4jImpl.getLogger(logName);
		}
	}
	
	public static Logger  getLogger(String appName,String logName){
		return Log4jImpl.getLogger(appName, logName);
	}
	
	public static void remove(String logName){
		if(isLog4j2) {
//			Log4j2Impl.remove(logName);
			Log4jImpl.remove(logName);;
		}else {
			Log4jImpl.remove(logName);;
		}
	}
	
	public static void removeAll() {
		if(isLog4j2) {
//			Log4j2Impl.removeAll();
			Log4jImpl.removeAll();
		}else {
			Log4jImpl.removeAll();
		}
	}
		
}
