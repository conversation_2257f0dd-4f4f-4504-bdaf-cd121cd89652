package org.easitline.common.core.web.websecurity;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringUnicodeUtils {

	/**
	 * 字符串转换unicode
	 */
	public static String string2Unicode(String string) {

		StringBuffer unicode = new StringBuffer();

		for (int i = 0; i < string.length(); i++) {

			// 取出每一个字符
			char c = string.charAt(i);

			// 转换为unicode
			unicode.append("\\u" + Integer.toHexString(c));
		}

		return unicode.toString();
	}

	/**
	 * unicode 转字符串
	 */
	public static String unicode2String(String unicode) {

		StringBuffer string = new StringBuffer();

		String[] hex = unicode.split("\\\\u");

		for (int i = 1; i < hex.length; i++) {

			// 转换出每一个代码点
			int data = Integer.parseInt(hex[i], 16);

			// 追加成string
			string.append((char) data);

			//System.out.println("unicode2String-->" + string.toString());
		}

		return string.toString();
	}

	/**
	 * unicode转字符串升级版，可转换既有unicode又有普通字符的字符串
	 * 
	 * @param unicodeStr
	 *            包含unincode字符的字符串
	 * @return
	 */
	public static String unicodeStr2String(String unicodeStr) {
		if(null==unicodeStr) {//避免空指针异常
			unicodeStr = "";
		}
		int count =0;//统计已进行替换的下标位置
		int length = unicodeStr.length();
		String regEx = "\\\\u[0-9a-fA-F]{4}";//匹配\u0000-\uffff的unicode编码
		Pattern pattern = Pattern.compile(regEx);// 编译正则表达式
		Matcher matcher = pattern.matcher(unicodeStr);
		StringBuffer strBuff = new StringBuffer();
		
		while (matcher.find()) {
			String oldChar = matcher.group();
			String newChar = unicode2String(oldChar);
			int index = unicodeStr.indexOf(oldChar);//符合规则字符串出现的位置
			strBuff.append(unicodeStr.substring(count, index));//添加不符合正则匹配的字符
			strBuff.append(newChar);//添加转换后的字符
			count = index+oldChar.length();
		}
		strBuff.append(unicodeStr.substring(count,length));//添加末尾包含不符合匹配规则的字符

		return strBuff.toString();
	}
	

	public static void main(String[] args) {

		// String unicode = string2Unicode(test);
		String unicode = "\\u0061\\u006C\\u0065\\u0072\\u0074(123)";

		System.out.println(unicodeStr2String(unicode));

	}
}
