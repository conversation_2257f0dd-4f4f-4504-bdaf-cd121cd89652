package org.easitline.common.annotation.engine;



import org.easitline.common.annotation.Types;

/**
 * 列模型描述
 * <AUTHOR>
 *
 */
public class MethodEntity {
	

	private String mothedName ;
	
	private String name;
	
	private Types type;


	public String getMothedName() {
		return mothedName;
	}

	public void setMothedName(String mothedName) {
		this.mothedName = mothedName;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Types getType() {
		return type;
	}
	public String toString(){
		StringBuffer buf = new StringBuffer();
		buf.append("[mothedName:").append(this.mothedName).append(",name:").append(this.name).append(",type:").append(this.type).append("]");
		return buf.toString();
	}

	public void setType(Types type) {
		this.type = type;
	}
	
}
