package org.easitline.common.core.vo;

import java.util.Date;

public class ApiLog {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column rt_api_log.id
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	private Long id;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column rt_api_log.app_key
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	private String appKey;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column rt_api_log.api_method
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	private String apiMethod;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column rt_api_log.access_ip
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	private String accessIp;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column rt_api_log.access_token
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	private String accessToken;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column rt_api_log.request_time
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	private Date requestTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column rt_api_log.response_time
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	private Date responseTime;

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column rt_api_log.id
	 * @return  the value of rt_api_log.id
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	public Long getId() {
		return id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column rt_api_log.id
	 * @param id  the value for rt_api_log.id
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column rt_api_log.app_key
	 * @return  the value of rt_api_log.app_key
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	public String getAppKey() {
		return appKey;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column rt_api_log.app_key
	 * @param appKey  the value for rt_api_log.app_key
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	public void setAppKey(String appKey) {
		this.appKey = appKey == null ? null : appKey.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column rt_api_log.api_method
	 * @return  the value of rt_api_log.api_method
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	public String getApiMethod() {
		return apiMethod;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column rt_api_log.api_method
	 * @param apiMethod  the value for rt_api_log.api_method
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	public void setApiMethod(String apiMethod) {
		this.apiMethod = apiMethod == null ? null : apiMethod.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column rt_api_log.access_ip
	 * @return  the value of rt_api_log.access_ip
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	public String getAccessIp() {
		return accessIp;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column rt_api_log.access_ip
	 * @param accessIp  the value for rt_api_log.access_ip
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	public void setAccessIp(String accessIp) {
		this.accessIp = accessIp == null ? null : accessIp.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column rt_api_log.access_token
	 * @return  the value of rt_api_log.access_token
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	public String getAccessToken() {
		return accessToken;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column rt_api_log.access_token
	 * @param accessToken  the value for rt_api_log.access_token
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	public void setAccessToken(String accessToken) {
		this.accessToken = accessToken == null ? null : accessToken.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column rt_api_log.request_time
	 * @return  the value of rt_api_log.request_time
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	public Date getRequestTime() {
		return requestTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column rt_api_log.request_time
	 * @param requestTime  the value for rt_api_log.request_time
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	public void setRequestTime(Date requestTime) {
		this.requestTime = requestTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column rt_api_log.response_time
	 * @return  the value of rt_api_log.response_time
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	public Date getResponseTime() {
		return responseTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column rt_api_log.response_time
	 * @param responseTime  the value for rt_api_log.response_time
	 * @mbggenerated  Thu Jun 30 18:47:29 CST 2016
	 */
	public void setResponseTime(Date responseTime) {
		this.responseTime = responseTime;
	}
}