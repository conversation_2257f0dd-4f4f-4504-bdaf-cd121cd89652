package org.easitline.common.core.sso;

import java.security.Principal;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import org.easitline.common.core.log.LogEngine;

public class UserPrincipalFactory  implements Runnable{
	
	private static  ConcurrentHashMap<Principal,UserPrincipalProxy> principals = new ConcurrentHashMap<Principal,UserPrincipalProxy>();
	
	private static Thread lifeCycleThread ;
	
	static{
		lifeCycleThread = new Thread(new UserPrincipalFactory());
		lifeCycleThread.start();
	}
	
	public static  UserPrincipal getUserPrincipal(Principal principal){
		UserPrincipalProxy proxy = principals.get(principal);
		if(proxy==null) {
			proxy  = new UserPrincipalProxy(principal);
			principals.put(principal, proxy);
		}
		return proxy.getUserPrincipal();
	}
	
	

	@Override
	public void run() {
		while(true){
			try {
				LogEngine.getLogger("mars-sso").info("Check UserPrincipalProxy timeout ...");
				Collection<UserPrincipalProxy> users  =  principals.values();
				Set<Principal> set = new HashSet<Principal>();
				for(UserPrincipalProxy user:users){
					if(user.isTimeout())  set.add(user.getPrincipal());
				}
				LogEngine.getLogger("mars-sso").info("当前在线用户数："+principals.size()+"，本次处理的回话超时用户数："+set.size());
				for(Principal p:set){
					principals.remove(p);
				}
				
			} catch (Exception ex) {
				// TODO: handle exception
			}
			try {
				Thread.sleep(1800*1000);
			} catch (Exception ex) {
				// TODO: handle exception
			}
		}
	}
}
