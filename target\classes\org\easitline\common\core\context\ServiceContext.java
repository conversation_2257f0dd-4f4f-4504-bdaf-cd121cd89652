package org.easitline.common.core.context;

import java.util.List;
import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.MarsService;
import org.easitline.common.core.service.ServiceAdaptee;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.service.ServiceRegistor;

public class ServiceContext {
	
	public static MarsService getRemoteService(String serviceId) throws ServiceException{
		return null;
	}
	
	
	/**
	 * 获得Eastline服务
	 * @param serviceId 服务ID
	 * @return  IService服务对象
	 * @throws ServiceException  如果服务部存在，或者服务实例化失败时抛出
	 */
	public static IService getService(String serviceId) throws ServiceException{
		ServiceAdaptee  serviceAdaptee = null;
		ServiceResource serviceResource = ServiceRegistor.getRegistor().findServiceByID(serviceId);
		if(serviceResource == null) {
			CoreLogger.getPlatform().warn("ErrorCode:404,服务调用失败，原因：未找到服务["+serviceId+"]注册信息！");
			return null;
		}
		
		try {
			Object messageInstance = serviceResource.newInstance();
			IService service = (IService) messageInstance;
			serviceAdaptee = new ServiceAdaptee(service,serviceResource);
			return serviceAdaptee;
		} catch (Exception ex) {
			CoreLogger.getPlatform().error("ErrorCode:500,服务调用失败，原因：实例化服务["+serviceId+"]失败！",ex);
			return null;
		}
	}
	
	/**
	 * 根据服务ID前缀获得服务ID列表
	 * @return
	 */
	public static List<String> findByPrefix(String  prefixName){
		return ServiceRegistor.getRegistor().findServiceByPrefix(prefixName);
	}
	
	public static void main(String[] args) {
		List<String> serviceIds = ServiceContext.findByPrefix("PAY_NOTIFY_");
	}
	
}
