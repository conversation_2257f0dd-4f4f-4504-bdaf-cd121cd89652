package org.easitline.common.core.web;

import java.io.IOException;
import java.io.PrintWriter;
import java.util.Date;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;

import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.context.impl.TomcatAppContext;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.resource.ApiResource;
import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.service.ServiceRegistor;
import org.easitline.common.core.vo.ApiLogWithBLOBs;
import org.easitline.common.core.vo.MarsResult;
import org.easitline.common.core.vo.RouterReqParam;
import org.easitline.common.db.EasyQuery;

/**
 * 移动请求的接口基础 类
 * <AUTHOR>
 * @date 20170302
 */
public abstract class EasyRouterBaseServlet extends HttpServlet {
	
	private static final long serialVersionUID = 1609930206407099944L;

	private Logger logger;

	/**
	 * HttpSession 对象
	 */
	protected HttpSession session = null;
	
	/**
	 * HttpServletRequest 请求对象
	 */
	protected HttpServletRequest request;
	
	/**
	 * HttpServletResponse 请求对象
	 */
	protected HttpServletResponse response; 
	
	/**
	 * 保存当前的请求方式，通常为： post 和  get的方法
	 */
	protected String method = "post";

	/**
	 * 保存应用的上下文
	 */
	protected AppContext appContext = AppContext.getContext(this.getAppName());
	
			
	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp)
			throws ServletException, IOException {
		this.service(req, resp,"get");  
		
	}

	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp)
			throws ServletException, IOException {
		this.service(req, resp,"post");
	}
	
	/**
	 * 获取业务方法
	 * @param serviceId
	 * @param method
	 * @return
	 */
	public ApiResource getApiResource(String serviceId, String method) {
		ServiceResource sr = null;
		ApiResource ar = null;
		if(null != serviceId && !"".equals(serviceId)) {
			sr = ServiceRegistor.getRegistor().getRegistor().findServiceByID(serviceId);
		}
		if(null != sr) {
			ar = sr.apiResLst.get(method);
		}
		return ar;
	}
	
	/**
	 * 获取配置常量
	 * @param key
	 * @return
	 */
	public String getAppConfigProperty(String key) {
		AppContext appContext = TomcatAppContext.getContext(getAppName());
		String value = "";
		if(null != appContext) {
			value = appContext.getProperty(key, "");
		}
		return value;
	}
	
	/**
	 * request请求参数转化为RouterReqParam对象
	 * @return
	 */
	protected RouterReqParam getRouterReqParam() {
		
		String format = request.getParameter("format");
		if(null == format || "".equals(format)) {
			format="json";
		}
		
		String version = request.getParameter("version");
		if(null == version || "".equals(version)) {
			version="1";
		}
		
		String tempServiceId = request.getParameter("serviceId");
		String serviceId = "";
		String method = "";
		String[] tempArray = new String[2];  
		if(null != tempServiceId && !"".equals(tempServiceId)) {
			tempArray = tempServiceId.split("\\.");
			if(tempArray.length == 2) {
				serviceId = tempArray[0];
				method = tempArray[1];
			} else if(tempArray.length == 1){
				serviceId = tempArray[0];
			}
		}
	
		//获取参数		
		RouterReqParam reqParam = new RouterReqParam(
			serviceId,
			method,
    		request.getParameter("timestamp"),
    		format,
    		request.getParameter("app_key"),
    		version,
    		request.getParameter("sign"),
    		request.getParameter("access_token")
	    );
		
		this.logger.debug("RouterReqParam:" + reqParam);
		
		return reqParam;
	}
	
	/**
	 * 验证参数是否为空
	 * @param routerParam
	 * @return
	 */
	protected boolean hasErrors(RouterReqParam routerParam) {
		
		if(StringUtils.isBlank(routerParam.getServiceId())){
			return true;
		}
		
		/*if(StringUtils.isBlank(routerParam.getMethod())){
			return true;
		}*/
        if(routerParam.getTimestamp() == null){
        	return true;
		}
        if(StringUtils.isBlank(routerParam.getApp_key())){
        	return true;
		}
        if(StringUtils.isBlank(routerParam.getSign())){
        	return true;
		}
		return false;
	}
		
	/**
	 * BaseServlet默认实现类service
	 */
	private void service(HttpServletRequest request, HttpServletResponse response,String requestType) {
		
		this.method = requestType;
		this.request = request;
		this.response = response;

		request.setAttribute("path", request.getContextPath());
		
		ApiLogWithBLOBs apiLog=new ApiLogWithBLOBs();
		
		this.logger = LogEngine.getLogger(this.getAppName(),this.getLoggerName());
		
		MarsResult marsResult = new MarsResult();
		String result = "";
				
		try {
			this.request.setCharacterEncoding("UTF-8");
			result = handleService();
			apiLog.setResponseTime(new Date());
			writeJSONString(result);
		} catch(ServiceException apierr) {
			//api的业务异常
			marsResult.setStateCode(apierr.getErrorCode());
			marsResult.setMessage(apierr.getMessage());	
			
			//记录返回日志
			String content = JSON.toJSONString(result);
			apiLog.setResponseContent(content);
			apiLog.setResponseTime(new Date());
			try {
				this.response.sendError(marsResult.getStateCode());
				writeJSONString(JSON.toJSONString(result));
			} catch (ServletException e) {
				e.printStackTrace();
			} catch (IOException e) {
				e.printStackTrace();
			}	

			return ; 
		} catch(Exception err) {
			this.getLogger().error(err, err);
			
			//api的业务异常
			marsResult.setStateCode(ErrorCode.System_error.getCode());
			marsResult.setMessage(err.getMessage());	

			//记录返回日志
			result = JSON.toJSONString(marsResult);
			apiLog.setResponseContent(result);
			apiLog.setResponseTime(new Date());
			
			this.getLogger().debug(result);
	        try {
	        	this.response.sendError(marsResult.getStateCode());
				writeJSONString(JSON.toJSONString(result));
			} catch (ServletException e) {
				e.printStackTrace();
			} catch (IOException e) {
				e.printStackTrace();
			}
			
			return ; 
		}
	}

	/**
	 * 获得当前登陆用户ID
	 * @return
	 */
	protected String getRemoteUser(){
		this.debug("EasyMobileBaseServlet.getRemoteUser()->getUserPrincipal:"+request.getUserPrincipal(), null);
		if(request.getUserPrincipal() == null) return "";
		else return request.getUserPrincipal().getName();
	}
		
	/**
	 * 处理客户端请求
	 * @return
	 */
	public abstract String handleService() throws ServiceException;

		
	/**
	 * 在Response中写入JSONString
	 * @param jsonString
	 * @throws ServletException
	 * @throws IOException
	 */
	protected void writeJSONString(String  jsonString) throws ServletException, IOException {
		response.setContentType("application/json;charset=UTF-8");   
		response.setCharacterEncoding("UTF-8");   
		response.setHeader("Pragma", "No-cache");   
		response.setHeader("Cache-Control", "no-cache");   
		response.setDateHeader("Expires", 0);   

		PrintWriter pw = response.getWriter();
		pw.write(jsonString);
		pw.flush();
		pw.close();
	}

	
	/**
	 * json数组数据返回帮助
	* @Title: writeJSONArray 
	* @Description: 向浏览器返回json数组
	* @param response
	* @param jsonArray JSONArray
	* @throws ServletException
	* @throws IOException
	* @return void
	 */
	protected void writeJSONArray(JSONArray jsonArray) throws ServletException, IOException {
		response.setContentType("application/json;charset=UTF-8");   
		response.setCharacterEncoding("UTF-8");   
		response.setHeader("Pragma", "No-cache");   
		response.setHeader("Cache-Control", "no-cache");   
		response.setDateHeader("Expires", 0);   
		PrintWriter pw = response.getWriter();
		pw.write(jsonArray.toString());
		pw.flush();
		pw.close();
	}

	private Logger getLogger() {
		if(this.logger == null){
			String appName = this.getAppName();
			if(appName == null) appName = "";
			appName = appName.trim();
			if("".equals(appName)) appName = "easitline-app";
			
			String logName = this.getLoggerName();
			if(logName == null) logName = "";
			logName = logName.trim();
			if("".equals(logName)) logName = "easitline-app";
			
			
			this.logger = LogEngine.getLogger(appName,logName);
		}
		return this.logger;
	}

	private String getRequestContextInfo(){
		try {
			String info = request.getRequestURI() +","+request.getRemoteUser()+","+request.getSession().getId();
			return info ;
		} catch (Exception e) {
			return "";
		}
		
	}
	
	/**
	 *  打印debug日志，当ServerContext.isDebug()为true时，采用info级别进行打印。
	 * @param message
	 * @param e
	 */
	protected void debug(Object message,Throwable e){
		if(ServerContext.isDebug()){
			if(e == null){
				this.getLogger().info("**[DEBUG:"+getRequestContextInfo()+"]:"+this.getLoggerMessage(message));
			}else{
				this.getLogger().info("**[DEBUG:"+getRequestContextInfo()+"]:"+this.getLoggerMessage(message),e);
			}
		}
	}
	
	/**
	 * 打印info日志
	 * @param message
	 * @param e
	 */
	protected void info(Object message,Throwable e){
		if(e == null){
			this.getLogger().info("**[INFO:"+getRequestContextInfo()+"]:"+this.getLoggerMessage(message));
		}else{
			this.getLogger().info("**[INFO:"+getRequestContextInfo()+"]:"+this.getLoggerMessage(message),e);
		}
		
	}
	
	/**
	 * 打印warn日志
	 * @param message
	 * @param e
	 */
	protected void warn(Object message,Throwable e){
		if(e == null){
			this.getLogger().warn("**[WARN:"+getRequestContextInfo()+"]:"+this.getLoggerMessage(message));
		}else{
			this.getLogger().warn("**[WARN:"+getRequestContextInfo()+"]:"+this.getLoggerMessage(message),e);
		}
	}
	
	/**
	 * 打印error日志
	 * @param message
	 * @param e
	 */
	protected void error(Object message,Throwable e){
		if(ServerContext.isDebug()){
			if(e == null){
				this.getLogger().info("**[ERROR:"+getRequestContextInfo()+"]:"+this.getLoggerMessage(message));
			}else{
				this.getLogger().info("**[ERROR:"+getRequestContextInfo()+"]:"+this.getLoggerMessage(message),e);
			}
		}
	}
	
	private Object getLoggerMessage(Object message){
		return message;
	}
	
	/**
	 * 获得根据当前的应用名和数据源名获得查询对象
	 * @return
	 */
	protected  EasyQuery getQuery(){
		if("default-ds".equals(getAppDatasourceName())){
			return EasyQuery.getQuery(this.getAppDatasourceName());
		}else{
			return EasyQuery.getQuery(this.getAppName(),this.getAppDatasourceName());
		}
	}

	/**
	 * 获得当前用用ID
	 * @return
	 */
	protected abstract String getAppName();
	
	/**
	 * 获得当前用户的日志名
	 * @return
	 */
	protected abstract String getLoggerName();
	
	/**
	 * 获得当前应用数据源名称
	 * @return
	 */
	protected abstract String getAppDatasourceName();
	
	/**
	 * 获得当前servlet对应的资源ID，通过 canAccess方法来判断当前用户是否有权访问该servlet
	 * @return
	 */
	protected abstract String getResId();
	
}
