package org.easitline.common.core.context.impl;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

import org.easitline.common.core.EasyPool;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ConfigContext;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.common.db.EasyRow;

import com.alibaba.druid.pool.DruidDataSource;

public class TomcatAppContext extends AppContext {
	
	private String appId;
	private String appName;
	private String appVersion;
	private String warName;
	private String deployTime;
	
	private  Map<String,String> dsInfo =  new ConcurrentHashMap<String, String>();
	
	/// 应用程序配置
	private  Map<String,String> appconfig =  new ConcurrentHashMap<String, String>();
	
	/**
	 * 构造函数
	 * 
	 * @param appName
     * String 应用程序名称
	 */
	public TomcatAppContext(String appId) {
		this.reload(appId);
	}

	/**
	 * 初始化应用
	 */
	public  void initAppInfo() {
		String sql = "select APP_ID,APP_NAME,APP_VERSION,WAR_NAME,DEPLOY_TIME from EASI_APP_INFO  where APP_ID = ?";
		try {
			List<EasyRow> easyRowList = ServerContext.getSqliteQuery().queryForList(sql, this.appId);
			if(null != easyRowList) {
				for(EasyRow easyRow:easyRowList) {
					this.setAppId(easyRow.getColumnValue("APP_ID"));
					this.setAppName(easyRow.getColumnValue("APP_NAME"));
					this.setAppVersion(easyRow.getColumnValue("APP_VERSION"));
					this.setWarName(easyRow.getColumnValue("WAR_NAME"));
					this.setDeployTime(easyRow.getColumnValue("DEPLOY_TIME"));
				}
			}
		} catch (SQLException ex) {
			CoreLogger.getPlatform().error("TomcatAppContext.initAppInfo() error,cause:"+ex.getMessage(),ex);
		}
	}
	
	/**
	 * 初始化应用配置地图
	 */
	private  void initDsInfo() {
		
		if(this.appId == null || "".equals(this.appId) )  return;
		String sql = "select * from EASI_APP_DS where APP_ID = ?";
		CoreLogger.getPlatform().info("应用["+this.appId+"]数据源:"+sql);
		try {
			List<EasyRow> easyRowList = ServerContext.getSqliteQuery().queryForList(sql, this.appId);
			CoreLogger.getPlatform().info("应用["+this.appId+"]数据源个数:"+easyRowList.size());
			if(null != easyRowList) {
				for(EasyRow easyRow:easyRowList) {
					String dsName = easyRow.getColumnValue("DS_NAME");
					String sysDsName = easyRow.getColumnValue("SYS_DS_NAME");
					CoreLogger.getPlatform().info("生成应用["+this.appId+"]数据源和系统数据源的对应关系["+dsName+","+sysDsName+"]");
					dsInfo.put(dsName, sysDsName);
				}
			}
		} catch (SQLException ex) {
			CoreLogger.getPlatform().error("TomcatAppContext.initDsInfo() error,cause:"+ex.getMessage(),ex);
		}
	}
	

	
	/**
	 * 初始化属性
	 * @throws  
	 */
	private void initProperties() {
		if(this.appId == null || "".equals(this.appId))  return;
		String sql = "select APP_ID,ITEM_KEY,ITEM_VALUE from EASI_APP_CONF  where APP_ID = ? ";
		try {
			List<EasyRow> easyRowList = ServerContext.getSqliteQuery().queryForList(sql, this.appId);
			
			if(null != easyRowList) {
				Map<String,String> configMap = new HashMap<String,String>(); 
				for(EasyRow easyRow:easyRowList) {
					String appId = easyRow.getColumnValue("APP_ID");
					String itemKey = easyRow.getColumnValue("ITEM_KEY");
					String itemValue = easyRow.getColumnValue("ITEM_VALUE");
					configMap.put(itemKey, itemValue);
				}
				appconfig = ConfigContext.decryptStrWithPrefix(configMap);
			}
		} catch (SQLException ex) {
			CoreLogger.getPlatform().error("TomcatAppContext.initProperties() error,cause:"+ex.getMessage(),ex);
		}
	}
	
	
	
	/**
	 * 获得应用的配置属性
	 * @param key appId-itmeName
	 * @param defaultValue
	 * @return
	 */
	@Override
	public String getProperty(String key, String defaultValue) {
		if(null == key || "".equals(key)) return defaultValue;
		String  value = appconfig.get(key);
		if(value == null) return defaultValue;
		if("".equals(value.trim())) return defaultValue;
		return value;
	}



	/**
	 * 获得应用数据源对应系统的数据源
	 * 
	 * @param appDatasourceName
	 * @return
	 */
	public String getDatasourceName(String appDatasourceName) {
		if(null == appDatasourceName || "".equals(appDatasourceName)) {
			appDatasourceName = "_default_ds";
		}
		return  this.dsInfo.get(appDatasourceName);
	}
	


	/**
	 * 根据应用的数据源，获取系统的链接
	 * 
	 * @param appDatasourceName
	 * @return
	 */
	public Connection getConnection(String appDatasourceName) {
		try{
			String sysDatasource = this.getDatasourceName(appDatasourceName);
			return EasyPool.getInstance().getConnection(sysDatasource);
		}catch(Exception ex){
			CoreLogger.getPlatform().error(ex,ex);
		};
		return null;
	}
	
	public DruidDataSource getDruidDataSource(String appDatasourceName) {
		try{
			String sysDatasource = this.getDatasourceName(appDatasourceName);
			return EasyPool.getInstance().getDruidDatasource(sysDatasource);
		}catch(Exception ex){
			CoreLogger.getPlatform().error(ex,ex);
		};
		return null;
	}

	/**
	 * 检查当前用户是否具备该角色
	 * 
	 * @param usercode
	 *            用户唯一标识
	 * @param roleId
	 *            角色ID
	 * @return true or false；
	 */
	public boolean isRole(String usercode, String roleId) {
		return false;
	}


	/**
	 * 操作日志
	 * 
	 * @param usercode
	 *            用户唯一标识
	 * @param resourceId
	 *            资源ID
	 * @param parameters
	 *            操作日志参数
	 */
	public void operatorLog(String usercode, String resourceId,Properties parameters) {
	}

	public String getAppName() {
		return appName;
	}

	public void setAppName(String appName) {
		this.appName = appName;
	}

	public String getAppVersion() {
		return appVersion;
	}

	public void setAppVersion(String appVersion) {
		this.appVersion = appVersion;
	}

	public String getWarName() {
		return warName;
	}

	public void setWarName(String warName) {
		this.warName = warName;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public String getDeployTime() {
		return deployTime;
	}

	public void setDeployTime(String deployTime) {
		this.deployTime = deployTime;
	}

	@Override
	public void reload(String appId){
		this.appId = appId;
		this.initAppInfo();
		this.initDsInfo();
		this.initProperties();
	}

	@Override
	public Map<String,String> getPropertys() {
		return appconfig;
	}

	@Override
	public void setProperty(String key, String value) {
		appconfig.put(key, value);
		CoreLogger.getPlatform().info("AppContext.setProperty>key>"+key+">"+value);
	}


}
