package org.easitline.common.core.web;

import java.io.IOException;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.easitline.common.annotation.PreAuthorize;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.PreAuthorize.ResIdCheckType;
import org.easitline.common.annotation.engine.MethodEntity;
import org.easitline.common.annotation.engine.TypeEntity;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.dao.DaoContext;
import org.easitline.common.core.dao.DaoFactory;
import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.common.core.sso.PrincipalConvert;
import org.easitline.common.core.sso.UserPrincipal;
import org.easitline.common.core.web.render.Render;
import org.easitline.common.utils.kit.JsonKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * Dao统一处理类
 *
 */
public  class EasyDaoBaseServlet extends HttpServlet {
	private static final long serialVersionUID = -4153251109598280968L;

	@Override
	protected void doGet(HttpServletRequest request, HttpServletResponse response )	throws ServletException, IOException {
		response.getWriter().write("不允许get访问!");
		return;
	}
	private static JSONObject requestToJsonObject(HttpServletRequest request){
        JSONObject jsonObject = new JSONObject();
        Map<?, ?> pmap = request.getParameterMap();
		Iterator<?> it = pmap.keySet().iterator();
		while (it.hasNext()){
		    String key = it.next().toString();
		    String[] values = (String[])pmap.get(key);
		    if(!key.equals("data")){
		    	jsonObject.put(key, values[0]);
		    }
		}
		jsonObject.putAll(JsonKit.getJSONObject(request, null));
        return jsonObject;
	} 
	private void actionRequest(HttpServletRequest request, HttpServletResponse response,String action){
		String contextPath = request.getContextPath();
		String[] controls=action.split("\\.");
		if(controls.length < 2){
			Render.renderJson(request,response,"{}");
			return;
		}
		JSONObject jsonObject =requestToJsonObject(request);
		TypeEntity typeEntity = null;
		String method = null;
		if(controls.length==3) {
			typeEntity =  DaoFactory.getObjectByPath(controls[0], controls[1]);
			method = controls[2];
		}else{
			typeEntity =  DaoFactory.getObject(contextPath, controls[0]);
			method = controls[1];
		}
		if(typeEntity == null){
			CoreLogger.getMarsReq().error(action+" webObject is not exist.");
			Render.renderJson(request,response,"{}");
			return;
		}
		
		if(StringUtils.isNotBlank(method) &&method.lastIndexOf(")")>-1) {
			method = tranMethodParams(method, jsonObject);  
		}
		MethodEntity methodEntity  = typeEntity.getMethodEntity(method);
		if(methodEntity == null){
			CoreLogger.getMarsReq().error(action+" method is not exist.");
			Render.renderJson(request,response,"{}");
			return;
		}
		JSONObject resultObject = null;
		try {
			resultObject = (JSONObject)this.invokeMethod(typeEntity.getClazz(), methodEntity.getMothedName(),methodEntity.getType(), jsonObject, request,response);
		} catch (Exception e) {
			CoreLogger.getMarsReq().error(e.getMessage(),e);
			Render.renderJson(request,response,"{}");
			return;
		}
		Render.renderJson(request,response,resultObject);
		return;
	}
	@Override
	protected void doPost(HttpServletRequest request, HttpServletResponse response )throws ServletException, IOException {
		//获得请求对象
		String contextPath = request.getContextPath();
		String action=request.getParameter("action");
		if(StringUtils.notBlank(action)){
			actionRequest(request,response,action);
			return;
		}
		
		JSONObject jsonObject  = JsonKit.getJSONObject(request, null);
		
		if(jsonObject.isEmpty()) {
			CoreLogger.getMarsReq().error(contextPath+"->请求格式错误，原因：data域为空！");
			Render.renderJson(request,response,"{}");
			return;
		}
		String data = request.getParameter("data");
		if(ServerContext.isDebug()){
			CoreLogger.getMarsReq().debug(request.getRequestURI()+"->data:"+data);
		}
		
		
		
		//请求参数
		JSONObject params = jsonObject.getJSONObject("params");
		if(params == null) {
			CoreLogger.getMarsReq().error(contextPath+"->请求格式错误，原因：params域为空！");
			Render.renderJson(request,response,"{}");
			return;
		}
		//控件
		JSONArray  controls = jsonObject.getJSONArray("controls");
		if(controls == null) {
			CoreLogger.getMarsReq().error(contextPath+"->请求格式错误，原因：controls域为空！");
			Render.renderJson(request,response,"{}");
			return;
		}
		JSONObject result = new JSONObject();
		
		int size = controls.size();
		for(int i = 0 ; i < size ; i++){
			String controlName = controls.getString(i);
			try{
				String[] item = controlName.split("\\.");
				if(item.length < 2){
					CoreLogger.getMarsReq().error(contextPath+"->@WebObject["+controlName+"]命名错误，正确格式为：@WebObject.@WebControl");
					continue;
				}
				TypeEntity typeEntity = null;
				String method = null;
				if(item.length==3) {
					method = item[2];
					typeEntity = DaoFactory.getObjectByPath(item[0], item[1]);
				}else {
					method = item[1];
					typeEntity = DaoFactory.getObject(contextPath, item[0]);
				}
				if(typeEntity == null){
					CoreLogger.getMarsReq().error(contextPath+"->@WebObject["+controlName+"] not defined!");
					continue;
				}
				//解析方法的参数
				if(StringUtils.isNotBlank(method) &&method.lastIndexOf(")")>-1) {
					method = tranMethodParams(method, params);  
				}
				MethodEntity methodEntity  = typeEntity.getMethodEntity(method);
				if(methodEntity == null){
					CoreLogger.getMarsReq().error(contextPath+"->@WebControl["+controlName+"]["+method+"] not found! "+typeEntity);
					continue;
				}
				
				JSONObject  resultObject = (JSONObject)this.invokeMethod(typeEntity.getClazz(),methodEntity.getMothedName(),methodEntity.getType(), params, request,response);
				if(resultObject == null){
					CoreLogger.getMarsReq().error(contextPath+"->@WebControl["+controlName+"]["+method+"]调用失败，原因：返回值为空或者不是有效的JSONObject!");
					continue;
				}
				resultObject.put("type", methodEntity.getType());
				result.put(controlName, resultObject);
			}catch(Exception ex){
				CoreLogger.getMarsReq().error(contextPath+"->@WebControl["+controlName+"]调用失败，原因："+ex.getMessage(),ex);
			}
		}
		Render.renderJson(request,response,result);
		return;
		
	}
	
	/**
	 * 解析方法参数
	 * @return
	 */
    private String tranMethodParams(String method, JSONObject params) {
    	 String methodParams = method.substring(method.indexOf("(")+1, method.indexOf(")"));
    	 if(StringUtils.isNotEmpty(methodParams)){
    		 methodParams= methodParams.replaceAll("'","").replaceAll("\"", "");
    		 String[] mParamAry = methodParams.split(",");
    		 for(int j=0; j<mParamAry.length; j++) {
    			 params.put("param["+j+"]", mParamAry[j]);
    		 }
    	 }
    	 method = method.substring(0, method.indexOf("("));
	     return method;
    } 
	
	
	private EasyResult notAuth(String msg,Types type){
		CoreLogger.getMarsReq().warn(msg);
		
		EasyResult result = new EasyResult();
		result.setErrorCode(403);
		result.addFail(msg);
		if(type==Types.LIST){
			result.setData(new ArrayList<>());
		}else if(type==Types.RECORD){
			result.setData(new ArrayList<>());
		}else if(type==Types.TEXT){
			result.setData("");
		}else{
			result.setData(new JSONObject());
		}
		return result;
	}
	
	
	
	/**
	 * 执行动态方法调用
	 * @param methodName 要执行的方法名
	 * @return 返回结果
	 * @throws Exception
	 */
	private Object invokeMethod(Class<?> clazz,String methodName,Types type,JSONObject params,HttpServletRequest request,HttpServletResponse response) throws Exception{
		Object obj = clazz.newInstance();
		Method method = null;

		if (obj instanceof DaoContext) {
			((DaoContext) obj).init(request,response,type,params);
		}
		method = clazz.getDeclaredMethod(methodName);
		
		PreAuthorize classAuth = clazz.getAnnotation(PreAuthorize.class);
		PreAuthorize annotation = method.getAnnotation(PreAuthorize.class);
		if(classAuth!=null) {
		   boolean bl  = verifyAuth(request,classAuth);
		   if(!bl) {
			  return notAuth(classAuth.msg(),type);
		   }
		   if(annotation!=null) {
			   bl  = verifyAuth(request,annotation);
		   }
		   if(!bl) {
				return notAuth(annotation.msg(),type);
			}
		}else if(annotation!=null) {
			boolean bl  = verifyAuth(request,annotation);
			if(!bl) {
				return notAuth(annotation.msg(),type);
			}
		}
		
		return method.invoke(obj);

	}
	
	/**
	 * 获得当前登录用户的UserPrincipal
	 * @return
	 */
	protected UserPrincipal  getUserPrincipal(HttpServletRequest request){
		return PrincipalConvert.getUserPrincipal(request);
	}
	
	
	private boolean verifyAuth(HttpServletRequest request,PreAuthorize annotation) {
		String[] resIds = annotation.resId();
		String[] roleIds = annotation.roleId();
		ResIdCheckType checkType = annotation.checkType();
		UserPrincipal principal = getUserPrincipal(request);
		boolean hasAuth = false;
		
		if(roleIds.length>0) {
			for(String roleId:roleIds) {
				boolean hasResAuth = principal.isRole(roleId);
				if(checkType==ResIdCheckType.AND) {
					if(!hasResAuth) {
						hasAuth =  false;
						break;
					}
				}else {
					if(hasResAuth) {
						hasAuth =  true;
						break;
					}
				}
			}
			return hasAuth;
		}
		
		for(String resId:resIds) {
			boolean hasResAuth = principal.isResource(resId);
			if(checkType==ResIdCheckType.AND) {
				if(!hasResAuth) {
					hasAuth =  false;
					break;
				}
			}else {
				if(hasResAuth) {
					hasAuth =  true;
					break;
				}
			}
		}
		return hasAuth;
	}

	
}
