package org.easitline.common.core.web.websecurity;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.common.core.web.render.Render;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

public class XssFilter  implements Filter{

	FilterConfig filterConfig = null;  
	
	
	@Override
	public void destroy() {
		 this.filterConfig = null;  
	}

	@Override
	public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain chain) throws IOException, ServletException{
		 HttpServletRequest request = (HttpServletRequest)servletRequest;  
		 HttpServletResponse response = (HttpServletResponse)servletResponse; 
		 String filter = request.getParameter("filter");
		 String filterHeader = request.getHeader("filter");
		 if("false".equals(filter)||"false".equals(filterHeader)){
			 chain.doFilter(servletRequest,servletResponse);
			 return;
		 }
		 response.addHeader("x-frame-options","SAMEORIGIN"); 
		 response.addHeader("X-Content-Type-Options","nosniff"); 
		 response.addHeader("X-XSS-Protection","1; mode=block"); 
		 String host=request.getHeader("host");
		 String referer=request.getHeader("Referer");
		 String hostBlankList=ServerContext.getProperties("hostBlankList", "");//白名单
		 
	     boolean filterHtmlTag = false;
	     if("true".equals(filterConfig.getInitParameter("filterHtmlTag"))) {
		    filterHtmlTag = true;
	     }else {
		    filterHtmlTag = false;
	     }
	     String urlRules = filterConfig.getInitParameter("urlRules");
		   
		 if(StringUtils.isBlank(host)){
			 Render.renderHtml(request, response, "host deny acccess.");
			 return;
		 }
		 
		 if(StringUtils.notBlank(hostBlankList)){
			//host 攻击
			 if(hostBlankList.indexOf(host)==-1){
				 CoreLogger.getPlatform().warn(WebKit.getIP(request)+"[host deny access tips]->"+host);  
				 Render.renderHtml(request, response, format(host) +" host deny");
				 return;
			 }
			 //CSRF攻击
			 
			 if(StringUtils.notBlank(referer)){
				 String[] hosts=hostBlankList.split(",");
				 boolean flag = false;
				 for(String str:hosts){
					 if(referer.indexOf(str)>-1){
						 flag=true;
						 break;
					 }
				 }
				 if(!flag){
					 CoreLogger.getPlatform().warn(WebKit.getIP(request)+"[souce deny access tips]->"+referer);  
					 Render.renderHtml(request, response, format(referer) + " souce deny");
					 return;
				 }
			 }else if("".equals(referer)){
				 CoreLogger.getPlatform().warn(WebKit.getIP(request)+"[souce deny access tips]");  
				 Render.renderHtml(request, response," souce deny");
				 return;
			 }

		 }
		 String param = request.getQueryString();
		 if(!checkValidParam(param,urlRules)){
			 CoreLogger.getPlatform().warn(WebKit.getIP(request)+"["+param+"][invalid access!]");  
			 Render.renderHtml(request, response,"invalid access!");
			 return;
		 }
		 chain.doFilter(new XssHttpServletRequest((HttpServletRequest) servletRequest,urlRules,filterHtmlTag), response);  
	}
	
	private String format(String value) {
	     return StringUtils.replaceEach(value,new String[]  {"alert","onload","onerror","<",">"},new String[]{"","","","&lt;","&gt;"});
	}
	
	private static  boolean checkValidParam(String paramValue,String urlRules){
		if(StringUtils.isBlank(paramValue))  return true;
		paramValue = paramValue.toLowerCase();
		
		if(paramValue.indexOf("onmouse")>-1) {return false;}
//		if(paramValue.indexOf("<")>0 && paramValue.indexOf(">")>0) return false;
		if(StringUtils.isBlank(urlRules)) {
			urlRules = "<,>,alert,eval(,confirm,window,onclick,onfocus,prompt,unction,fromCharCode";
		}
		String[] array = urlRules.split(",");
		for(String val:array) {
			if(paramValue.indexOf(val)>-1) {
				 CoreLogger.getPlatform().warn("xss攻击字符已被拦截，paramValue:"+paramValue+"> is xss content.");  
				return false;
			}
		}
		return true;
	}
	 
	@Override
	public void init(FilterConfig filterConfig) throws ServletException {
		   this.filterConfig = filterConfig;  
	}

}
