package org.easitline.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
/**
  *  操作权限拦截
  *   先校验roleId,如果不存再校验resId
   * 默认满足一个id就通过验证
 * <AUTHOR>
 *
 */
@Target({ElementType.METHOD,ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface PreAuthorize {
	
	public String[] resId() default {};
	
	public String[] roleId()  default {};
	
	public ResIdCheckType checkType() default ResIdCheckType.OR;
	
	public String tag() default "";
	
	public String msg() default "无权访问";
	
	public String objName() default "";
	
	public enum ResIdCheckType {
		AND,
		OR
	}
	

}
