package org.easitline.common.core.sso;

import java.security.Principal;

import javax.servlet.http.HttpServletRequest;

import org.easitline.common.core.context.ServerContext;

public class PrincipalConvert {

	public static UserPrincipal  getUserPrincipal(HttpServletRequest request){
		//如果非tomcat模式，需要根据当前的UserPrincipal来创建
		if(!ServerContext.isTomcat()) {
			Principal principal = request.getUserPrincipal();
			if(principal == null) {
				return null;
			}
			if(principal instanceof UserPrincipal) {
				return (UserPrincipal)principal;
			} 
			return UserPrincipalFactory.getUserPrincipal(principal);
		}
		
		return  (UserPrincipal)request.getUserPrincipal();
//		UserPrincipal userPrincipal =  
//		if(userPrincipal == null){
//			userPrincipal = (UserPrincipal)request.getSession().getAttribute("G_User_Principal");
//		}
//		if(userPrincipal == null) {
//			userPrincipal = new DefaultUserPrincipal();
//		}
//		return userPrincipal;
	}
	
}
