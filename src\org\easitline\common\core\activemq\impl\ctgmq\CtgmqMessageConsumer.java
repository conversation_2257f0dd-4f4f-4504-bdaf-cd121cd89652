package org.easitline.common.core.activemq.impl.ctgmq;

import java.util.List;


import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageConsumer;
import javax.jms.MessageListener;

import org.easitline.common.core.activemq.BrokerContext;
import org.easitline.common.core.activemq.impl.ObjectMessageImpl;
import org.easitline.common.core.activemq.log.ActivieMQLogger;

import com.ctg.mq.api.IMQPullConsumer;
import com.ctg.mq.api.bean.ConsumeStatus;
import com.ctg.mq.api.bean.MQResult;
import com.ctg.mq.api.exception.MQAckException;
import com.ctg.mq.api.exception.MQException;

public class CtgmqMessageConsumer implements MessageConsumer  {
	
	private IMQPullConsumer consumer;
	
	private BrokerContext context;
	
	public static final int CONSUMER_TYPE_QUEUE = 1;
	
	public static final int CONSUMER_TYPE_TOPIC = 2;
	
	
	private int consumerType;
	
	public CtgmqMessageConsumer(BrokerContext context,IMQPullConsumer consumer,int consumerType) {
		this.context = context;
		this.consumer = consumer;
		this.consumerType = consumerType;
	}
	
//	/**
//	 * 这里用的是push模式，按CTGMQ的建议，采用push，就是通过监听，服务端往客户端推送消息。
//	 * @throws MQException
//	 */
//	public void listenQueue() throws MQException{
//		 consumer.listenQueue(this.context.getBrokerName(), null, new ConsumerQueueListener() {
//	            @Override
//	            public ConsumerQueueStatus onMessage(List<MQResult> mqResultList) {
//	                //mqResultList 默认为1，可通过批量消费数量设置
//	            	try {
//	            		for(MQResult result : mqResultList) {
//		                	ActivieMQLogger.getLogger().info("CtgmqMessageConsumer.receive("+context.getBrokerName()+") << "+result);
//		                	if(messageQueue.size()>10000){
//		                		ActivieMQLogger.getLogger().warn("消息队列["+context.getBrokerName()+"]缓存需要处理的消息数超过10000，请检查程序处理逻辑！");
//		                		messageQueue.poll();  
//		                	}
//		                	messageQueue.add(result);
//		                }
//					} catch (Exception ex) {
//						ActivieMQLogger.getLogger().error("ConsumerQueueListener("+context.getBrokerName()+") run error,cause:"+ex.getMessage(),ex);
//					}
//	                return ConsumerQueueStatus.SUCCESS;//对消息批量确认(成功),这个很重要，告诉服务端消息已经被消费了。
//	                //return ConsumerTopicStatus.RECONSUME_LATER;//对消息批量确认(失败)
//	            }
//	        });
//	}
//	
//	
//	public void listenTopic() throws MQException{
//		 consumer.listenTopic(this.context.getBrokerName(), null, new ConsumerTopicListener() {
//	            @Override
//	            public ConsumerTopicStatus  onMessage(List<MQResult> mqResultList) {
//	                //mqResultList 默认为1，可通过批量消费数量设置
//	                for(MQResult result : mqResultList) {
//	                	if(messageQueue.size()>10000){
//	                		ActivieMQLogger.getLogger().warn("消息队列["+context.getBrokerName()+"]缓存需要处理的消息数超过10000，请检查程序处理逻辑！");
//	                		messageQueue.poll();  
//	                	}
//	                	ActivieMQLogger.getLogger().info("CtgmqMessageConsumer.receive("+context.getBrokerName()+") << "+result);
//	                	messageQueue.add(result);
//	                }
//	                return ConsumerTopicStatus.CONSUME_SUCCESS;//对消息批量确认(成功)
//	                //return ConsumerTopicStatus.RECONSUME_LATER;//对消息批量确认(失败)
//	            }
//	        });
//	}

	@Override
	public void close() throws JMSException {
//		try {
//			this.consumer.close();
//
//		} catch (MQException ex) {
//			ActivieMQLogger.getLogger().error(ex,ex);
//			throw new JMSException(ex.getMessage());
//		}
	}

	@Override
	public MessageListener getMessageListener() throws JMSException {
		return null;
	}

	@Override
	public String getMessageSelector() throws JMSException {
		return null;
	}

	@Override
	public Message receive() throws JMSException {
		return this.receive(10*1000);
		
	}

//	@Override
//	public Message receive(long timeout) throws JMSException {
//		MQResult result  = null;
//		try {
//			result = messageQueue.poll(timeout, TimeUnit.MILLISECONDS);
//		} catch (InterruptedException ex) {
//			throw new JMSException(ex.getMessage());
//		}
//		
//		if(result!=null){
//			 return this.getObjectMessage(result);
//		}
//		return null;
//	}
//	
	
	@Override
	public Message receive(long timeout) throws JMSException {
		try {
			return this.runReceive(timeout);
		} catch (JMSException ex) {
			ActivieMQLogger.getLogger().error("队列["+this.context.getBrokerName()+"]消息接收异常，原因："+ex.getMessage());
			ActivieMQLogger.getLogger().error(ex,ex);
			try {
				Thread.sleep(3000);
			} catch (Exception ex1) {
			}
			throw ex;
		}
	}
	
	public Message runReceive(long timeout) throws JMSException {
		long timer = System.currentTimeMillis();
		List<MQResult> mqResultList = null;
		try {
			//如果是队列的模式，则按队列模式来拉取消息
			if(consumerType == CONSUMER_TYPE_QUEUE){
				mqResultList = consumer.consumeMessagesByQueue(
					context.getBrokerName(),//topic名称
			        null,   //过滤条件 
			        1,   //一次拉取数量，最大32条
			        (int)timeout //拉取超时时间
			        );
			}else{
				mqResultList = consumer.consumeMessagesByTopic(
						context.getBrokerName(),//topic名称
						null,   //过滤条件 
				        1,   //一次拉取数量，最大32条
				        (int)timeout //拉取超时时间
				        );
			}
//			mqResultList = consumer.consumeMessagesByTopic(
//					context.getBrokerName(),//topic名称
//					null,   //过滤条件 
//			        1,   //一次拉取数量，最大32条
//			        (int)timeout //拉取超时时间
//			        );
		} catch (MQException ex) {
			ActivieMQLogger.getLogger().error("CtgmqMessageConsumer.receive("+context.getBrokerName()+")->timeout:"+timeout+","+ex.getMessage(),ex);
			try {
				Thread.sleep(3000);
			} catch (Exception e) {
				// TODO: handle exception
			}
			throw new JMSException(ex.getMessage());
		}
		
		if(mqResultList.size()>0){
			MQResult result = mqResultList.get(0);
			ObjectMessageImpl	objectMessage =  this.getObjectMessage(result);
            try {
				consumer.ackMessage(result, ConsumeStatus.CONSUME_SUCCESS);
			} catch (MQAckException ex) {
				ActivieMQLogger.getLogger().error(ex,ex);
			}
			ActivieMQLogger.getLogger().info("["+Thread.currentThread().getId()+"]CtgmqMessageConsumer.receive("+context.getBrokerName()+") << "+new String(result.getMessage().getBody()));
			return objectMessage;
		}else{
			ActivieMQLogger.getLogger().info("["+Thread.currentThread().getId()+"]CtgmqMessageConsumer.receive("+context.getBrokerName()+") no message ... ");
			timer = System.currentTimeMillis() - timer;
			//这里要防止设置拉取时间不生效的问题。
			if(timer<timeout){
				try {
					Thread.sleep(1000);
				} catch (Exception ex) {
					// TODO: handle exception
				}
			}
		}
		return null;
		
	}
	 

	@Override
	public Message receiveNoWait() throws JMSException {
		return this.receive(5000);
	}

	@Override
	public void setMessageListener(MessageListener arg0) throws JMSException {
	}
	
	private  ObjectMessageImpl  getObjectMessage(MQResult message){
		String body = new String(message.getMessage().getBody());
		ObjectMessageImpl objectMessage = new ObjectMessageImpl(body);
		return objectMessage;
	}

	
	@Override
	public String toString() {
		return "CtgmqMessageConsumer("+context.getBrokerName()+","+context.getConnector()+"),Consumer对象名："+this.consumer;
	}

}
