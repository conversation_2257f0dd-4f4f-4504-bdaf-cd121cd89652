package org.easitline.common.core.activemq.impl.ctgmq;

import javax.jms.Destination;


import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageProducer;
import javax.jms.ObjectMessage;

import com.ctg.mq.api.IMQProducer;
import com.ctg.mq.api.bean.MQMessage;
import com.ctg.mq.api.bean.MQSendResult;
import com.ctg.mq.api.exception.MQException;

import org.easitline.common.core.activemq.BrokerContext;
import org.easitline.common.core.activemq.log.ActivieMQLogger;

public class CtgmqMessageProducer implements MessageProducer  {
	
	
	private IMQProducer producer;
	
	private BrokerContext context;
	
	public CtgmqMessageProducer(BrokerContext context,IMQProducer producer){
		this.context = context;
		this.producer = producer;
	}

	@Override
	public void close() throws JMSException {
		try {
			this.producer.close();
		} catch (MQException ex) {
			ActivieMQLogger.getLogger().error(ex,ex);
			throw new JMSException(ex.getMessage());
		}
	}

	@Override
	public int getDeliveryMode() throws JMSException {
		return 0;
	}

	@Override
	public Destination getDestination() throws JMSException {
		return null;
	}

	@Override
	public boolean getDisableMessageID() throws JMSException {
		return false;
	}

	@Override
	public boolean getDisableMessageTimestamp() throws JMSException {
		return false;
	}

	@Override
	public int getPriority() throws JMSException {
		return 0;
	}

	@Override
	public long getTimeToLive() throws JMSException {
		return 0;
	}

	@Override
	public void send(Message message) throws JMSException {
		ObjectMessage objectMessage =  (ObjectMessage)message;
		String messageText = objectMessage.getObject().toString();
		
		ActivieMQLogger.getLogger().info("CtgmqMessageProducer.send("+context.getBrokerName()+") >> "+messageText);
		
		MQMessage msg = new MQMessage(
                this.context.getBrokerName(),
                null, // 消息标签
				messageText.getBytes()// 消息内容
        );
        
		try {
			MQSendResult sendResult = producer.send(msg);
			ActivieMQLogger.getLogger().info("CtgmqMessageProducer.send("+context.getBrokerName()+") -> "+sendResult);
		} catch (Exception ex) {
			try {
				Thread.sleep(3000);
			} catch (Exception e) {
				// TODO: handle exception
			}
			throw new JMSException(ex.getMessage());
		}
		// 发送消息并返回结果
	}

	@Override
	public void send(Destination arg0, Message arg1) throws JMSException {
		// TODO Auto-generated method stub

	}

	@Override
	public void send(Message arg0, int arg1, int arg2, long arg3) throws JMSException {
		// TODO Auto-generated method stub

	}

	@Override
	public void send(Destination arg0, Message arg1, int arg2, int arg3, long arg4) throws JMSException {
		// TODO Auto-generated method stub

	}

	@Override
	public void setDeliveryMode(int arg0) throws JMSException {
		// TODO Auto-generated method stub

	}

	@Override
	public void setDisableMessageID(boolean arg0) throws JMSException {
		// TODO Auto-generated method stub

	}

	@Override
	public void setDisableMessageTimestamp(boolean arg0) throws JMSException {
		// TODO Auto-generated method stub

	}

	@Override
	public void setPriority(int arg0) throws JMSException {
		// TODO Auto-generated method stub

	}

	@Override
	public void setTimeToLive(long arg0) throws JMSException {

	}
	
	@Override
	public String toString() {
		return "CtgmqMessageProducer("+context.getBrokerName()+","+context.getConnector()+"),Producer对象名："+this.producer;
	}

}
