package org.easitline.common.core.framework.vo;

import java.sql.SQLException;
import java.util.List;

import com.alibaba.fastjson.JSONObject;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.framework.vo.rowmapper.DepartmentRowMapper;
import org.easitline.common.core.framework.vo.rowmapper.LabelRowMapper;
import org.easitline.common.core.framework.vo.rowmapper.RoleRowMapper;
import org.easitline.common.core.log.impl.CoreLogger;

/**
 * 用户模型
 * <AUTHOR>
 *
 */
public class User {
	
	private String userId;
	private String username;
	private String nikeName;
	private int state;
	private String depts;
	private String roles;
	private String mobile;
	private String idcard;
	private String sex;
	private String email;
	private String picUrl;
	private String ipRange;
	private String plaform;
	private String description;
	private String userAcct;
	private String imei;
	private String data1;
	private String data2;
	private String data3;
	private String data4;
	private String data5;
	private String data6;
	private String data7;
	private String data8;
	private String data9;
	private String data10;
	
	
	public User(){
		
	}
    
    /**
     * 获得标签信息
     * @param labelId
     * @return
     */
    public List<Label> getLabelList(){
    	String sql = "SELECT T1.* FROM EASI_LABEL T1 , EASI_LABEL_USER T2 WHERE T1.LABEL_ID = T2.LABEL_ID AND T2.USER_ID = ?";
    	try {
			return ServerContext.getAdminQuery().queryForList(sql,new Object[]{this.userId},new LabelRowMapper());
		} catch (SQLException ex) {
			CoreLogger.getLogger().error("Core-framework->获取用户的标签列表失败，原因："+ex.getMessage(),ex);
		}
    	return null;
    }
    
    
    
	/**
	 * 获得用户的角色列表
	 * @param userId  用户ID
	 * @return
	 */
    public List<Role> getRoleList(){
    	String sql = "SELECT T1.* FROM EASI_ROLE T1 , EASI_ROLE_USER T2 WHERE T1.ROLE_ID = T2.ROLE_ID AND T2.USER_ID = ?  ";
    	try {
			return ServerContext.getAdminQuery().queryForList(sql,new Object[]{this.userId},new RoleRowMapper());
		} catch (SQLException ex) {
			CoreLogger.getLogger().error("Core-framework->获取用户的角色列表失败，原因："+ex.getMessage(),ex);
		}
    	return null;
    }
    
    
	/**
	 * 获得用户的所在的部门
	 * @param userId  用户ID
	 * @return
	 */
    public Department getDepartment(){
    	String sql = "SELECT T1.*,t3.NODE_TYPE  FROM EASI_DEPT T1  left join EASI_DEPT_TYPE t3 on t1.DEPT_TYPE = t3.DEPT_TYPE , EASI_DEPT_USER T2 WHERE T1.DEPT_ID = T2.DEPT_ID AND T2.USER_ID = ?  ";
    	try {
			return ServerContext.getAdminQuery().queryForRow(sql,new Object[]{this.userId},new DepartmentRowMapper());
		} catch (SQLException ex) {
			CoreLogger.getLogger().error("Core-framework->获取用户的部门信息失败，原因："+ex.getMessage(),ex);
		}
    	return null;
    }
    
    /**
     * 获得终端 imei
     * @return
     */
	public String getImei() {
		return imei;
	}

	public void setImei(String imei) {
		this.imei = imei;
	}

	/**
	 * 获得用户的登录账号
	 * @return
	 */
	public String getUserAcct() {
		return userAcct;
	}

	public void setUserAcct(String userAcct) {
		this.userAcct = userAcct;
	}
	
	/**
	 * 获得用户描述
	 * @return
	 */
	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}
	
	/**
	 * 获得用户ID
	 * @return
	 */
	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}
	
	/**
	 * 获得用户名
	 * @return
	 */
	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	/**
	 * 获得用户状态
	 * @return
	 */
	public int getState() {
		return state;
	}
	
	public String getStateDesc() {
		if(this.state ==0 ) return "正常";
		if(this.state ==1 ) return "暂停 ";
		if(this.state ==2 ) return "锁定";
		return "";
	}

	public void setState(int state) {
		this.state = state;
	}
	/**
	 * 获得用户所在的部门
	 * @return
	 */
	public String getDepts() {
		return depts;
	}

	public void setDepts(String depts) {
		this.depts = depts;
	}
	
	/**
	 * 获得用户角色
	 * @return
	 */
	public String getRoles() {
		return roles;
	}

	public void setRoles(String roles) {
		this.roles = roles;
	}
	
	/**
	 * 获得移动电话
	 * @return
	 */
	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	
	/**
	 * 获得身份证
	 * @return
	 */
	public String getIdcard() {
		return idcard;
	}

	public void setIdcard(String idcard) {
		this.idcard = idcard;
	}
	
	
	public String getNikeName() {
		return nikeName;
	}

	public void setNikeName(String nikeName) {
		this.nikeName = nikeName;
	}

	/**
	 * 获得性别
	 * @return
	 */
	public String getSex() {
		return sex;
	}

	public void setSex(String sex) {
		this.sex = sex;
	}

	/**
	 * 获得Email
	 * @return
	 */
	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}
	
	/**
	 * 获得登录的IP范围
	 * @return
	 */
	public String getIpRange() {
		return ipRange;
	}

	public void setIpRange(String ipRange) {
		this.ipRange = ipRange;
	}
	
	/**
	 * 获得登录平台配置
	 * @return
	 */
	public String getPlaform() {
		if(plaform ==null || "".equals(plaform)) return "{}";
		return plaform;
	}

	public void setPlaform(String plaform) {
		this.plaform = plaform;
	}
	
	/**
	 * 是否允许登录PC平台，1：代表是  0：代表否
	 * @return
	 */
	public int getPlaformPC(){
		JSONObject jsonObject =  JSONObject.parseObject(this.getPlaform());
		try {
			return jsonObject.getIntValue("pc");
		} catch (Exception e) {
		}
		return 0;
	}
	
	public String getPicUrl() {
		return picUrl;
	}

	public void setPicUrl(String picUrl) {
		this.picUrl = picUrl;
	}

	/**
	 * 是否允许登录移动平台，1：代表是  0：代表否
	 * @return
	 */
	public int getPlaformMobile(){
		JSONObject jsonObject =  JSONObject.parseObject(this.getPlaform());
		try {
			return jsonObject.getIntValue("mobile");
		} catch (Exception e) {
		}
		return 0;
	}

	public String getData1() {
		return data1;
	}

	public void setData1(String data1) {
		this.data1 = data1;
	}

	public String getData2() {
		return data2;
	}

	public void setData2(String data2) {
		this.data2 = data2;
	}

	public String getData3() {
		return data3;
	}

	public void setData3(String data3) {
		this.data3 = data3;
	}

	public String getData4() {
		return data4;
	}

	public void setData4(String data4) {
		this.data4 = data4;
	}

	public String getData5() {
		return data5;
	}

	public void setData5(String data5) {
		this.data5 = data5;
	}

	public String getData6() {
		return data6;
	}

	public void setData6(String data6) {
		this.data6 = data6;
	}

	public String getData7() {
		return data7;
	}

	public void setData7(String data7) {
		this.data7 = data7;
	}

	public String getData8() {
		return data8;
	}

	public void setData8(String data8) {
		this.data8 = data8;
	}

	public String getData9() {
		return data9;
	}

	public void setData9(String data9) {
		this.data9 = data9;
	}

	public String getData10() {
		return data10;
	}

	public void setData10(String data10) {
		this.data10 = data10;
	}

}
