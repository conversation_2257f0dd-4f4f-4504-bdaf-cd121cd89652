package org.easitline.common.core.framework.vo;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.framework.vo.rowmapper.UserRowMapper;
import org.easitline.common.core.log.impl.CoreLogger;

/**
 * 角色模型
 * <AUTHOR>
 *
 */
public class Role {

	private String roleId; 
	private String appId;  
	private String roleName;
	private int roleType;
	private String roleDesc;
	
	
	
	/**
	 * 获得标签用户
	 * @return List<User>  标签的用户列表
	 */
	public List<User> getUsers(){
		String sql = "SELECT * FROM V_EASI_ROLE_USER  where  STATE <> 9 AND ROLE_ID = ?";
    	try {
			return ServerContext.getAdminQuery().queryForList(sql,new Object[]{this.getRoleId()},new UserRowMapper());
		} catch (SQLException ex) {
			CoreLogger.getLogger().error("Core-framework->获取标签用户信息失败，原因："+ex.getMessage(),ex);
		}
    	return null;
	}
	
	
	/**
	 * 获得角色ID
	 * @return  角色ID
	 */
	public String getRoleId() {
		return roleId;
	}
	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}
	
	/**
	 * 获得角色所属应用ID
	 * @return 
	 */
	public String getAppId() {
		return appId;
	}
	
	public void setAppId(String appId) {
		this.appId = appId;
	}
	
	/**
	 * 获得角色名称
	 * @return  角色名称
	 */
	public String getRoleName() {
		return roleName;
	}
	public void setRoleName(String roleName) {
		this.roleName = roleName;
	}
	
	/**
	 * 获得角色类型
	 * @return
	 */
	public int getRoleType() {
		return roleType;
	}
	public void setRoleType(int roleType) {
		this.roleType = roleType;
	}
	
	/**
	 * 获得角色描述
	 * @return
	 */
	public String getRoleDesc() {
		return roleDesc;
	}
	public void setRoleDesc(String roleDesc) {
		this.roleDesc = roleDesc;
	} 
	

	
}
