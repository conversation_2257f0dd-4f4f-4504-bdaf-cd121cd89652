package org.easitline.common.core.log.impl;


import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Enumeration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.log4j.Appender;
import org.apache.log4j.Level;
import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.apache.log4j.PatternLayout;
import org.apache.log4j.RollingFileAppender;
import org.apache.log4j.spi.LoggingEvent;
import org.easitline.common.core.Globals;
import org.easitline.common.core.context.ServerContext;
/**
 * Log4j2实现类 - 通过log4j-1.2-api桥接包实现
 * 完全使用Log4j 1.x API，桥接包会自动转发到Log4j 2.x
 *
 * 动态日志文件支持：
 * - LogEngine.getLogger("mylog") -> 创建 mylog.log
 * - LogEngine.getLogger("userlog") -> 创建 userlog.log
 * - 每个logger都有独立的文件配置
 *
 * 异步功能：
 * - 自动启用：类加载时自动设置Log4jContextSelector系统属性
 * - 无需手动配置：用户无需添加JVM参数
 * - 高性能：所有动态创建的logger自动获得异步功能
 * - 兼容性：如果已有配置则保持不变
 *
 * 特性：
 * - 自动异步：无需任何配置即可获得异步性能
 * - 动态文件：支持任意数量的日志文件创建
 * - catalina.out：通过日志传播自动支持控制台输出
 * - API兼容：完全兼容现有Log4j 1.x API调用
 * - 向前兼容：可在删除log4j-1.2.17.jar后正常工作
 */
public class Log4j2Impl {

	private static Map<String, Logger> logs = new ConcurrentHashMap<String, Logger>();

    static {
        System.setProperty("Log4jContextSelector","org.apache.logging.log4j.core.async.AsyncLoggerContextSelector");
        System.setProperty("log4j2.enableThreadLocals","true");
        // 启用位置信息，虽然会影响性能但能显示类名和行号
        System.setProperty("log4j2.includeLocation","true");
        // 对于异步日志，强制包含位置信息
        System.setProperty("AsyncLogger.includeLocation","true");
    }

    private Log4j2Impl() {

    }
    
    public static void remove(String logName) {
        Logger logger = logs.remove(logName);
        if (logger != null) {
            try {
                // 正确关闭所有appender
                Enumeration<?> appenders = logger.getAllAppenders();
                while (appenders.hasMoreElements()) {
                    Appender appender = (Appender) appenders.nextElement();
                    try {
                        if (appender instanceof RollingFileAppender) {
                            appender.close(); // 关闭文件句柄
                        }
                    } catch (Exception e) {
                        System.err.println("关闭appender失败 [" + logName + "]: " + e.getMessage());
                        e.printStackTrace();
                    }
                }
                logger.removeAllAppenders();
                logger.setAdditivity(false);
                try {
                    Thread.sleep(50);
                } catch (InterruptedException ignored) {}
            } catch (Exception e) {
                System.err.println("清理logger失败 [" + logName + "]: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    public static synchronized void removeAll() {
        try {
            String[] logNames = logs.keySet().toArray(new String[0]);
            for (String logName : logNames) {
                remove(logName);
            }
            System.out.println("所有Log4j2日志实例已清理完成，共清理: " + logNames.length + " 个");
        } catch (Exception e) {
            System.err.println("清理所有日志实例时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public static Logger getLogger(String logName) {
        return getLogger("undefine", logName);
    }

    public static Logger getLogger(String appName, String logName) {
        Logger logger = logs.get(logName);
        if (logger == null) {
            synchronized (Log4j2Impl.class) {
                logger = logs.get(logName);
                if (logger == null) {
                    logger = createLogger(appName, logName);
                    logs.put(logName, logger);
                }
            }
        }
        return logger;
    }

    private static Logger createLogger(String appName, String logName) {
        try {
            if (logName == null || logName.trim().isEmpty()) {
                logName = "easitline-out";
            }

            // 完全使用Log4j 1.x API（通过桥接包）
            Logger logger = LogManager.getLogger(logName);

            // 创建RollingFileAppender（异步功能通过Log4j2配置实现）
            RollingFileAppender fileAppender = new RollingFileAppender();
            fileAppender.setName(logName + "-file");
            fileAppender.setFile(getLoggerPath(logName));
            fileAppender.setMaxBackupIndex(ServerContext.getLogBakCount());
            fileAppender.setMaximumFileSize(ServerContext.getLogBakSize());
            fileAppender.setEncoding("UTF-8");

            // 设置Layout - 使用自定义Layout确保显示位置信息
            FileLayout layout = new FileLayout(appName, logName);
            fileAppender.setLayout(layout);

            // 激活fileAppender
            fileAppender.activateOptions();

            // 配置logger
            logger.removeAllAppenders();
            logger.addAppender(fileAppender);
            logger.setLevel(getLogLevel());
            // 移除logger.setAdditivity(false)，允许日志传播到root logger，从而输出到catalina.out

            // 异步功能已通过静态初始化块自动启用，无需手动配置
            // Log4j 2.x的AsyncLoggerContextSelector已在类加载时设置
            // 所有通过此方法创建的logger都自动获得异步性能
            

            System.out.println("创建Log4j2异步日志实例 > " + logName);

            return logger;
        } catch (SecurityException e) {
            System.err.println("日志文件权限不足: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("创建日志记录器时出错: " + e.getMessage());
            e.printStackTrace();
        }
        return LogManager.getRootLogger();
    }


    private static String getLoggerPath(String logName) {
        String path = appLogDir() + File.separator + logName + ".log";
        return path;
    }

    private static String appLogDir() {
        return System.getProperty("appLogDir", Globals.LOG_DIR);
    }
    
    public static Level getLogLevel(){
		String str = ServerContext.getLogLevelStr();
		Level level=Level.ALL;
		switch (str) {
		case "INFO":
			level=Level.INFO;
			break;
		case "ERROR":
			level=Level.ERROR;
			break;
		case "WARN":
			level=Level.WARN;
			break;
		default:
			level=Level.DEBUG;
			break;
		}
		return level;
	}
    
    public static synchronized void reloadConfig() {
        try {
            // 重新设置所有logger的级别
            Level newLevel = getLogLevel();
            for (Logger logger : logs.values()) {
                logger.setLevel(newLevel);
            }
            System.out.println("Log4j2Impl配置已重新加载，新日志级别: " + newLevel);
        } catch (Exception e) {
            System.err.println("重新加载Log4j2Impl配置失败: " + e.getMessage());
        }
    }

    // 自定义Layout类，用于显示位置信息
    static class FileLayout extends org.apache.log4j.Layout {
        private String appName;
        private String logName;
        private static final ThreadLocal<SimpleDateFormat> dateFormatThreadLocal =
            new ThreadLocal<SimpleDateFormat>() {
                @Override
                protected SimpleDateFormat initialValue() {
                    return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
                }
            };

        public FileLayout(String appName, String logName) {
            this.appName = appName;
            this.logName = logName;
        }

        @Override
        public String format(LoggingEvent event) {
            Date date = new Date(event.timeStamp);
            String msg = event.getRenderedMessage();
            if (msg == null) msg = "";
            StringBuilder buf = new StringBuilder(msg.length() + 100);
            buf.append(dateFormatThreadLocal.get().format(date)).append(" ");
            buf.append("[").append(Thread.currentThread().getName()).append("] ");
            buf.append(event.getLevel()).append(" ");
            buf.append("[").append(this.appName).append(":").append(this.logName).append("] ");
            buf.append("- ").append(msg);
            if (event.getThrowableStrRep() != null) {
                buf.append("\r\n");
                String[] rows = event.getThrowableStrRep();
                for (String row : rows) {
                    buf.append(row).append("\r\n");
                }
            } else {
                // 添加位置信息，格式与Log4jImpl保持一致
                buf.append(" - ");
                buf.append(event.getLocationInformation().getClassName()).append(".");
                buf.append(event.getLocationInformation().getMethodName()).append("(");
                buf.append(event.getLocationInformation().getFileName()).append(":");
                buf.append(event.getLocationInformation().getLineNumber()).append(")");
                buf.append("\r\n");
            }
            return buf.toString();
        }

        @Override 
        public boolean ignoresThrowable() {
            return false;
        }

        public void activateOptions() {
            // 无需特殊激活操作
        }
    }

}
