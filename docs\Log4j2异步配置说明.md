# Log4j2异步配置说明

## 概述

Log4j2Impl支持动态日志文件创建和高性能异步日志功能，**无需任何手动配置**。

## 动态日志文件支持

```java
// 每个调用都会创建对应的日志文件
Logger logger1 = LogEngine.getLogger("mylog");      // 创建 mylog.log
Logger logger2 = LogEngine.getLogger("userlog");    // 创建 userlog.log
Logger logger3 = LogEngine.getLogger("orderlog");   // 创建 orderlog.log
```

## 异步功能启用

### ✅ 自动启用（默认方式）

**无需任何配置！** Log4j2Impl在类加载时自动启用异步功能：

```java
// 静态初始化块自动设置
System.setProperty("Log4jContextSelector",
    "org.apache.logging.log4j.core.async.AsyncLoggerContextSelector");
```

启动时会看到提示信息：
```
Log4j2Impl: 已自动启用异步日志功能
```

### 手动配置（可选）

如果需要自定义配置，可以在启动前设置JVM参数：

```bash
-DLog4jContextSelector=org.apache.logging.log4j.core.async.AsyncLoggerContextSelector
```

**注意**：如果检测到已有配置，会保持现有设置不变。

### 配置文件

已提供 `resource/log4j2.xml` 基础配置文件，包含：
- 控制台输出配置
- 动态文件说明
- 第三方库日志级别控制

## 性能优势

启用异步后的性能提升：
- **吞吐量**：比同步日志高10-20倍
- **延迟**：日志调用几乎不阻塞业务线程
- **内存**：使用环形缓冲区，内存效率高
- **CPU**：减少日志写入的CPU开销

## 配置验证

启动应用后，查看日志输出：

**1. 异步功能启用确认：**
```
Log4j2Impl: 已自动启用异步日志功能
```

**2. 日志实例创建确认：**
```
创建Log4j2异步日志实例 > mylog, 备份数 > 8, 备份大小 > 157286400 (异步功能已自动启用)
```

如果看到这些消息，说明Log4j2Impl异步功能正常工作。

## 注意事项

1. **异步模式下**，应用关闭时需要确保日志缓冲区被刷新
2. **catalina.out输出**：通过日志传播机制自动支持
3. **文件滚动**：支持按大小滚动，配置通过ServerContext获取
4. **编码格式**：统一使用UTF-8编码

## 故障排除

如果异步功能未生效：
1. 检查JVM参数是否正确设置
2. 确认log4j-core和log4j-api版本匹配
3. 查看启动日志是否有异步相关信息
