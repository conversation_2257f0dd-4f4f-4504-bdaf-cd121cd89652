# EasitLine Core 快速入门指南

## 概述

本指南帮助新开发者快速上手 EasitLine Core 框架，通过简单的示例快速掌握核心功能。

## 环境准备

### 1. 项目依赖
确保项目中包含以下依赖：
- easitline-core-3.4.jar
- 数据库驱动（MySQL、Oracle等）
- 缓存依赖（Redis、Memcache）

### 2. 配置文件
在 `resource` 目录下创建配置文件：

**log4j.properties**
```properties
log4j.rootLogger=INFO, stdout, file
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} [%p] %c{1} - %m%n
```

## 5分钟快速上手

### 第一步：创建基础Servlet

```java
package com.example.servlet;

import org.easitline.common.core.web.EasyBaseServlet;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.kit.RandomKit;
import com.alibaba.fastjson.JSONObject;

public class HelloServlet extends EasyBaseServlet {
    
    // 处理GET请求 - 查询数据
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) {
        try {
            // 获取查询对象
            EasyQuery query = getQuery();
            
            // 执行查询
            List<EasyRow> users = query.queryForList("SELECT * FROM users LIMIT 10");
            
            // 返回结果
            render(EasyResult.ok(users, "查询成功"));
            
        } catch (Exception e) {
            error("查询失败", e);
            render(EasyResult.error("查询失败"));
        }
    }
    
    // 处理POST请求 - 创建数据
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) {
        try {
            // 获取JSON参数
            JSONObject params = getJSONObject();
            String userName = params.getString("userName");
            String email = params.getString("email");
            
            // 创建记录
            EasyRecord user = new EasyRecord("users", "id");
            user.set("id", RandomKit.uuid());
            user.set("user_name", userName);
            user.set("email", email);
            user.set("create_time", new Date());
            
            // 保存到数据库
            EasyQuery query = getQuery();
            query.save(user);
            
            // 返回成功结果
            render(EasyResult.ok("用户创建成功"));
            
        } catch (Exception e) {
            error("创建用户失败", e);
            render(EasyResult.error("创建用户失败"));
        }
    }
    
    // 必须实现的抽象方法
    @Override
    protected String getAppName() {
        return "demo-app";
    }
    
    @Override
    protected String getLoggerName() {
        return "helloServlet";
    }
    
    @Override
    protected String getAppDatasourceName() {
        return "default-ds";
    }
    
    @Override
    protected String getResId() {
        return "hello.manage";
    }
}
```

### 第二步：配置web.xml

```xml
<servlet>
    <servlet-name>HelloServlet</servlet-name>
    <servlet-class>com.example.servlet.HelloServlet</servlet-class>
</servlet>
<servlet-mapping>
    <servlet-name>HelloServlet</servlet-name>
    <url-pattern>/api/hello</url-pattern>
</servlet-mapping>
```

### 第三步：测试接口

**查询数据（GET请求）**
```
GET http://localhost:8080/api/hello
```

**创建数据（POST请求）**
```
POST http://localhost:8080/api/hello
Content-Type: application/json

{
    "userName": "张三",
    "email": "<EMAIL>"
}
```

## 常用功能快速示例

### 1. 数据库操作

#### 简单查询
```java
EasyQuery query = getQuery();

// 查询列表
List<EasyRow> users = query.queryForList("SELECT * FROM users WHERE status = ?", 1);

// 查询单个对象
EasyRow user = query.queryForObject("SELECT * FROM users WHERE id = ?", userId);

// 查询数量
int count = query.queryForInt("SELECT COUNT(*) FROM users");
```

#### 增删改操作
```java
EasyQuery query = getQuery();

// 新增
EasyRecord newUser = new EasyRecord("users", "id");
newUser.set("id", RandomKit.uuid());
newUser.set("user_name", "李四");
query.save(newUser);

// 更新
EasyRecord updateUser = new EasyRecord("users", "id");
updateUser.set("id", userId);
updateUser.set("user_name", "李四修改");
query.update(updateUser);

// 删除
query.update("DELETE FROM users WHERE id = ?", userId);
```

#### 事务操作
```java
EasyQuery query = getQuery();
query.begin();

try {
    // 操作1
    query.save(record1);
    
    // 操作2
    query.update(record2);
    
    // 提交事务
    query.commit();
    
} catch (Exception e) {
    // 回滚事务
    query.rollback();
    throw e;
}
```

### 2. 缓存操作

```java
// 获取缓存对象
EasyCache cache = CacheManager.getCache();

// 存储数据（默认3小时）
cache.put("user:" + userId, userObject);

// 存储数据（指定1小时过期）
cache.put("user:" + userId, userObject, CacheTime.HOUR);

// 获取数据
User user = cache.get("user:" + userId);

// 删除缓存
cache.delete("user:" + userId);

// 清空所有缓存
cache.flushAll();
```

### 3. JSON处理

```java
// 获取请求参数
JSONObject params = getJSONObject();
String name = params.getString("name");
Integer age = params.getInteger("age");

// 获取指定模型的参数
JSONObject userParams = getJSONObject("user");

// 获取数组参数
JSONArray dataArray = getJSONArray();

// 返回JSON响应
render(EasyResult.ok(data));
```

### 4. 日志记录

```java
// 记录操作日志
OperatorLog log = new OperatorLog()
    .setUserId(getCurrentUserId())
    .setUserAcct(getCurrentUserAcct())
    .setModule("用户管理")
    .setOperation("创建用户");
log.save();

// 记录应用日志
ServerContext.addAppLog("demo-app", "用户登录成功");

// 记录调试日志
debug("处理用户请求: " + userId);
info("用户创建成功: " + userName);
error("处理失败", exception);
```

## 进阶功能

### 1. 创建DAO类

```java
public class UserDao extends DaoContext {
    
    public JSONObject getUserList(int pageNo, int pageSize) {
        EasyQuery query = getQuery();
        
        String sql = "SELECT * FROM users ORDER BY create_time DESC";
        List<EasyRow> users = query.queryForList(sql + " LIMIT ?, ?", 
            (pageNo - 1) * pageSize, pageSize);
        
        int total = query.queryForInt("SELECT COUNT(*) FROM users");
        
        JSONObject result = new JSONObject();
        result.put("data", users);
        result.put("total", total);
        result.put("pageNo", pageNo);
        result.put("pageSize", pageSize);
        
        return result;
    }
    
    public boolean createUser(JSONObject userInfo) {
        try {
            EasyQuery query = getQuery();
            
            EasyRecord user = new EasyRecord("users", "id");
            user.set("id", RandomKit.uuid());
            user.setColumns(userInfo);
            user.set("create_time", new Date());
            
            query.save(user);
            return true;
            
        } catch (Exception e) {
            error("创建用户失败", e);
            return false;
        }
    }
    
    @Override
    protected String getAppName() { return "demo-app"; }
    
    @Override
    protected String getLoggerName() { return "userDao"; }
    
    @Override
    protected String getAppDatasourceName() { return "default-ds"; }
}
```

### 2. 创建Service类

```java
public class UserService extends BaseService {
    
    private UserDao userDao = new UserDao();
    
    public EasyResult getUserList(int pageNo, int pageSize) {
        try {
            JSONObject result = userDao.getUserList(pageNo, pageSize);
            return EasyResult.ok(result);
            
        } catch (Exception e) {
            CoreLogger.getPlatform().error("查询用户列表失败", e);
            return EasyResult.error("查询失败");
        }
    }
    
    public EasyResult createUser(JSONObject userInfo) {
        // 参数验证
        if (StringUtils.isBlank(userInfo.getString("userName"))) {
            return EasyResult.error("用户名不能为空");
        }
        
        // 业务逻辑
        boolean success = userDao.createUser(userInfo);
        
        if (success) {
            // 记录日志
            OperatorLog log = new OperatorLog(1, "创建用户")
                .setParams(userInfo);
            log.save();
            
            return EasyResult.ok("用户创建成功");
        } else {
            return EasyResult.error("用户创建失败");
        }
    }
    
    @Override
    protected String getAppName() { return "demo-app"; }
    
    @Override
    protected String getLoggerName() { return "userService"; }
    
    @Override
    protected String getAppDatasourceName() { return "default-ds"; }
}
```

### 3. 使用Service的Servlet

```java
public class UserManageServlet extends EasyBaseServlet {
    
    private UserService userService = new UserService();
    
    // 查询用户列表
    public void getUserList() {
        JSONObject params = getJSONObject();
        int pageNo = params.getIntValue("pageNo");
        int pageSize = params.getIntValue("pageSize");
        
        EasyResult result = userService.getUserList(pageNo, pageSize);
        render(result);
    }
    
    // 创建用户
    public void createUser() {
        JSONObject userInfo = getJSONObject("user");
        
        EasyResult result = userService.createUser(userInfo);
        render(result);
    }
    
    @Override
    protected String getAppName() { return "demo-app"; }
    
    @Override
    protected String getLoggerName() { return "userManageServlet"; }
    
    @Override
    protected String getAppDatasourceName() { return "default-ds"; }
    
    @Override
    protected String getResId() { return "user.manage"; }
}
```

## 常见问题解决

### 1. 数据库连接问题
```java
// 检查数据源是否正确配置
try {
    EasyQuery query = EasyQuery.getQuery("default-ds");
    query.queryForInt("SELECT 1");
    System.out.println("数据库连接正常");
} catch (Exception e) {
    System.err.println("数据库连接失败: " + e.getMessage());
}
```

### 2. 缓存连接问题
```java
// 检查缓存是否正常
try {
    EasyCache cache = CacheManager.getCache();
    cache.put("test", "value", 60);
    String value = cache.get("test");
    System.out.println("缓存连接正常: " + value);
} catch (Exception e) {
    System.err.println("缓存连接失败: " + e.getMessage());
}
```

### 3. JSON解析问题
```java
// 安全的JSON解析
try {
    JSONObject params = getJSONObject();
    if (params == null || params.isEmpty()) {
        render(EasyResult.error("请求参数不能为空"));
        return;
    }
    // 处理参数...
} catch (Exception e) {
    error("JSON解析失败", e);
    render(EasyResult.error("请求参数格式错误"));
}
```

## 下一步学习

1. 阅读完整的[开发者使用手册](./开发者使用手册.md)
2. 学习高级功能：权限控制、加密解密、性能优化
3. 了解部署和运维相关知识
4. 参考项目中的示例代码

## 技术支持

如果遇到问题，可以：
1. 查看日志文件排查错误
2. 参考完整的API文档
3. 联系技术支持团队
