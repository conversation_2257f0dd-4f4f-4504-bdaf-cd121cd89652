package org.easitline.common.annotation;

import java.lang.annotation.Annotation;
import java.lang.reflect.*;
import java.util.HashMap;
import java.util.Map;


/**
 * Annotation 解析工具类,提供获取类对应的类、方法、字段的注释
 * <AUTHOR>
 *
 */
public class AnnotationUtil {
	
	/**
	 * 获得类注释
	 * @param clazz
	 * @return
	 */
	public static Annotation[]  parseType(Class clazz) throws Exception{
		Annotation[] annos = clazz.getAnnotations();
		return annos;
	}
	
	/**
	 * 获得字段注释
	 * @param clazz
	 * @return
	 */
	public static Map<String,Annotation[]> parseFiled(Class clazz){
		//clazz.getFields()
		Map<String,Annotation[]>  annonsMap = new HashMap<String,Annotation[]>();
		/**
		 * clazz.getDeclaredFields()  遍历所有的字段
		 */
		for (Field field : clazz.getDeclaredFields()) {
			//获得字段对应的注释
			Annotation[] annos = field.getAnnotations();
			if(annos != null){
				annonsMap.put(field.getName(), annos);
			}
		}
		return annonsMap;
	}
	
	public static Map<String,Annotation[]> parsePrimaryKey(Class clazz){
		//clazz.getFields()
		Map<String,Annotation[]>  annonsMap = new HashMap<String,Annotation[]>();
		/**
		 * clazz.getDeclaredFields()  遍历所有的字段
		 */
		for (Field field : clazz.getDeclaredFields()) {
			//获得字段对应的注释
			Annotation[] annos = field.getAnnotations();
			if(annos != null){
				annonsMap.put(field.getName(), annos);
			}
		}
		return annonsMap;
	}
	
	
	
	
	/**
	 * 获得所有方法注释
	 * @param clazz
	 * @return
	 */
	public static HashMap<String,Annotation[]> parseMethod(Class clazz,Class annotationClass){
		//clazz.getFields()
		HashMap<String,Annotation[]>  annonsMap = new HashMap<String,Annotation[]>();
		/**
		 * clazz.getDeclaredFields()  遍历所有的字段
		 */
		for (Method method : clazz.getDeclaredMethods()) {
			//获得字段对应的注释
			Annotation[] annos = method.getAnnotations();
					//method.getAnnotationsByType(annotationClass);
			if(annos != null){
				annonsMap.put(method.getName(), annos);
			}
		}
		return annonsMap;
	}
}
