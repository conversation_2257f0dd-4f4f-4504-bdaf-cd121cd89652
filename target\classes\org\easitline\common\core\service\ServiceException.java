package org.easitline.common.core.service;

public class ServiceException extends Exception {

	/**
	 * 
	 */
	private String msg;
	
	private int errorCode;
	
	private static final long serialVersionUID = 1L;

	public ServiceException(int errorCode ,String msg){
		this.msg = msg;
		this.errorCode = errorCode;
	}

	@Override
	public String getMessage() {
		return msg;
	}
	
	public int getErrorCode(){
		return this.errorCode;
	}
}
