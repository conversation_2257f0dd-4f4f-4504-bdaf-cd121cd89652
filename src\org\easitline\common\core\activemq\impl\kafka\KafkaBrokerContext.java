package org.easitline.common.core.activemq.impl.kafka;

import java.io.Serializable;

import java.util.Arrays;
import java.util.Properties;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageConsumer;

import javax.jms.MessageProducer;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.easitline.common.core.activemq.Broker;
import org.easitline.common.core.activemq.BrokerContext;
import org.easitline.common.core.activemq.impl.ObjectMessageImpl;
import org.easitline.common.core.activemq.impl.proxy.ConsumerProxy;
import org.easitline.common.core.activemq.impl.proxy.ProducerProxy;
import org.easitline.common.core.activemq.log.ActivieMQLogger;
import org.easitline.common.core.context.ServerContext;

/**
 * 消息队列上下文信息
 * <AUTHOR>
 */
public class KafkaBrokerContext extends BrokerContext{
	
	private static int brokerIndex = 0 ;
	
	/**
	 * 消息的生产者
	 */
	private MessageProducer producer;

	/**
	 * 消息的消费者
	 */
	private MessageConsumer consumer;
	
	
	private KafkaConsumer<String, String> kafkaConsumer;
	
	private  KafkaProducer<String, String> kafkaProducer;
	
	
	public synchronized String getTopicBrokerName(String brokerName){
		brokerIndex++;
		return brokerName + "-" +ServerContext.getServerName()+"-"+brokerIndex;
	}
	

	public KafkaBrokerContext(Integer connectorType, String connector, String brokerName, String username,String password) {
		super(connectorType, connector, brokerName, username, password);
	}

	@Override
	public void init() {
		try {
			if(this.getConnectorType() == Broker.CONNECTOR_TYPE_CONSUMER) {
				//如果组名称相同，则是队列模式，如果不相同则topic模式。
				if(this.kafkaConsumer == null)  this.initConsumer(this.brokerName);
			}
			
			if(this.getConnectorType() == Broker.CONNECTOR_TYPE_PRODUCER) {
				if(this.kafkaProducer == null) this.initProducer();
			}
			
			if(this.getConnectorType() == Broker.CONNECTOR_TYPE_TOPIC_CONSUMER) {
				if(this.kafkaConsumer == null)  this.initConsumer(getTopicBrokerName(this.brokerName));
			}
			
			if(this.getConnectorType() == Broker.CONNECTOR_TYPE_TOPIC_PRODUCER) {
				if(this.kafkaProducer == null) this.initProducer();
			}
		} catch (Exception ex) {
			ActivieMQLogger.getLogger().error(ex,ex);
		}
	}
	
	
	/**
	 * 初始化消费者
	 * @param groupName   消费组名称，如果是一样的，则是queue模式，不一样则topic模式。
	 * @throws Exception
	 */
	private void initConsumer(String groupName) throws Exception{
		
		Properties properties = new Properties();
		// kafka的地址
		properties.put("bootstrap.servers", this.getConnector());
		// 组名。
		properties.put("group.id", groupName);
		// 是否自动提交，默认为true。
		properties.put("enable.auto.commit", "true");
		// 从poll(拉)的回话处理时长。
		properties.put("auto.commit.interval.ms", "5000");
		properties.put("auto.offset.reset", "latest");
		// 超时时间
		properties.put("session.timeout.ms", "30000");
		// 键序列化，默认org.apache.kafka.common.serialization.StringDeserializer。
        properties.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        properties.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);

		
		ActivieMQLogger.getLogger().info("kafkaConsumer->"+properties);

        kafkaConsumer = new KafkaConsumer<>(properties);
        //设置的是自动提交，需要先订阅一个topic，也就是指定消费哪一个topic。
        kafkaConsumer.subscribe(Arrays.asList(this.brokerName));

        KafkaMessageConsumer _consumer = new KafkaMessageConsumer(this,kafkaConsumer);
        
        if(this.consumer == null){
        	this.consumer = new ConsumerProxy(this,_consumer);
        }else{
        	((ConsumerProxy)this.consumer).setConsumer(_consumer);
        }
		
	}
	
	private void initProducer()throws Exception{
		
		Properties props = new Properties();
		// bootstrap.servers:kafka的地址
		props.put("bootstrap.servers", this.getConnector());
		/*
		 * acks=0：如果设置为0，生产者不会等待kafka的响应。
		 * acks=1：这个配置意味着kafka会把这条消息写到本地日志文件中，但是不会等待集群中其他机器的成功响应。
		 * acks=all：这个配置意味着leader会等待所有的follower同步完成。这个确保消息不会丢失，除非kafka集群中所有机器挂掉。
		 * 这是最强的可用性保证
		 */
		props.put("acks", "all");
		//retries：配置为大于0的值的话，客户端会在消息发送失败时重新发送
		props.put("retries", 0);
		//batch.size:当多条消息需要发送到同一个分区时，producer将试图批处理消息记录，以减少请求次数.默认的批量处理消息字节数
		props.put("batch.size", 16384);
		// 延迟1ms发送，这项设置将通过增加小的延迟来完成--即，不是立即发送一条记录，producer将会等待给定的延迟时间以允许其他消息记录发送，这些消息记录可以批量处理
		props.put("linger.ms", 1);
		// producer可以用来缓存数据的内存大小。
		props.put("buffer.memory", 33554432);
		props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
		props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);

		kafkaProducer = new KafkaProducer<String, String>(props);
		
		KafkaMessageProducer  _producer = new KafkaMessageProducer(this,kafkaProducer);
        
        if(this.producer == null){
        	this.producer = new ProducerProxy(this,_producer);
        }else{
        	((ProducerProxy)this.producer).setProducer(_producer);
        }
	}


	@Override
	public void close() {
	}

	@Override
	public MessageProducer getProducer() {
		return this.producer;
	}

	@Override
	public MessageConsumer getConsumer() {
		return this.consumer;
	}

	@Override
	public String getConnector() {
		return this.connector;
	}

	@Override
	public String getBrokerName() {
		return this.brokerName;
	}

	@Override
	public Message createObjectMessage(Serializable messageObj) throws JMSException {
		return new ObjectMessageImpl(messageObj);
	}


	@Override
	public void reload() {
		try {
			if(this.kafkaConsumer != null) this.consumer.close();
		} catch (Exception ex) {
			ActivieMQLogger.getLogger().error(ex,ex);
		}
		try {
			if(this.kafkaProducer != null) this.producer.close();
		} catch (Exception ex) {
			ActivieMQLogger.getLogger().error(ex,ex);
		}
		this.kafkaConsumer = null;
		this.kafkaProducer = null;
		this.init();
	}
	
}
