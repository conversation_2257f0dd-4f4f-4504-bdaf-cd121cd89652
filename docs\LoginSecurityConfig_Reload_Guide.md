# LoginSecurityImpl 配置重新加载功能使用指南

## 概述

`LoginSecurityImpl` 类中包含多个静态变量，这些变量在类加载时从系统配置中初始化。当配置文件（如 `EASI_CONF` 表）中的相关参数被修改后，这些静态变量不会自动更新，需要手动触发重新加载。

## 问题背景

原有的 `LoginSecurityImpl` 类中的静态变量包括：
- `verifyLogin`: 是否开启登录验证
- `maxErrorLoginCount`: 最大操作错误次数
- `unLockTime`: 间隔多少分钟解锁
- `loginTimeLimit`: 间隔多少分钟内操作判断为连续操作
- `updatePwdLimit`: 间隔多久修改一次密码
- `hisPwdCount`: 保存历史密码次数
- `firstLoginUpdatePwd`: 开启首次登录修改密码验证
- `pwdDif`: 修改密码不一致次数

这些变量只在类加载时初始化一次，即使后续修改了配置文件，也不会自动生效。

## 解决方案

新增了 `reloadConfig()` 静态方法，该方法可以：
1. 重新加载 `ServerContext` 的配置
2. 更新所有相关的静态变量
3. 记录重新加载的日志信息
4. 提供线程安全的重新加载机制

## 使用方法

### 1. 直接调用

```java
// 重新加载登录安全配置
LoginSecurityImpl.reloadConfig();
```

### 2. 在管理接口中调用

可以在系统管理界面或管理接口中提供配置重新加载功能：

```java
@RequestMapping("/admin/reloadLoginConfig")
public ResponseEntity<String> reloadLoginConfig() {
    try {
        LoginSecurityImpl.reloadConfig();
        return ResponseEntity.ok("登录安全配置重新加载成功");
    } catch (Exception e) {
        return ResponseEntity.status(500).body("配置重新加载失败: " + e.getMessage());
    }
}
```

### 3. 在配置修改后自动调用

当修改配置后，可以自动触发重新加载：

```java
// 修改配置
updateConfigInDatabase(key, value);

// 重新加载相关配置
LoginSecurityImpl.reloadConfig();
```

## 注意事项

1. **线程安全**: `reloadConfig()` 方法使用了 `synchronized` 关键字，确保在多线程环境下的安全性。

2. **异常处理**: 方法内部包含异常处理，重新加载失败时会记录错误日志。

3. **日志记录**: 成功重新加载后会记录详细的配置信息到日志中，便于监控和调试。

4. **配置生效**: 调用此方法后，新的配置值会立即生效，无需重启应用。

## 配置项说明

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| verifyLogin | false | 是否开启登录验证 |
| maxErrorLoginCount | 6 | 最大操作错误次数 |
| unLockTime | 10 | 间隔多少分钟解锁 |
| loginTimeLimit | 10 | 间隔多少分钟内操作判断为连续操作 |
| updatePwdLimit | 0 | 间隔多久修改一次密码（天） |
| hisPwdCount | 4 | 保存历史密码次数 |
| firstLoginUpdatePwd | false | 开启首次登录修改密码验证 |
| pwdDif | 0 | 修改密码不一致次数 |

## 示例代码

参考 `ConfigReloadExample.java` 文件中的示例代码。

## 最佳实践

1. 在系统管理界面提供"重新加载配置"按钮
2. 在配置修改接口中自动调用重新加载方法
3. 定期检查日志，确保配置重新加载正常
4. 在生产环境中谨慎使用，建议在维护窗口期间操作
