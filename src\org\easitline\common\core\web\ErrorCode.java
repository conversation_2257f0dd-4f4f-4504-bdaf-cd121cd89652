package org.easitline.common.core.web;

/**
 * 系统错误枚举定义
 * 
 * 500：系统错误，  403决绝访问   404：找不到对象  999：其他错误
 * 511:输入有误 ， 512:AppKey没有注册
 * 513:方法不存在  514:签名有误
 * 
 * <AUTHOR>
 *
 */
public enum ErrorCode {
	
	System_error(500), Forbidden_error(403), Notfound_error(404), Other_error(999),
	INPUT_VALIDATION_ERROR(511), API_ROUTER_APP_KEY_NO_REGISTER(512),
	API_ROUTER_METHOD_NO_EXISTENCE(513), API_ROUTER_SIGN_ERROR(514),
	API_ROUTER_ACCESS_TOKEN_NO_EXISTENCE(515), API_ROUTER_ACCESS_TOKEN_HAVE_LOGOUT(516),
	API_ROUTER_ACCESS_TOKEN_OVERTIME(517), API_ROUTER_TIMSTAMP_OVER_TIME(518),
	API_ROUTER_SYSTEM_IS_UPDATING(519);
	
	private int code;

    // 构造方法
    private ErrorCode(int code) {
       this.code = code;
    }
    
    public int getCode(){
    	return this.code;
    }

}
