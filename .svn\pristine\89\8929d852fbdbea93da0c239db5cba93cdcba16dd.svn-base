package org.easitline.common.core.cache.impl;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.util.ArrayList;
import java.util.List;

import org.easitline.common.core.cache.CacheTime;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.impl.CoreLogger;
import org.easitline.common.utils.string.StringUtils;

import net.spy.memcached.MemcachedClient;

/**
 * Memcache的实现
 *
 */
public class MemcacheImpl  implements EasyCache {
	
	private MemcacheImpl(){
		init();
	}
	private static class Holder{
		private static MemcacheImpl memcache = new MemcacheImpl();
	}
	
	public static MemcacheImpl getInstance(){
		return Holder.memcache;
	}
	
	private  MemcachedClient client = null;
	
	@Override
	public void init(){
		String addrInfo = ServerContext.getCacheAddr();
		CoreLogger.getPlatform().info("init memcache["+addrInfo+"]...");
		if(StringUtils.isNotBlank(addrInfo)){
			String[] addrs = addrInfo.split(",");
			List<InetSocketAddress> addrList = new ArrayList<InetSocketAddress>();
			for(String addr:addrs){
				String[] arr = addr.split(":");
				InetSocketAddress serverAddr = new InetSocketAddress(arr[0],Integer.parseInt(arr[1]));
				addrList.add(serverAddr);
			}
			try {
				client=new MemcachedClient(addrList);
			} catch (IOException e) {
				CoreLogger.getPlatform().error(e.getMessage(),e);
			}  
		}
	}
	/**
	 * 默认缓存三小时 
	 */
	@Override
	public void put(String key, Object value) {
		try {
			if(client==null)init();
			if(client!=null){
				client.set(key, CacheTime.HOUR*3, value);
			}
		} catch (Exception e) {
			CoreLogger.getPlatform().error(e.getMessage(),e);
		}
		
	}

	/**
	 * 缓存时间单位(秒)
	 * CacheTime.HOUR CacheTime.DAY...
	 */
	@Override
	public void put(String key, Object value, int second) {
		try {
			if(client==null)init();
			if(client!=null)client.set(key, second, value);
		} catch (Exception e) {
			CoreLogger.getPlatform().error(e.getMessage(),e);
		}
	}
	
	@SuppressWarnings("unchecked")
	public <T> T get(String key) {
		try {
			if(client==null)init();
			if(client==null)return null;
			Object object=client.get(key);
			if(object==null)return null;
			return (T)object;
		} catch (Exception e) {
			CoreLogger.getPlatform().error(e.getMessage(),e);
		}
		return null;
	}

	@Override
	public void flushAll() {
		client.flush();
	}

	public MemcachedClient getClient(){
		if(client==null)init();
		if(client!=null){
			return client;
		}else{
			return null;
		}
	}
	
	@Override
	public void delete(String key) {
		if(client==null)init();
		if(client==null)return;
		client.delete(key);
	}
	@Override
	public void stop() {
		CoreLogger.getPlatform().info("stop memcache...");
		if(client!=null)client.shutdown();
		client=null;
		
	}
	@Override
	public void reload() {
		stop();
		init();
	}

}
