package org.easitline.common.core.log;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.log.impl.Log4j2Impl;
import org.easitline.common.core.log.impl.Log4jImpl;

/**
 * MARS日志打印引擎
 * 
 * 例子： 
 *  org.apache.log4j.Logger logger = LogEngine.getLogger("mylog");
 *  org.apache.log4j.Logger logger = LogEngine.getLogger("testapp","mylog");
 *
 */
public class LogEngine {

	// 延迟初始化，避免循环依赖
	private static Boolean isLog4j2 = null;

	private static boolean isLog4j2() {
		if (isLog4j2 == null) {
			isLog4j2 = ServerContext.isLog4j2();
		}
		return isLog4j2;
	}
	
	public static Logger  getLogger(String logName){
		if(isLog4j2()) {
			return Log4j2Impl.getLogger(logName);
		}else {
			return Log4jImpl.getLogger(logName);
		}
	}

	public static Logger  getLogger(String appName,String logName){
		if(isLog4j2()) {
			return Log4j2Impl.getLogger(appName, logName);
		}else {
			return Log4jImpl.getLogger(appName, logName);
		}
	}

	public static void remove(String logName){
		if(isLog4j2()) {
			Log4j2Impl.remove(logName);
		}else {
			Log4jImpl.remove(logName);;
		}
	}

	public static void removeAll() {
		if(isLog4j2()) {
			Log4j2Impl.removeAll();
		}else {
			Log4jImpl.removeAll();
		}
	}


}
