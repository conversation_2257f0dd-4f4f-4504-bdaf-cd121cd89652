package org.easitline.common.core.activemq.impl;

import java.io.Serializable;

import javax.jms.Message;
import javax.jms.MessageListener;
import javax.jms.ObjectMessage;

import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.easitline.common.core.activemq.BrokerContext;
import org.easitline.common.core.activemq.BrokerMessageListener;
import org.easitline.common.core.activemq.log.ActivieMQLogger;

/**
 * 消息监听代理
 * 
 * <AUTHOR>
 * 
 */
public class MessageListenerProxy implements MessageListener {

	private BrokerMessageListener objectMessageListener;

	private BrokerContext context;

	public MessageListenerProxy(BrokerMessageListener objectMessageListener,BrokerContext context) {
		this.objectMessageListener = objectMessageListener;
		this.context = context;
	}

	/**
	 * 消息处理
	 */
	public void onMessage(Message message) {

		if (!(message instanceof ObjectMessage)) {
			return;
		}

		try {
			Serializable s = ((ObjectMessage) message).getObject();
			if(ActivieMQLogger.getLogger().isDebugEnabled()){
				ActivieMQLogger.getLogger().debug("<recv> broker("+ context.getBrokerName()+ ")->"+ ReflectionToStringBuilder.toString(s,ToStringStyle.SHORT_PREFIX_STYLE));
			}
			objectMessageListener.onMessage(s);

		} catch (Exception me) {
			ActivieMQLogger.getLogger().error(" MessageListener.onMessage(<" + context.getBrokerName()+ "," + objectMessageListener+ ">) handle error , cause:" + me.getMessage(), me);
		}
	}
}
