package org.easitline.common.core.framework.vo;

import java.sql.SQLException;
import java.util.List;

import org.easitline.common.core.context.ServerContext;
import org.easitline.common.core.framework.vo.rowmapper.UserRowMapper;
import org.easitline.common.core.log.impl.CoreLogger;

/**
 * 标签模型
 * <AUTHOR>
 *
 */

public class Label {
	private String labelId;
	private String labelName;
	
	public Label(){
	}
	
	/**
	 * 获得标签用户
	 * @return List<User>  标签的用户列表
	 */
	public List<User> getUsers(){
		String sql = "SELECT * FROM V_EASI_LABEL_USER  where  STATE <> 9 AND LABEL_ID = ?";
    	try {
			return ServerContext.getAdminQuery().queryForList(sql,new Object[]{this.getLabelId()},new UserRowMapper());
		} catch (SQLException ex) {
			CoreLogger.getLogger().error("Core-framework->获取标签用户信息失败，原因："+ex.getMessage(),ex);
		}
    	return null;
	}
	
	/**
	 * 获得标签ID
	 * @return
	 */
	public String getLabelId() {
		return labelId;
	}
	
	public void setLabelId(String labelId) {
		this.labelId = labelId;
	}
	/**
	 * 获得标签名称
	 * @return
	 */
	public String getLabelName() {
		return labelName;
	}
	public void setLabelName(String labelName) {
		this.labelName = labelName;
	}
	
}
