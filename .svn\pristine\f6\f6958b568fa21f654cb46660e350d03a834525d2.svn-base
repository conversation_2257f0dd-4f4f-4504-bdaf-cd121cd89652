log4j.rootLogger=DEBUG,console

log4j.appender.console=org.apache.log4j.ConsoleAppender
log4j.appender.console.layout=org.apache.log4j.PatternLayout
log4j.appender.console.layout.ConversionPattern={"timeStamp":"%d{yyyy-MM-dd HH:mm:ss}","content":"%m","level":"%p","thread":"%t"}%n

log4j.appender.file=org.apache.log4j.RollingFileAppender
log4j.appender.file.File=logs/${project.name}.log
log4j.appender.file.layout=org.apache.log4j.PatternLayout
log4j.appender.file.layout.ConversionPattern={"timeStamp":"%d{yyyy-MM-dd HH:mm:ss}","content":"%m","level":"%p","thread":"%t"}%n
log4j.appender.console.MaxFileSize=150MB
log4j.appender.console.MaxBackupIndex=8
log4j.logger.org.quartz=INFO
log4j.category.org.springframework=INFO
